package com.jdx.rover.monitor.manager.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.ErrorInfoDO;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/16 23:57
 * @description monitor-web动态配置信息
 */
@Component
@Data
public class DuccMonitorProperties {

    /**
     * DUCC动态配置：车牌号
     */
    @LafValue(key = "monitor.web.map.vehicleNameList")
    private List<String> vehicleNameList;

    /**
     * DUCC动态配置：是否下发采图模式切换指令
     */
    @LafValue(key = "monitor.web.map.sendMapCollectionCommand")
    private Boolean sendMapCollectionCommand;

    /**
     * DUCC动态配置：切换采图模式超时时间，单位S
     */
    @LafValue(key = "monitor.web.map.switchModeTimeout")
    private Integer switchModeTimeout;

    /**
     * DUCC动态配置：车辆是否处于运动状态最小速度，单位m/s
     */
    @LafValue(key = "monitor.web.map.minDrivingSpeed")
    private Double minDrivingSpeed;

    /**
     * DUCC动态配置：车辆每秒最大行驶距离，单位m
     */
    @LafValue(key = "monitor.web.map.maxDrivingDistance")
    private Integer maxDrivingDistance;

    /**
     * DUCC动态配置：是否开启xata数据拷贝
     */
    @LafValue(key = "monitor.web.map.openXataDataCopy")
    private Boolean openXataDataCopy;

    /**
     * DUCC动态配置：是否开启xata数据拷贝
     */
    @LafValue(key = "monitor.web.map.openXataQueryStorage")
    private Boolean openXataQueryStorage;

    /**
     * DUCC动态配置：xata数据拷贝topic列表
     */
    @LafValue(key = "monitor.web.map.xataDataCopyTopicList")
    private List<String> xataDataCopyTopicList;

    /**
     * 地图采集任务定时上传任务间隔，单位：分钟
     */
    @LafValue(key = "monitor.web.map.mapTaskAutoUploadTimeInterval")
    private Integer mapTaskAutoUploadTimeInterval;

    /**
     * DUCC动态配置：采图模式错误码
     */
    private Map<String, String> mapCollectionErrorMap = new HashMap<>();

    @LafValue("monitor.web.map.error")
    public void errorChange(String errorKey) {
        List<ErrorInfoDO> errorInfoDOS = JsonUtils.readValue(errorKey, new TypeReference<>() {
        });
        for (ErrorInfoDO errorInfoDO : errorInfoDOS) {
            mapCollectionErrorMap.put(errorInfoDO.getErrorCode(), errorInfoDO.getErrorName());
        }
    }
}