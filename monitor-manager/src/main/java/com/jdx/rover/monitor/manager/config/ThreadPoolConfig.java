package com.jdx.rover.monitor.manager.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * ThreadPool配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean("asyncThreadPool")
    public Executor asyncThreadPool() {

        //cpu数
        int cpuNum = getCpuNum();
        log.info("获取的cpu数为:{}",cpuNum);
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(cpuNum * 2);
        //最大线程数
        executor.setMaxPoolSize(cpuNum * 4);
        //队列容量
        executor.setQueueCapacity(500);
        //设置等待任务在关机时完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //设置线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞住
        executor.setAwaitTerminationSeconds(60);
        //线程名字前缀
        executor.setThreadNamePrefix("async-monitor-");
        //设置当poolSize已达到maxPoolSize，由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 获取cpu核心数
     * @return
     */
    private int getCpuNum(){
        try {
            int cpuNum = Runtime.getRuntime().availableProcessors();
            if (cpuNum > 0){
                return cpuNum ;
            }
            return 4;
        }catch (Exception ex){
            return 4;
        }
    }
}
