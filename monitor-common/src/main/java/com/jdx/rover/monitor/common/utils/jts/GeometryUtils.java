package com.jdx.rover.monitor.common.utils.jts;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.List;

/**
 * 几何工具类util
 *
 * <AUTHOR>
 * @date 2024/01/11
 */
public class GeometryUtils {
    /**
     * 多个点构建多边形
     */
    public static Geometry createPolygon(List<Coordinate> coordinates) {
        return GeometryFactoryUtils.getInstance().createPolygon(coordinates.toArray(new Coordinate[]{}));
    }

    /**
     * 构建点
     */
    public static Point createPoint(double x, double y) {
        Coordinate coordinate = new Coordinate(x, y);
        return GeometryFactoryUtils.getInstance().createPoint(coordinate);
    }

    /**
     * 构建路线
     */
    public static LineString createLineString(List<Coordinate> coordinates) {
        return GeometryFactoryUtils.getInstance().createLineString(coordinates.toArray(new Coordinate[0]));
    }

    /**
     * 构建矩形
     *
     * @param x1 最小x经度
     * @param y1 最小y纬度
     * @param x2 最大x经度
     * @param y2 最大y纬度
     */
    public static Polygon createEnvelopePolygon(double x1, double y1, double x2, double y2) {
        Coordinate[] shapeEnvelopes = new Coordinate[5];
        shapeEnvelopes[0] = new Coordinate(x1, y1);
        shapeEnvelopes[1] = new Coordinate(x1, y2);
        shapeEnvelopes[2] = new Coordinate(x2, y2);
        shapeEnvelopes[3] = new Coordinate(x2, y1);
        shapeEnvelopes[4] = new Coordinate(x1, y1);
        return GeometryFactoryUtils.getInstance().createPolygon(shapeEnvelopes);
    }
}
