/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/12 02:25
 * @description 车端查询是否存在限速
 */
@Data
public class CheckSpeedLimitDTO {

    /**
     * 是否有配置，默认false
     */
    private Boolean hasSpeedLimit = false;

    /**
     * 限速值（仅当hasSpeedLimit为true时赋值）
     */
    private Double maxVelocity;
}
