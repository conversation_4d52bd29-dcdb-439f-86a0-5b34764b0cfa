package com.jdx.rover.monitor.bo.websocket;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Websocket客户端业务对象
 *
 * <AUTHOR>
 */
@Data
public class WebsocketClientBO {
  /**
   * Session ID
   */
  private String sessionId;

  /**
   * 用户名称
   */
  private String username;

  /**
   * 产品
   */
  private String productKey;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 地图页车辆名称
   */
  private List<String> mapVehicle;

  /**
   * 事件类型
   */
  private String eventType;

  /**
   * 事件主题名称
   */
  private String eventTopicName;

  /**
   * 事件监听器ID
   */
  private Integer eventListenerId;

  /**
   * 告警主题名称
   */
  private String alarmTopicName;

  /**
   * 告警监听器ID
   */
  private Integer alarmListenerId;

  /**
   * 工单监听器ID
   */
  private Integer issueListenerId;

  /**
   * 车辆位置监听器ID
   */
  private Integer positionListenerId;

  /**
   * 地图页监听器ID
   */
  private Map<String, Integer> mapListenerIds;

  /**
   * 单车页右下角tab类型
   */
  private String singleVehicleTabType;

  /**
   * web terminal name
   */
  private String terminalName;

  /**
   * web terminal id
   */
  private Long webTerminalId;
  /**
   * 会话连接时间
   */
  private Long wsConnectionTime;

  /**
   * 车辆位姿监听器ID
   */
  private Long poseListenerId;

  /**
   * 任务监听器ID
   */
  private Integer scheduleListenerId;
}
