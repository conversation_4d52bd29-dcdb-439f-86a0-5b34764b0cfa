package com.jdx.rover.monitor.vo.cockpit;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * @description: 座席绑定/解绑车辆VO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-06-12 15:35
 **/
@Data
public class BindVehicleVO {

    /**
     * 座席编号
     */
    @NotBlank(message = "座席编号不能为空")
    private String cockpitNumber;

    /**
     * 车辆列表
     */
    @NotNull(message = "车辆列表不能为空")
    private List<String> vehicleNameList;
}