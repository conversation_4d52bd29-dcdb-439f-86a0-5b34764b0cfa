/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.mqtt;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 档位
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MqttTopicEnum {
    COCKPIT_TEAM_GROUP("r/%s/drive/cockpitTeamInfo/group/", "prefix", "共享订阅全局驾驶团队信息"),
    USER_INFO("r/%s/drive/userInfo/", "prefix", "用户信息"),
    DRAWER_INFO("r/%s/drive/drawer/", "prefix", "抽屉信息"),
    PDU_COMMAND( "r/pdu/command/", "prefix", "PDU指令"),
    CONTROL_CONNECT_SERVER("r/drive/control/connect/server/", "prefix", "接管车辆握手指令"),
    REPLY_CONTROL_CONNECT_SERVER("reply/r/drive/control/connect/server/", "prefix", "接管车辆握手指令响应"),
    SERVER_VEHICLE("r/drive/server/vehicle/", "prefix", "服务端发送车端"),
    ROBOT_CONTROL_COMMAND( "t/%s/%s/%s/%s/pb/services", "all", "机器人遥控指令"),
    ;
    /**
     * 类型
     */
    private final String topic;
    /**
     * 类型
     */
    private final String type;
    /**
     * 标题描述
     */
    private final String title;
}
