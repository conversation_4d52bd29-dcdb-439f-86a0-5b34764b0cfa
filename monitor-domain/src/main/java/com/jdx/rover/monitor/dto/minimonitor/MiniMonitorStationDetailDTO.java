/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.minimonitor;

import lombok.Data;

/**
 * <p>
 * This is a station data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorStationDetailDTO {

  /**
   * <p>
   * Represents the station id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the station user name. The default value is null. It's changeable.
   * </p>
   */
  private String stationUser;

  /**
   * <p>
   * Represents the attention flag. The default value is null. It's changeable.
   * </p>
   */
  private String userAttentionState;

}
