/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 重置车辆定位位姿
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorResetVehiclePoseCommandVO extends MonitorRemoteCommandVO{

  /**
   * <p>
   * 指令id
   * </p>
   */
  @NotNull
  @Min(0)
  private Long id;

  /**
   * <p>
   * x轴
   * </p>
   */
  @NotNull
  private Double x;

  /**
   * <p>
   * y轴
   * </p>
   */
  @NotNull
  private Double y;

  /**
   * <p>
   * z轴
   * </p>
   */
  @NotNull
  private Double z;

  /**
   * <p>
   * row角
   * </p>
   */
  @NotNull
  private Double roll;

  /**
   * <p>
   * yaw轴
   * </p>
   */
  @NotNull
  private Double yaw;

  /**
   * <p>
   * pitch角
   * </p>
   */
  @NotNull
  private Double pitch;

}
