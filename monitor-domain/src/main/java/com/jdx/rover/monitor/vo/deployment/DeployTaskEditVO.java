/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.deployment;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:18
 * @description 编辑勘查任务线路
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeployTaskEditVO extends DeployTaskCreateVO {

    /**
     * 勘查任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
}
