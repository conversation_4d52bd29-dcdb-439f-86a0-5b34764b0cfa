/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 人工告警记录
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ManualAlarmRecord extends BaseDomain {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 座席编号(巡查提报有座席编号)
   * </p>
   */
  private String cockpitNumber;

  /**
   * <p>
   * 提报来源
   * </p>
   */
  private String source;

  /**
   * <p>
   * 提报用户
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * 提报时间
   * </p>
   */
  private Date reportTime;

  /**
   * <p>
   * 描述
   * </p>
   */
  private String description;

}