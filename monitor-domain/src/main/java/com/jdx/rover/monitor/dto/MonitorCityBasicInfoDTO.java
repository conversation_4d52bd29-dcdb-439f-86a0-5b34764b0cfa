/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.Map;

import lombok.Data;

/**
 * <p>
 * This is a city basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorCityBasicInfoDTO {

   /**
   * <p>
   * Represents the city's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the city's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the station list. The default value is null. It's changeable.
   * </p>
   */
  private Map<Integer, MonitorStationBasicInfoDTO> station;
}
