package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 定责事故等级枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
@Deprecated
public enum SafetyGroupAccidentTypeEnum {


    INITIATIVE_CRASH("INITIATIVE_CRASH","主动碰撞"),
    PASSIVE_CRASH("PASSIVE_CRASH","被动碰撞"),
    BRAKES_REAR_END("BRAKES_REAR_END","急刹追尾"),
    SLOW_REAR_END("SLOW_REAR_END", "缓刹追尾"),
    MULTI_CRASH("MULTI_CRASH","多方碰撞");
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (SafetyGroupAccidentTypeEnum safetyGroupAccidentTypeEnum : SafetyGroupAccidentTypeEnum.values()) {
            if (safetyGroupAccidentTypeEnum.getValue().equals(value)) {
                return safetyGroupAccidentTypeEnum.getName();
            }
        }
        return null;
    }
}
