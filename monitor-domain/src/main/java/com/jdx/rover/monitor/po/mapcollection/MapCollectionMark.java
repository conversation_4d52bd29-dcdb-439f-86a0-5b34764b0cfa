/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.mapcollection;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.domain.jsonhandler.AttachmentListTypeHandler;
import com.jdx.rover.monitor.po.mapcollection.json.Attachment;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:13
 * @description 勘查标记表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "map_collection_mark", autoResultMap = true)
public class MapCollectionMark extends BaseModel {

    /**
     * 纬度（厘米）WGS84
     */
    private Double latitude;

    /**
     * 经度（厘米）WGS84
     */
    private Double longitude;

    /**
     * 标记类型
     * @see com.jdx.rover.monitor.enums.mapcollection.MarkTypeEnum
     */
    private String markType;

    /**
     * 标记地址名称
     */
    private String addressName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    @TableField(typeHandler = AttachmentListTypeHandler.class)
    private List<Attachment> attachmentList;
}
