/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 监控单车页调度停靠点信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleScheduleStopDTO {

  /**
   * 停靠点id
   */
  private Integer id;

  /**
   * 停靠目标点id
   */
  private Integer goalId;

  /**
   * 停靠点名称
   */
  private String name;

  /**
   * 停靠点类型
   */
  private String type;

  /**
   * 停靠点动作
   */
  private String stopAction;

  /**
   * 停靠状态
   */
  private String travelStatus;

  /**
   * 到达时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date arrivedTime;

  /**
   * 开始前往时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * 离开时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date departTime;

  /**
   * 预计离开时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date estDepartTime;

  /**
   * 等待时间
   */
  private Integer waitingTime;

  /**
   * 分段总里程
   */
  private Double globalMileage;
}
