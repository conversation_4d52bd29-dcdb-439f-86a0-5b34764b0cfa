/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import lombok.Data;

/**
 * <p>
 * This is a view object for mini monitor vehicle response.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorVehicleDTO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the lat of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the takeOverUsername of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String takeOverUsername;

  /**
   * <p>
   * Represents the takeOver source of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String takeOverSource;

  /**
   * <p>
   * Represents the state of schedule. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents dada work state. The default value is null. It's changeable.
   * </p>
   */
  private Boolean dadaWorkState;

  /**
   * <p>
   * Represents the task type of schedule. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * Represents the left order number of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Integer leftOrderNum;

  /**
   * <p>
   * Represents the status of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private MiniMonitorVehicleStatusDTO status;

  /**
   * <p>
   * Represents the business type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleBusinessType;
}
