/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.cockpit;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

/**
 * 坐席车辆对象
 *
 * <AUTHOR>
 */
@Data
@ToString
public class CockpitSingleVehicleVO {
    /**
     * 坐席编号
     */
    @NotBlank
    private String cockpitNumber;

    /**
     * 车辆名称
     */
    @NotBlank
    private String vehicleName;
}
