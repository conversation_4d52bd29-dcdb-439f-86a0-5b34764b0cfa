/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:43
 * @description 地图初始点位类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum PointTypeEnum {
    STATION("STATION", "站点"),
    STOP("STOP", "停靠点"),
    UNKNOWN("UNKNOWN", "未知")
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 根据pointType获取枚举
     *
     * @param pointType pointType
     * @return PointTypeEnum
     */
    public static PointTypeEnum of(String pointType) {
        for (PointTypeEnum em : PointTypeEnum.values()) {
            if (em.getCode().equals(pointType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
