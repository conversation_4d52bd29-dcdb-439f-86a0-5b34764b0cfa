/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is an tracking event.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ShadowTrackingEventVO {

  /**
   * <p>
   * Represents the operation type.
   * </p>
   */
  private String operationType = "ADD";

  /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * 产品
   * </p>
   */
  private String productKey;

  /**
   * <p>
   * 设备
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * Represents the occur time of event.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportTime;

  /**
   * <p>
   * Represents the identity of event.
   * </p>
   */
  @NotBlank
  private String eventNo;

  /**
   * <p>
   * Represents the event type.
   * </p>
   */
  private String eventType;

  /**
   * <p>
   * Represents the trace id.
   * </p>
   */
  private String traceId;

  /**
   * <p>
   * Represents the parent trace id.
   * </p>
   */
  private String parentTraceId;

  /**
   * <p>
   * Represents the event body.
   * </p>
   */
  private String eventBody;

}
