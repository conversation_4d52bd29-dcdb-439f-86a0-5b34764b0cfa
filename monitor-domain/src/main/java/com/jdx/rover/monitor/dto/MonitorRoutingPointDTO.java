/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a monitor routing point data transform object entity..
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorRoutingPointDTO {

  /**
   * <p>
   * Represents the routing point's lat. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the routing point's lon. The default value is 0.0. It's changeable.
   * </p>
   */
  private Double lon;

}
