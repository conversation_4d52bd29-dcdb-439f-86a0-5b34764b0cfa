/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:43
 * @description 线路颜色类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum RouteColorEnum {
    BLUE("BLUE", "蓝色"),
    YELLOW("YELLOW", "黄色"),
    GREEN("GREEN", "绿色"),
    UNKNOWN("UNKNOWN", "未知")
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 根据routeColor获取枚举
     *
     * @param routeColor routeColor
     * @return RouteColorEnum
     */
    public static RouteColorEnum of(String routeColor) {
        for (RouteColorEnum em : RouteColorEnum.values()) {
            if (em.getCode().equals(routeColor)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
