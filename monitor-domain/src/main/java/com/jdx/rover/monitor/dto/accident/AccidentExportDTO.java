package com.jdx.rover.monitor.dto.accident;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AccidentExportDTO {

    /**
     * 事故编号
     */
    @ExcelProperty("事故编号")
    private String accidentNo;

    /**
     * 车牌号
     */
    @ExcelProperty("车牌号")
    private String vehicleName;

    /**
     * 站点名称
     */
    @ExcelProperty("站点名称")
    private String stationName;

    /**
     * 事故提报时间
     */
    @ExcelProperty("事故发生时间")
    private Date accidentReportTime;

    /**
     * 事故来源名称
     */
    @ExcelProperty("事故来源名称")
    private String accidentSourceName;

    /**
     * 事故地址
     */
    @ExcelProperty("发生位置")
    private String accidentAddress;

    /**
     * 一线跟进人
     */
    @ExcelProperty("一线跟进人")
    private String operationUser;

    /**
     * 处理方式
     */
    @ExcelProperty("处理方式")
    private String operationHandleName;

    /**
     * 是否需要赔偿
     */
    @ExcelProperty("是否设计赔偿")
    private String operationCompensatedName;

    /**
     * 金额
     */
    @ExcelProperty("赔偿金额")
    private Double operationAmount;

    /**
     * 是否上报车网
     */
    @ExcelProperty("是否需要上报监控（车网）")
    private String operationIsReportVehicleNetName;

    /**
     * 关联缺陷
     */
    @ExcelProperty("关联缺陷")
    private String bugCode;


    /**
     * 安全组定责事故等级
     */
    @ExcelProperty("定责事故等级")
    private String safetyGroupAccidentLevelName;

    /**
     * 安全组定责事故分类
     */
    @ExcelProperty("定责事故分类")
    private String safetyGroupAccidentTypeName;

    /**
     * 安全组描述
     */
    @ExcelProperty("关键问题分析")
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    @ExcelProperty("事故模块")
    private String safetyGroupAccidentModuleName;

    /**
     * 事故标签
     */
    @ExcelProperty("事故标签")
    private String safetyGroupAccidentTagName;

    /**
     * 解决方案
     */
    @ExcelProperty("解决方案")
    private String safetyGroupAccidentSolution;

    /**
     * 解决情况
     */
    @ExcelProperty("解决情况")
    private String safetyGroupAccidentResolutionStatusName;

    /**
     * 挂起原因
     */
    @ExcelProperty("挂起原因")
    private String safetyGroupAccidentSuspendReason;
}
