/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import lombok.Data;

/**
 * <p>
 * This is a mini-monitor remote command response data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorRemoteCommandDTO {

  /**
   * <p>
   * Represents the response message of the remote command. The default value is
   * null. It's changeable.
   * </p>
   */
  private String eventType;

  /**
   * <p>
   * Represents the control user of the remote command. The default value is
   * null. It's changeable.
   * </p>
   */
  private String userName;

}
