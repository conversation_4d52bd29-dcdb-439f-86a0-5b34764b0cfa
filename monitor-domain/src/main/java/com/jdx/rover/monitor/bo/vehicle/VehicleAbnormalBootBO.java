/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.bo.vehicle;

import com.jdx.rover.monitor.po.VehicleAbnormalBoot;
import lombok.Data;

import java.util.List;

/**
 * 车辆异常启动对象
 *
 * <AUTHOR>
 */
@Data
public class VehicleAbnormalBootBO {
  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 耗时（S）
   */
  private Integer duration;

  /**
   * 异常启动模块详情
   */
  private List<VehicleAbnormalBoot> abnormalModuleBootList;

}
