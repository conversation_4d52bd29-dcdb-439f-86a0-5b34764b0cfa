/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 座席下站点车辆列表
 *
 * <AUTHOR>
 * @date 2024/06/13
 */
@Data
@NoArgsConstructor
public class CockpitStationVehicleListDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 站点编号
     */
    private String stationNumber;

    /**
     * 城市编号
     */
    private String cityNumber;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 车辆名称列表
     */
    private List<String> vehicleList;
}
