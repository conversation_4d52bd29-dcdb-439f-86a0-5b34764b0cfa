/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字段映射枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum ToggleStateEnum {
  ON("on", "开"),
  OFF("off", "关"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 名称
   */
  private final String name;

}