package com.jdx.rover.monitor.dto.user;

import lombok.Data;

/**
 * @description: 获取用户座席状态DTO
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-06-12 17:17
 **/
@Data
public class GetCockpitStatusDTO {

    /**
     * 座席团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 座席编号
     */
    private String cockpitNumber;

    /**
     * 座席类型
     */
    private String cockpitType;

    /**
     * 座席状态
     */
    private String cockpitStatus;

    /**
     * 座席模式
     */
    private String cockpitMode;
}