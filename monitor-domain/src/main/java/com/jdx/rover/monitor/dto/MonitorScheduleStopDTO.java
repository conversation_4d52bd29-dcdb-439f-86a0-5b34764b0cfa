/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a schedule stop information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopDTO {

  /**
   * <p>
   * Represent the id of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represent the goal id of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represent the type of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private String stopType;

  /**
   * <p>
   * Represent the action of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private String stopAction;

  /**
   * <p>
   * Represent the name of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private String stopName;

  /**
   * <p>
   * Represents the lat of stop. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of stop. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represent the arrived time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date arrivedTime;

  /**
   * <p>
   * Represent the depart time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date departTime;

  /**
   * <p>
   * Represent the waiting duration of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer waitingTime;

  /**
   * <p>
   * Represents stop travel status. The default value is null. It's changeable.
   * </p>
   */
  private String travelStatus;

  /**
   * <p>
   * Represent the global mileage of stop. The default value is null. It's changeable. 
   * </p>
   */
  private Double routingMileage;
}
