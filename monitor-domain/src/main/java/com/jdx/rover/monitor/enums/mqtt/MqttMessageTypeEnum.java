/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.mqtt;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 档位
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MqttMessageTypeEnum {
    SCREEN_COCKPIT_TEAM("大屏驾驶舱团队信息"),
    COCKPIT_TEAM("控制屏驾驶舱团队信息"),
    COCKPIT_INFO("驾驶舱信息"),
    USER_INFO("用户信息"),
    VEHICLE_TAKE_OVER_INFO("车辆接管信息"),
    VEHICLE_VIDEO_MODE_INFO("车辆视频模式信息"),
    DRAWER_INFO("抽屉信息"),
    SCREEN_MULTI_VEHICLE("大屏多车页"),
    SCREEN_SINGLE_VEHICLE("大屏单车页"),
    CONTROL_PAD_MULTI_VEHICLE("控制屏多车页"),
    CONTROL_PAD_SINGLE_VEHICLE("控制屏单车页"),
    CONTROL_PAD_COMMAND_RESULT("控制屏指令结果"),
    SCREEN_COMMAND_RESULT("大屏指令结果"),
    ROBOT_CONTROL_COMMAND("机器人遥控指令"),
    ;
    /**
     * 类型
     */
    private final String topic;
}
