package com.jdx.rover.monitor.enums.vehicle.single;

/**
 * <AUTHOR>
 */

import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 单车页右下角tab标签类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SingleVehicleTabTypeEnum {
  DRIVABLE("DRIVABLE", WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue(), "使能信息"),
  EXCEPTION("EXCEPTION", WebsocketEventTypeEnum.SINGLE_VEHICLE_EXCEPTION.getValue(), "异常信息"),
  OPERATION("OPERATION", WebsocketEventTypeEnum.SINGLE_VEHICLE_OPERATION.getValue(), "操作记录"),
  ACCIDENT("ACCIDENT", WebsocketEventTypeEnum.SINGLE_VEHICLE_ACCIDENT.getValue(), "事故信息"),
  ;

  private final String value;

  private final String eventType;

  private final String title;
}
