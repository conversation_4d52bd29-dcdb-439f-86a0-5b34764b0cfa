/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a impassable area data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorImpassableAreaDTO {
  /**
   * <p>
   * Represents id of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents coord type of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private Integer coordType;

  /**
   * <p>
   * Represents type name of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String coordTypeName;

  /**
   * <p>
   * Represents duration of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private Integer duration;

  /**
   * <p>
   * Represents duration name of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String durationName;

  /**
   * <p>
   * Represents coord status of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private Integer coordStatus;

  /**
   * <p>
   * Represents status name of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String coordStatusName;

  /**
   * <p>
   * Represents coords of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String coords;

  /**
   * <p>
   * Represents utm of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String utm;

  /**
   * <p>
   * Represents userName of impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents timestamp of operate impassable area. The default value is null. It's changeable.
   * </p>
   */
  private String timestamp;

  /**
   * <p>
   * Represents type of impassable area. It's changeable.
   * </p>
   */
  private Integer type;

  /**
   * <p>
   * 生效类别（永久性、周期性）
   * </p>
   */
  private Integer effectType;

  /**
   * <p>
   * 启停用状态
   * </p>
   */
  private Integer effectState;

  /**
   * <p>
   * 周期时长
   * </p>
   */
  private List<String> timeLimit;

  /**
   * <p>
   * 备注
   * </p>
   */
  private String remark;
  
}
