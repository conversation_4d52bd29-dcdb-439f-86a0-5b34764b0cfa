/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import lombok.Data;

import java.util.List;

/**
 * @description: 获取采图路线响应对象
 * @author: wang<PERSON>otai
 * @create: 2024-12-20 18:20
 **/
@Data
public class GetRouteDTO {

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 导航路径
     */
    private List<RoutePointDTO> taskRouteList;

    /**
     * 四车道列表
     */
    private List<RouteLaneDTO> fourLaneList;

    /**
     * 已行驶点位列表，WGS84
     */
    private List<List<RoutePointDTO>> finishedPointList;

    /**
     * 点位
     */
    @Data
    public static class RoutePointDTO {

        /**
         * 经度
         */
        private Double longitude;

        /**
         * 纬度
         */
        private Double latitude;
    }

    /**
     * 四车道
     */
    @Data
    public static class RouteLaneDTO {

        /**
         * 四车道起点名称
         */
        private String startAddress;

        /**
         * 四车道终点名称
         */
        private String endAddress;

        /**
         * 四车道点位对应taskRouteList下标
         */
        private List<Integer> routeIndexList;
    }
}