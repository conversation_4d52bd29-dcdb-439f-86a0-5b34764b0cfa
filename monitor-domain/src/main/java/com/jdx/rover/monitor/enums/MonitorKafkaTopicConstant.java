/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums;

/**
 * kafka消息主题
 *
 * <AUTHOR>
 */
public interface MonitorKafkaTopicConstant {

  /**
   * monitor下发远程控制指令
   */
  String MONITER_REMOTE_COMMAND_LOG = "monitor_remote_command_log";


  /**
   * 云端下发远程遥控指令
   */
  String REMOTE_CONTROL_COMMAND = "remote_control_command";

  /**
   * 调度变化
   */
  String MONITER_SCHEDULE_CHANGE = "Server-Schedule_Task";

  /**
   * 规划导航信息
   */
  String MONITER_PNC_ROUTE = "monitor_pnc_route";

  /**
   * 调度停靠点变化
   */
  String MONITER_SCHEDULE_STOP_CHANGE = "monitor_schedule_stop_change";

  /**
   * 调度订单状态变化
   */
  String MONITER_SCHEDULE_ORDER_CHANGE = "Server-Schedule_monitor_order_Task";

  /**
   * 查看订单敏感信息
   */
  String MONITER_VIEW_ORDER_DETAIL = "monitor_view_order_detail";

  /**
   * 查看点云地图
   */
  String MONITER_VIEW_CLOUD_MAP = "monitor_view_cloud_map";

  /**
   * 影子系统事件追踪
   */
  String MONITOR_SHADOW_TRACKING_EVENT = "Shadow_tracking_event";

  /**
   * 影子系统jira事件追踪
   */
  String MONITOR_SHADOW_JIRA_EVENT = "Shadow_jira_event";

  /**
   * 影子系统事件追踪
   */
  String MONITOR_SINGLE_VEHICLE_REALTIME = "monitor_single_vehicle_realtime";

  /**
   * 影子系统事件追踪
   */
  String MONITOR_SINGLE_VEHICLE_SCHEDULE = "monitor_single_vehicle_schedule";

  /**
   * 影子事件视频合成完成结果同步
   */
  String SHADOW_SUBSCRIBE_EVENT_TASK_RESULT = "shadow_subscribe_event_task_result";

  /**
   * 事故流程通知
   */
  String MONITOR_ACCIDENT_FLOW_EVENT = "monitor_accident_flow_event";
}
