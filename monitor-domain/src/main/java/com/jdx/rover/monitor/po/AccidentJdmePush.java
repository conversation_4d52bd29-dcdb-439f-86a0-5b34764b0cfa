package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 事故消息推送表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccidentJdmePush extends BaseModel {
    /**
     * 影子系统事件编号
     */
    private Integer shadowEventId;

    /**
     * 事故编号
     */
    private String accidentNo;
    /**
     * 事故群号
     */
    private String accidentGroupId;
    /**
     * 标题
     */
    private String title;
    /**
     * 车牌号
     */
    private String vehicleName;
    /**
     * 事故等级
     */
    private String level;
    /**
     * 严重程度
     */
    private String severity;
    /**
     * 缺陷ID
     */
    private Long bugId;
    /**
     * 缺陷编号
     */
    private String bugCode;
    /**
     * 发生时间
     */
    private String debugTime;
    /**
     * 发生位置
     */
    private String accidentAddress;
    /**
     * 推送状态（INIT、SUCCESS、FAIL）
     */
    private String status;
    /**
     * 事故视频地址
     */
    private String videoUrl;
    /**
     * PC端整车回放地址
     */
    private String eventPlayUrl;
    /**
     * 问题跟进人ERP
     */
    private String follower;
    /**
     * 当前处理类型
     */
    private String accidentFlowType;
}
