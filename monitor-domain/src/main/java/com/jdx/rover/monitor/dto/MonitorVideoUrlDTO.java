/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a rover video data transform object entity..
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVideoUrlDTO {

  /**
   * <p>
   * Represents the front video url.
   * </p>
   */
  private String front;
  /**
   * <p>
   * Represents the right video url.
   * </p>
   */
  private String right;
  /**
   * <p>
   * Represents the back video url.
   * </p>
   */
  private String back;

  /**
   * <p>
   * Represents the left video url.
   * </p>
   */
  private String left;

  /**
   * <p>
   * Represents the audio video url.
   * </p>
   */
  private String audio;

  /**
   * <p>
   * Represents the video push type.
   * </p>
   */
  private String pushType;

}
