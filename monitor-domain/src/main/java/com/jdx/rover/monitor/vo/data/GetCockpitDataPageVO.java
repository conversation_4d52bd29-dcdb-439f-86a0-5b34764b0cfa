package com.jdx.rover.monitor.vo.data;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @description: GetCockpitDataPageVO
 * @author: wanggu<PERSON>i
 * @create: 2024-06-20 10:48
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GetCockpitDataPageVO extends PageVO {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 坐席类型
     */
    private List<String> cockpitTypeList;

    /**
     * 坐席团队
     */
    private List<String> cockpitTeamNumberList;

    /**
     * 坐席编号
     */
    private String cockpitNumber;
}