package com.jdx.rover.monitor.bo.vehicle;

import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 车辆告警咚咚通知对象
 *
 * <AUTHOR>
 */
@Data
public class VehicleAlarmNoticeBO {
  /**
   * 车辆名称
   */
  private String vehicleName;
  /**
   * 主题
   */
  private String title;

  /**
   * 内容
   */
  private String content;

  /**
   * 调度状态
   */
  private String scheduleState;

  /**
   * 站点名
   */
  private StationBasicDTO stationInfo;

  /**
   * 通知用户
   */
  private List<String> user;

  /**
   * 事件触发事件
   */
  private Date date;

}
