package com.jdx.rover.monitor.bo.vehicle;

import lombok.Data;

import java.util.List;

/**
 * 车辆分数业务对象
 *
 * <AUTHOR>
 */
@Data
public class VehicleScoreBO {
  /**
   * 车辆名称
   */
  private String vehicleName;
  /**
   * 调度状态
   */
  private String scheduleState;
  /**
   * 系统状态
   */
  private String systemState;
  /**
   * 告警事件
   */
  private String alarmEvent;
  /**
   * 业务类型
   */
  private String businessType;
  /**
   * 电量
   */
  private Double power;
  /**
   * 站点名称(营业部排序时非空)
   */
  private String stationName;
  /**
   * 车辆名称有序列表
   */
  private List<String> vehicleNameSortList;
  /**
   * 站点名称有序列表
   */
  private List<String> stationSortList;
  /**
   * 分数类型
   */
  private String scoreType;
}
