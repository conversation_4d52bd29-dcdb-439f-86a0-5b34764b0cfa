/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a schedule stop information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopDetailDTO {

  /**
   * <p>
   * Represent the id of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represent the total order number of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer totalCollectOrderNum;

  /**
   * <p>
   * Represent the finished order number of the stop. The default value is 0. It's changeable. The
   * unit is minute.
   * </p>
   */
  private Integer finishedCollectOrderNum;

  /**
   * <p>
   * Represent the canceled order num of stop. The default value is null. It's changeable. 
   * </p>
   */
  private Integer canceledCollectOrderNum;

  /**
   * <p>
   * Represent the total order number of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer totalDeliveryOrderNum;

  /**
   * <p>
   * Represent the finished order number of the stop. The default value is 0. It's changeable. The
   * unit is minute.
   * </p>
   */
  private Integer finishedDeliveryOrderNum;

  /**
   * <p>
   * Represent the canceled order num of stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer canceledDeliveryOrderNum;

  /**
   * <p>
   * Represent the arrived time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date arrivedTime;

  /**
   * <p>
   * Represent the depart time. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date departTime;

  /**
   * <p>
   * Represent the waiting duration of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer waitingTime;

}
