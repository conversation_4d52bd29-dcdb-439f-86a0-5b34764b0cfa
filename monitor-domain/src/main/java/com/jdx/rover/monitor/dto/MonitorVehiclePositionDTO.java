/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a  vehicle postion data transform object entity..
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehiclePositionDTO {

  /**
   * <p>
   * The latitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * The longitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * The heading for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double heading;

  /**
   * <p>
   * Represents the zone of utm. The default value is null. It's changeable.
   * </p>
   */
  private String utmZone;

}
