package com.jdx.rover.monitor.dto.s3;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预签名请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class S3PreSignDTO {
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * PUT请求URL,用于上传
     */
    private String putUrl;
    /**
     * GET请求URL,用于下载
     */
    private String getUrl;
}
