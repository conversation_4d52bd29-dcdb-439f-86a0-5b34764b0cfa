/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/8/12 02:09
 * @description 车辆限速信息
 */
@Data
public class MonitorSpeedLimitDTO {

    /**
     * 车端conf限速值
     */
    private Double xgflags;

    /**
     * 是否开启临时限速
     */
    private Boolean isTempSpeedLimitEnabled;

    /**
     * 临时限速类型
     * @see com.jdx.rover.monitor.enums.SpeedLimitTypeEnum
     */
    private String tempSpeedLimitType;

    /**
     * 临时限速值，KM/H
     */
    private Double tempSpeedLimit;
}
