/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.tnta;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a impassable area data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ImpassableAreaResponseDTO {
  /**
   * <p>
   * 响应码
   * </p>
   */
  private Integer stateCode;

  /**
   * <p>
   * 数据量
   * </p>
   */
  private Integer recordCount;

  /**
   * <p>
   * 当前页
   * </p>
   */
  private Integer pageNo;

  /**
   * <p>
   * 总页数
   * </p>
   */
  private Integer pageSize;

  /**
   * <p>
   * 页总显示数
   * </p>
   */
  private Integer pageCount;

  /**
   * <p>
   * 返回数据
   * </p>
   */
  private List<ImpassableAreaItemDTO> objects;

}
