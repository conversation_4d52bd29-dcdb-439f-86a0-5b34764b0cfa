/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.accident;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 事故相关DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Data
@NoArgsConstructor
public class AccidentInfoDTO {
    /**
     * 事故分类
     */
    private String accidentType;

    /**
     * 事故创建来源
     */
    private String accidentSource;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * 事故发生时间 确定发生时间
     */
    private Date accidentTime;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 事故排查状态
     */
    private String accidentStatus;

    /**
     * 提报的jira号
     */
    private String jiraNo;

    /**
     * xata 平台任务ID
     */
    private String xataTaskId;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 事故提报时 rover 版本
     */
    private String roverVersion;

    /**
     * 事故提报时车辆所属站点名称
     */
    private String stationName;

    /**
     * 事故创建时间
     */
    private Date createdTime;
}
