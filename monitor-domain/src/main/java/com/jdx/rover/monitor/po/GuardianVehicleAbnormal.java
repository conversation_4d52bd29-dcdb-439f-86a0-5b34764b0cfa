/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * 车辆异常 数据库表名为guardian_vehicle_abnormal
 *
 * <AUTHOR>
 */
@Data
public class GuardianVehicleAbnormal extends BaseDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 翻译信息
     */
    private String translateMessage;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}