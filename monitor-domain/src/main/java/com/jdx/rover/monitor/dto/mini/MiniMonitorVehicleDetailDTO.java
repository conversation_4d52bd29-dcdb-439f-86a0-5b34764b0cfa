/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import com.jdx.rover.monitor.dto.vehicle.SingleVehicleExceptionDTO;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a mini-monitor vehicle detail data transform object entity for search.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorVehicleDetailDTO {

  /**
   * <p>
   * Represents the id of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the name of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the business type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleBusinessType;

  /**
   * <p>
   * Represents the schedule state of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents dada work state. The default value is null. It's changeable.
   * </p>
   */
  private Boolean dadaWorkState;

  /**
   * <p>
   * Represents the schedule start time. The default value is null. It's changeable.
   * </p>
   */
  private Long scheduleStartTime;

  /**
   * <p>
   * Represents the schedule task of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * Represents the pnc task of the vehicle. The default value is null. It's changeable.
   */
  private String pncTaskType;

  /**
   * <p>
   * Represents the status of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private MiniMonitorVehicleStatusDTO status;

  /**
   * <p>
   * Represents the stop list of schedule. The default value is null. It's changeable.
   * </p>
   */
  private List<MiniMonitorStopDTO> stop;

  /**
   * <p>
   * Represents the order list of schedule. The default value is null. It's changeable.
   * </p>
   */
  private List<MiniMonitorOrderInfoDTO> order;

  private List<SingleVehicleExceptionDTO> vehicleExceptionList;

  /**
   * 接管人
   */
  private String takeOverUsername;

  /**
   * <p>
   * 接管来源
   * </p>
   */
  private String takeOverSource;
}
