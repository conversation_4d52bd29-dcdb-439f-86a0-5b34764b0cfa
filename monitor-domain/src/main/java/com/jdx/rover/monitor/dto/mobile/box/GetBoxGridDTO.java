package com.jdx.rover.monitor.dto.mobile.box;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.api.domain.enums.GridPositionEnum;
import com.jdx.rover.metadata.domain.dto.box.BoxGridInfoDto;
import com.jdx.rover.metadata.domain.dto.box.GridBasicInfoDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16 15:09
 * @description 货箱格口列表
 */
@Data
@NoArgsConstructor
public class GetBoxGridDTO {

    /**
     * 左侧列数
     */
    private int leftBoxColumnNum;

    /**
     * 右侧列数
     */
    private int rightBoxColumnNum;

    /**
     * 左侧格口列表
     */
    private List<GridInfo> leftGridList;

    /**
     * 右侧格口列表
     */
    private List<GridInfo> rightGridList;

    /**
     * Constructor
     * 
     * @param boxGridInfoDto 格口基本信息传输对象
     */
    public GetBoxGridDTO(BoxGridInfoDto boxGridInfoDto) {
        if (null == boxGridInfoDto) {
            return;
        }
        this.leftBoxColumnNum = boxGridInfoDto.getLeftBoxColumnNum();
        this.rightBoxColumnNum = boxGridInfoDto.getRightBoxColumnNum();
        List<GridBasicInfoDto> gridBasicInfoList = boxGridInfoDto.getGridBasicInfoList();
        if (CollUtil.isEmpty(gridBasicInfoList)) {
            return;
        }
        gridBasicInfoList.forEach(gridBasicInfoDto -> {
            GridInfo gridInfo = new GridInfo();
            gridInfo.setGridNo(gridBasicInfoDto.getGridNo());
            gridInfo.setLength(gridBasicInfoDto.getLength());
            gridInfo.setWidth(gridBasicInfoDto.getWidth());
            gridInfo.setHeight(gridBasicInfoDto.getHeight());
            gridInfo.setEnable(gridBasicInfoDto.getEnable());
            if (GridPositionEnum.LEFT.getValue().equals(gridBasicInfoDto.getSide())) {
                if (null == this.leftGridList) {
                    this.leftGridList = Lists.newArrayList();
                }
                this.leftGridList.add(gridInfo);
            } else if (GridPositionEnum.RIGHT.getValue().equals(gridBasicInfoDto.getSide())) {
                if (null == this.rightGridList) {
                    this.rightGridList = Lists.newArrayList();
                }
                this.rightGridList.add(gridInfo);
            }
        });
    }

    /**
     * 格口信息
     */
    @Data
    public static class GridInfo {

        /**
         * 格口号
         */
        private Integer gridNo;

        /**
         * 长度
         */
        private Double length;

        /**
         * 宽度
         */
        private Double width;

        /**
         * 高度
         */
        private Double height;

        /**
         * 启用状态
         */
        private Integer enable;
    }
}
