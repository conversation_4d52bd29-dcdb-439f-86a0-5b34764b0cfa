package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故快照视频截取状态
 * capturing-截取中;success-截取成功;fail-截取失败
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VideoCaptureStatus {
    CAPTURING("capturing", "截取中"),
    SUCCESS("success", "截取成功"),
    FAIL("fail", "截取失败"),
    ;
    /**
     * 状态代码
     */
    private final String code;
    /**
     * 状态名称
     */
    private final String name;
}
