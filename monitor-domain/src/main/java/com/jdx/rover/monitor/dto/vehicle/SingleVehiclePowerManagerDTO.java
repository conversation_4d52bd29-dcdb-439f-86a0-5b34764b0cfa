/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 监控单车页红绿灯列表
 *
 * <AUTHOR>
 */
@Data
public class SingleVehiclePowerManagerDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 操作名称
   */
  private String action;


  /**
   * 操作状态(INIT,RUNNING,SUCCESS,FAIL)
   */
  private String state;

  /**
   * 操作时间
   */
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date operateTimestamp;
}
