/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

/**
 * <p>
 * 用户基础数据请求
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserMetaDataInfoRequestVO {

  /**
   * <p>
   * Reprensents the request name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * 数据类型，表示用户基础数据的类型。
   */
  private String dataType;

}
