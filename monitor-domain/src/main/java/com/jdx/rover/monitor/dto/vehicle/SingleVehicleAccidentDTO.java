/*
 * Copyright (c) 2022-2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.jdx.rover.monitor.dto.accident.MonitorAccidentAttachmentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 监控单车页事故信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleAccidentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 事故创建来源
     *
     * @see com.jdx.rover.monitor.enums.accident.AccidentSourceEnum
     */
    private String accidentSource;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * 事故发生时间
     */
    private Date accidentTime;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 提报的jira号
     */
    private String jiraNo;

    /**
     * 影子系统事件对应id
     */
    private Integer shadowEventId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 事故快照
     */
    private List<MonitorAccidentAttachmentDTO> snapshotAttachmentUrlList;

    /**
     * 视频截取状态：capturing-截取中;success-截取成功;fail-截取失败
     */
    private String videoCaptureStatus;

    /**
     * 事故分类（合并原来的技术支持和安全组）
     */
    private String accidentType;

    /**
     * 事故等级（合并原来的技术支持和安全组）
     */
    private String accidentLevel;

    /**
     * 提报缺陷号
     */
    private String bugCode;
}
