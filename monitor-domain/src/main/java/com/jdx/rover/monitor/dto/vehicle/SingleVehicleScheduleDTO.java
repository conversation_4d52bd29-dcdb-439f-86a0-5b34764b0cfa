/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.util.List;

/**
 * 监控单车页调度信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleScheduleDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 调度单号
   */
  private String scheduleNo;

  /**
   * 当前停靠点索引
   */
  private Integer currentGoalIndex;

  /**
   * 规划总里程
   */
  private Double globalMileage;

  /**
   * 站点列表
   */
  private List<SingleVehicleScheduleStopDTO> stopList;
}
