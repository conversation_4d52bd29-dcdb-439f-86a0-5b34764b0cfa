/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

/**
 * 监控单车页操作记录
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleTakeOverAndIssueDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 工单号
   */
  private String issueNo;

  /**
   * 工单状态
   */
  private String issueState;

  /**
   * 接管状态
   */
  private String takeOverUser;

  /**
   * 接管方
   */
  private String takeOverSource;

  /**
   * 接管方式
   */
  private String takeOverStatus;
}
