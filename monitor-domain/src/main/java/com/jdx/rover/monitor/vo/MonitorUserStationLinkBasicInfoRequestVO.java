/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a basic infomation entity which contains user id and station.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe.
 * But it is an entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class MonitorUserStationLinkBasicInfoRequestVO {

  /**
   * <p>
   * The User id. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(1)
  private Integer userId;

  /**
   * <p>
   * The User name. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String userName;

  /**
   * <p>
   * The business type. The default value is null. It's changeable.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * The special station id. The default value is null. It's changeable.
   * </p>
   */
  private String stationId;

}
