/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

/**
 * <p>
 * This is a guardian vehicle abnormal boot info entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAbnormalBoot extends BaseDomain {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 启动id
   * </p>
   */
  private String bootUuid;

  /**
   * <p>
   * 软启动id
   * </p>
   */
  private Integer bootId;

  /**
   * <p>
   * 启动次数
   * </p>
   */
  private Integer bootCount;

  /**
   * <p>
   * 启动节点
   * </p>
   */
  private String nodeName;

  /**
   * <p>
   * 启动模块
   * </p>
   */
  private String moduleName;

  /**
   * <p>
   * 启动状态
   * </p>
   */
  private String bootStatus;

  /**
   * <p>
   * 启动错误码
   * </p>
   */
  private Integer errorCode;

  /**
   * <p>
   * 启动错误消息
   * </p>
   */
  private String errorMessage;

  /**
   * <p>
   * 持续时间
   * </p>
   */
  private Integer duration;

  /**
   * <p>
   * 关注用户
   * </p>
   */
  private String users;

}
