/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a guardian alarm info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAlarmInfoVO {

  /**
   * <p>
   * Represents the id of vehicle alarm. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Integer id;

  /**
   * <p>
   * Represents the description. The default value is null. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the id of component. The default value is null. It's changeable.
   * </p>
   */
  private String componentId;

  /**
   * <p>
   * Represents the name of reporter. The default value is null. It's changeable.
   * </p>
   */
  private String componentName;

}
