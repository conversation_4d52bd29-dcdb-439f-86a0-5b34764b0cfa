/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 车辆调度任务类型.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ScheduleTaskTypeEnum {
  /**
   * <p>
   * The enumerate type.
   * </p>
   */
  NOTASK("NOTASK", "-"),
  LOADTASK("LOADTASK", "装载"),
  DELIVERY("DELIVERY", "配送"),
  UNLOADTASK("UNLOADTASK","卸载"),
  DROPOFF("DROPOFF", "揽收");

  /**
   * <p>
   * 类型.
   * </p>
   */
  private String value;

  /**
   * <p>
   * 名称.
   * </p>
   */
  private String name;

  public static ScheduleTaskTypeEnum of(String value) {
    for (ScheduleTaskTypeEnum em : ScheduleTaskTypeEnum.values()) {
      if (Objects.equals(value, em.getValue())) {
        return em;
      }
    }
    return ScheduleTaskTypeEnum.NOTASK;
  }
}
