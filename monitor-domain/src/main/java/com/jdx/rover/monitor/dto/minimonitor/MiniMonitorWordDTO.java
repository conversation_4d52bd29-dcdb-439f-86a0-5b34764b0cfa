/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.minimonitor;

import lombok.Data;

/**
 * <p>
 * This is a word data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorWordDTO {

  /**
   * <p>
   * Represents the word id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the content. The default value is null. It's changeable.
   * </p>
   */
  private String content;

}
