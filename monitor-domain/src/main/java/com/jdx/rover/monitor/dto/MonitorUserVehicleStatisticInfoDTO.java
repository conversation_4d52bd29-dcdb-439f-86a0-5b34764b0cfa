/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a vehicle basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserVehicleStatisticInfoDTO {
  
  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the vehicle's state. The default value is null. It's changeable.
   * </p>
   */
  private Integer count;
}
