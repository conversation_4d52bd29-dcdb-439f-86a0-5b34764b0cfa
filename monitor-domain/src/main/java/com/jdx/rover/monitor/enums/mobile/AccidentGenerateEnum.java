package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故单生成操作枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentGenerateEnum {

    SAVE("SAVE", "暂存"),
    SUBMIT("SUBMIT", "提交"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentOperateEnum accidentOperateEnum : AccidentOperateEnum.values()) {
            if (accidentOperateEnum.getValue().equals(value)) {
                return accidentOperateEnum.getName();
            }
        }
        return null;
    }
}
