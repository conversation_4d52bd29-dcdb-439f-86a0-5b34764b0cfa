///***************************************************************************
// *
// * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
// *
// **************************************************************************/
//package com.jdx.rover.monitor.vo.accident;
//
//import com.jdx.rover.common.utils.login.LoginUtils;
//import com.jdx.rover.monitor.enums.accident.AccidentSourceEnum;
//import com.jdx.rover.monitor.enums.accident.AccidentStatusEnum;
//import lombok.Data;
//
//import java.util.Date;
//
///**
// * <p>
// * 监控端事故提报入参
// * </p>
// *
// * <AUTHOR>
// * @date 2023/06/15
// */
//@Data
//public class MonitorAccidentAddVO {
//
//    /**
//     * 事故分类
//     * @see com.jdx.rover.monitor.enums.accident.AccidentTypeEnum
//     */
//    private String accidentType;
//
//    /**
//     * 事故描述
//     */
//    private String accidentDesc;
//
//    /**
//     * 事故发生时间
//     */
//    private Date accidentTime;
//
//    /**
//     * 车辆名称
//     */
//    private String vehicleName;
//
//    /**
//     * 事故发生时间
//     */
//    private Date accidentStartTime;
//
//    /**
//     * 事故发生时间
//     */
//    private Date accidentEndTime;
//
//    /**
//     * 创建 AccidentInfo
//     * @return AccidentInfo
//     */
//    public AccidentInfo createAccident(){
//        AccidentInfo accidentInfo = new AccidentInfo();
//        accidentInfo.setAccidentSource(AccidentSourceEnum.MANUALLY_CREATED.getValue());
//        accidentInfo.setAccidentStatus(AccidentStatusEnum.WAIT.getValue());
//        accidentInfo.setAccidentType(this.accidentType);
//        accidentInfo.setVehicleName(this.vehicleName);
//        accidentInfo.setAccidentDesc(this.accidentDesc);
//        accidentInfo.setAccidentTime(this.accidentTime);
//        accidentInfo.setAccidentStartTime(this.accidentStartTime);
//        accidentInfo.setAccidentEndTime(this.accidentEndTime);
//        accidentInfo.setCreatedUser(LoginUtils.getUsername());
//        return accidentInfo;
//    }
//}
