/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 接口返回值风格样式枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleScheduleStateSortEnum {
  SETOUT(1, "去程"),
  RETURN(2, "返程"),
  TOLOAD(3, "去装载"),
  TOUNLOAD(3, "去卸载"),
  DELIVERY(4, "投递中"),
  LOADING(5, "装载中"),
  UNLOADING(5, "卸载中"),
  ALREADY(6, "已装载"),
  // 新版没有UNLOAD已卸载了
  UNLOAD(6, "已卸载"),
  ARRIVED(7, "已到达"),
  WAITING(8, "待装载"),
  ;

  private final Integer value;

  private final String title;
}

