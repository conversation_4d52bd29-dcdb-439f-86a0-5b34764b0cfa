package com.jdx.rover.monitor.vo.mobile.box;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/16 18:36
 * @description 开箱VO
 */
@Data
public class VehicleOpenBoxVO {

    /**
     * 车辆名称
     */
    @NotBlank(message = "车辆名称不能为空")
    private String vehicleName;

    /**
     * 开箱格口列表
     */
    @NotNull(message = "开箱格口列表不能为空")
    private List<String> gridNoList;
}
