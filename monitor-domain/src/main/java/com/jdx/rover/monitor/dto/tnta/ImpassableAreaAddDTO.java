/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.tnta;

import lombok.Data;

/**
 * <p>
 * This is a impassable area add data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data

public class ImpassableAreaAddDTO {

  /**
   * <p>
   * 响应码
   * </p>
   */
  private String stateCode;

  /**
   * <p>
   * ID
   * </p>
   */
  private String objid;

}
