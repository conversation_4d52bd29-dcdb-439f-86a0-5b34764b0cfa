/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆异常DTO
 *
 * <AUTHOR>
 */
@Data
public class GuardianVehicleAbnormalDTO implements Serializable {
  private static final long serialVersionUID = 1L;
  /**
   * 编号
   */
  private Integer id;

  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 模块名称
   */
  private String moduleName;

  /**
   * 错误码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMessage;

  /**
   * 开始时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * 结束时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;
}