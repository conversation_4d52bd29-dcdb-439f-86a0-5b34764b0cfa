package com.jdx.rover.monitor.enums.mobile;

import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: SystemStatusEnum
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-07-15 17:41
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SystemStatusEnum {

    NORMAL("NORMAL", "正常", 1), ABNORMAL("ABNORMAL", "异常", 2), OFFLINE("OFFLINE", "离线", 3),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 排序
     */
    private final Integer sort;

    /**
     * 枚举映射
     *
     * @param systemState systemState
     * @return SystemStatusEnum
     */
    public static SystemStatusEnum getCodeMapping(String systemState) {
        if (SystemStateEnum.NORMAL.getSystemState().equals(systemState)) {
            return SystemStatusEnum.NORMAL;
        }
        if (SystemStateEnum.OFFLINE.getSystemState().equals(systemState)) {
            return SystemStatusEnum.OFFLINE;
        }
        return SystemStatusEnum.ABNORMAL;
    }
}