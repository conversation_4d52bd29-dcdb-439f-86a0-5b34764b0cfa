/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/20 12:12
 * @description 监控单车/多车页任务列表请求VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorTaskSearchVO extends PageVO {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 站点ID
     */
    private Integer stationId;
}
