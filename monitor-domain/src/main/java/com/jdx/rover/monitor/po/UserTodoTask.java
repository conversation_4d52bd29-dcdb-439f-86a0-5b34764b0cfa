package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 用户代办任务表
 */
@Data
public class UserTodoTask extends BaseModel {

    /**
     * 模块
     */
    private String module;

    /**
     * 类型
     */
    private String type;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 位置
     */
    private String address;

    /**
     * 描述
     */
    private String description;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 业务主键
     */
    private String businessKey;
}
