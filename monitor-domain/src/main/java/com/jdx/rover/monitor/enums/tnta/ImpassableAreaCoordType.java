/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.tnta;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a impassable area coord type enum.
 * </p>
 *
 * <p>
 * <strong>impassableArea type: </strong> enumeration of the class impassableArea type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@AllArgsConstructor
@ToString
public enum ImpassableAreaCoordType {

  /**
   * <p>
   * The enumerate impassableArea coordinate types.
   * </p>
   */
  XCS(0), WGS84(1), UTM(2), GCJ02(3);

  /**
   * <p>
   * The type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private Integer coordType;
}
