/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 不可通行区域类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ImpassableAreaEffectTypeEnum {

  /**
   * <p>
   * The enumerate effect type.
   * </p>
   */
  PERMANENT("PERMANENT ", 1, "长期生效"),
  SEASONAL("SEASONAL", 2, "周期生效");

  /**
   * <p>
   * The type of effect.
   * </p>
   */
  private final String type;

  /**
   * <p>
   * The value of effect.
   * </p>
   */
  private final Integer value;

  /**
   * <p>
   * The type name of effect.
   * </p>
   */
  private final String name;

  /**
   * <p>
   * 依据type获取enum
   * </p>
   */
  public static ImpassableAreaEffectTypeEnum getByType(final String type) {
    for (ImpassableAreaEffectTypeEnum itemEnum : ImpassableAreaEffectTypeEnum.values()) {
      if (itemEnum.type.equals(type)) {
        return itemEnum;
      }
    }
    return null;
  }

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static ImpassableAreaEffectTypeEnum getByValue(final String value) {
    for (ImpassableAreaEffectTypeEnum itemEnum : ImpassableAreaEffectTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
