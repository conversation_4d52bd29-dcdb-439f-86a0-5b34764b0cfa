/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.tnta;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a impassable area coord field status enum.
 * </p>
 *
 * <p>
 * <strong>impassableArea coord status: </strong> enumeration of the class impassableArea coord status.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@AllArgsConstructor
@ToString
public enum ImpassableAreaCoordStatus {

  /**
   * <p>
   * The enumerate impassableArea coord status.
   * </p>
   */
  USEING(1), DELETE(2);

  /**
   * <p>
   * The status corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private Integer coordStatus;
}
