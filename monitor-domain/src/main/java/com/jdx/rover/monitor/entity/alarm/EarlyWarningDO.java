package com.jdx.rover.monitor.entity.alarm;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 预警DO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Data
public class EarlyWarningDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警编号
     */
    private String alarmNumber;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 消失时间
     */
    private Date endTime;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 区域ID
     */
    private Integer areaId;

}