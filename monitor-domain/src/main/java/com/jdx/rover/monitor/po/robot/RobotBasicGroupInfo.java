/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.po.robot;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

/**
 * <p>
 * 机器人(多合一/巡检)分组信息表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RobotBasicGroupInfo extends BaseDomain {

  /**
   * <p>
   * 产品标识
   * </p>
   */
  private String productKey;

  /**
   * <p>
   * 设备编号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 设备备注
   * </p>
   */
  private String remarkName;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelNo;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelName;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupOne;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupTwo;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupThree;

  /**
   * <p>
   * 分组名称
   * </p>
   */
  private String groupName;

  /**
   * <p>
   * 分级名称
   * </p>
   */
  private String groupLevelName;

  /**
   * <p>
   * 工作模式
   * </p>
   */
  private String workMode;

  /**
   * <p>
   * 是否可用
   * </p>
   */
  private Boolean enable;

}
