/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo.robot;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 机器人地图模式请求
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/10
 */
@Data
public class RobotMapInfoRequestVO {

  /**
   * 产品
   */
  @NotEmpty
  private String productKey;

  /**
   * 一级分组
   */
  @NotEmpty
  private String groupOne;

  /**
   * 二级分组
   */
  private String groupTwo;

  /**
   * 设备编号
   */
  private String deviceName;

  /**
   * 工作模式
   */
  private List<String> workMode;

}
