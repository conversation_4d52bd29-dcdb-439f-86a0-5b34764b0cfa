/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.dto.cockpit;

import com.jdx.rover.common.domain.page.PageDTO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 座舱看板页列表
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MultiCockpitVehiclePageDTO<T> extends PageDTO<T> implements Serializable {
  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 业务中车辆数
   */
  private Integer scheduleVehicleCount;

}
