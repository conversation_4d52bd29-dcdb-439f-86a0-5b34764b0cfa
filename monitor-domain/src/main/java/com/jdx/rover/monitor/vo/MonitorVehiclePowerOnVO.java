package com.jdx.rover.monitor.vo;

import com.jdx.rover.monitor.enums.mobile.H5RemotePowerEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @description: 远驾远程开机VO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-08-13 13:39
 **/
@Data
public class MonitorVehiclePowerOnVO {

    /**
     * 车号
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 操作类型
     *
     * @see H5RemotePowerEnum
     */
    @NotBlank(message = "operateType不能为空")
    private String operateType;
}