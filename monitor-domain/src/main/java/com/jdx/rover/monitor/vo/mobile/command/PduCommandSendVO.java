package com.jdx.rover.monitor.vo.mobile.command;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @description: PduCommandSendVo
 * @author: wangguotai
 * @create: 2024-07-18 13:25
 **/
@Data
public class PduCommandSendVO {

    /**
     * 车号
     */
    @NotBlank(message = "车号不能为空")
    private String vehicleName;

    /**
     * 指令类型
     */
    @NotBlank(message = "指令类型不能为空")
    private String commandType;
}
