/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;
import java.util.List;

/**
 * <p>
 * This is a view object for verify code request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVerifyCodeRequestVO {

    /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the id of order. The default value is 0. It's changeable.
   * </p>
   */
  private Integer orderId;

  /**
   * <p>
   * Represents the order info. The default value is 0. It's changeable.
   * </p>
   */
  private List<MonitorOrderInfoRequestVO> orderInfo;

}
