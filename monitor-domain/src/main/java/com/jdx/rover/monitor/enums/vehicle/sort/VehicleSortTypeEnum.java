/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 排序类型(VEHICLE_NAME车号,BUSINESS_TYPE业务,POWER电量)
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleSortTypeEnum {
  VEHICLE_NAME("VEHICLE_NAME", "车号"),
  BUSINESS_TYPE("BUSINESS_TYPE", "业务"),
  POWER("POWER", "电量"),
  STATION("STATION", "站点"),
  ;

  private final String value;

  private final String title;

  public static VehicleSortTypeEnum of(final String value) {
    if (StringUtils.isBlank(value)) {
      return null;
    }
    for (VehicleSortTypeEnum itemEnum : VehicleSortTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}

