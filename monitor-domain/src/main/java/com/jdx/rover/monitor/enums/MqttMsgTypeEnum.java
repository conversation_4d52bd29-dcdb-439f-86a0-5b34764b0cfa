/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * mqtt消息类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MqttMsgTypeEnum {
  VEHICLE_MSG_BROADCAST("start_voice_broadcast", "语音播报"),
  VEHICLE_VOICE_WHISTLE("start_vehicle_whistle", "声音鸣笛"),
  VEHICLE_CHANGE_VIDEO_MODE("t/%s/rover/%s/video/json/services", "切换视频质量"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 名称
   */
  private String name;
}
