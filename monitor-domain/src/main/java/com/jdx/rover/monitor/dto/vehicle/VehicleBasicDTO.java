package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 车辆基础信息
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
@Data
public class VehicleBasicDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 车辆名
     */
    private String name;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 运营场景
     */
    private String ownerUseCase;

    /**
     * 站点Id
     */
    private Integer stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 城市Id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 车辆类型
     */
    private Integer vehicleTypeId;

    /**
     * 标签列表
     */
    private List<Integer> tagList;
    
    /**
     * 货箱id
     */
    private Integer boxId;

    /**
     * 供应商
     */
    private String supplier;


}
