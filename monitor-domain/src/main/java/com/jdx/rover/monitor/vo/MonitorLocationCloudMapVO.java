/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 定位初始化车辆激光点云地图帧数据
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorLocationCloudMapVO {

  /**
   * <p>
   * 车号
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * 请求id
   * </p>
   */
  private Long requestId;

  /**
   * <p>
   * 定位给出的待选位姿列表
   * </p>
   */
  private List<MonitorVehicleLocationPoseVO> poseList;

  /**
   * <p>
   * 数据长度
   * </p>
   */
  @NotNull
  private Integer dataLength;

  /**
   * <p>
   * 点云地图
   * </p>
   */
  @NotNull
  private String poseData;

  /**
   * <p>
   * 地图id
   * </p>
   */
  @NotNull
  private Integer mapId;

  /**
   * <p>
   * 地图版本
   * </p>
   */
  private Long mapVersion;

  /**
   * <p>
   * 瓦片列表
   * </p>
   */
  private List<MonitoVehicleTileMapVO> tileList;

  /**
   * <p>
   * 定位显示图片
   * </p>
   */
  private String locResult;

}
