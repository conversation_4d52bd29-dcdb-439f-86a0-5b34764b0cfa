/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is a mini monitor alarm event data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorAlarmEventDTO {
  /**
   * <p>
   * Represents the alarm event's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the alarm event's type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the alarm event's start date. The default value is null. It's changeable.
   * </p>
   */
  private Date startTimestamp;
}
