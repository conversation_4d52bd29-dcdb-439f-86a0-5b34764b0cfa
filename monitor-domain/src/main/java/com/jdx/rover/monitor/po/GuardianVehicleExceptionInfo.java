/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.GuardianVehicleExceptionInfoDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a guardian exception info entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleExceptionInfo extends BaseDomain {

  /**
   * <p>
   * Represents the vehicle entity of guardian exception info. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the city entity of guardian exception info. It's changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station entity of guardian exception info. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the error code of guardian exception info. It's changeable.
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * Represents the error level of guardian exception info. It's changeable.
   * </p>
   */
  private String errorLevel;

  /**
   * <p>
   * Represents the error message of guardian exception info. It's changeable.
   * </p>
   */
  private String errorMessage;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian exception info. It's
   * changeable.
   * </p>
   */
  private Date operateTimestamp;

  /**
   * <p>
   * guardian exception info to guardian exception info data transform object
   * </p>
   * 
   * @return The guardian exception info data transform object
   */
  public GuardianVehicleExceptionInfoDTO toGuardianExceptionInfoDto() {
    GuardianVehicleExceptionInfoDTO guardianExceptionInfoDto = new GuardianVehicleExceptionInfoDTO();
    guardianExceptionInfoDto.setId(getId());
    guardianExceptionInfoDto.setVehicleName(vehicleName);
    guardianExceptionInfoDto.setErrorCode(errorCode);
    guardianExceptionInfoDto.setErrorLevel(errorLevel);
    guardianExceptionInfoDto.setErrorMessage(errorMessage);
    guardianExceptionInfoDto.setOperateTimestamp(operateTimestamp);
    guardianExceptionInfoDto.setCityName(cityName);
    guardianExceptionInfoDto.setStationName(stationName);
    return guardianExceptionInfoDto;
  }
}
