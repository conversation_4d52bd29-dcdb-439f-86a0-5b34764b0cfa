package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentIsSatefyEnum {

    YES(1, "需要"),
    NO(0, "不需要"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentIsSatefyEnum accidentIsSatefyEnum : AccidentIsSatefyEnum.values()) {
            if (accidentIsSatefyEnum.getValue().equals(value)) {
                return accidentIsSatefyEnum.getName();
            }
        }
        return null;
    }
}
