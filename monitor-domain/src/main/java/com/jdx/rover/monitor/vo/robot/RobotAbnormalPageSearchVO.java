/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.vo.robot;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import java.util.Date;

/**
 * 机器人告警查询列表
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class RobotAbnormalPageSearchVO extends PageVO {
  /**
   * 设备编号
   */
  private String deviceName;

  /**
   * 车牌号
   */
  private String remarkName;

  /**
   * 产品
   */
  private String productKey;

  /**
   * 工作模式
   */
  private String workMode;

  /**
   * 分组编号
   */
  private String groupNo;

  /**
   * 开始时间
  */
  private Date startTime;

  /**
   * 结束时间
   */
  private Date endTime;

  /**
   * 站点名称
   */
  private String stationName;

  /**
   * 处理方式
   */
  private String processMode;

  /**
   * 跟进用户
   */
  private String followUser;

}
