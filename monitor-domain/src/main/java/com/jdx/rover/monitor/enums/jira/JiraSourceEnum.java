/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.jira;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * This is a jira source enum.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
@Getter
public enum JiraSourceEnum {
  /**
   * <p>
   * The enumerate jira use case type.
   * </p>
   */
  ISSUE("ISSUE", "工单"),
  ACCIDENT("ACCIDENT", "事故"),
  ;

  private String value;

  private String name;

  public static JiraSourceEnum of(String value) {
    for (JiraSourceEnum em : JiraSourceEnum.values()) {
      if (Objects.equals(value, em.getValue())) {
        return em;
      }
    }
    return null;
  }
}
