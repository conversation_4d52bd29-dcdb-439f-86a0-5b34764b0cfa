/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a vehicle stop basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehiceStopBasicMapInfoDTO {

   /**
   * <p>
   * Represents the stop's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the stop's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the stop's lat. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the stop's lon. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents whether the stop is skiped. The default value is 0. It's changeable.
   * </p>
   */
  private boolean isSkiped;

  /**
   * <p>
   * Represents stop travel status. The default value is null. It's changeable.
   * </p>
   */
  private String travelStatus;

}
