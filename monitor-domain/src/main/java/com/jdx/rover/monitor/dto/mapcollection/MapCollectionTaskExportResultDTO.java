/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 勘查任务导出结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MapCollectionTaskExportResultDTO {

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 下载URL
     */
    private String downloadUrl;

    /**
     * 导出记录数
     */
    private Integer recordCount;
}
