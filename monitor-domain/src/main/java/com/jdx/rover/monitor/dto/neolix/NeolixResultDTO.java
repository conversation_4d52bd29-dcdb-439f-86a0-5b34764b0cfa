package com.jdx.rover.monitor.dto.neolix;

import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

/**
 * 新石器侧返回对象
 */
@Data
public class NeolixResultDTO<T> {

    /**
     * 响应码
     */
    private String code;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 新石器侧成功响应
     */
    private static final String successCode = "10000";

    /**
     * 校验响应码是否成功
     * @return 校验结果
     */
    public boolean isSuccess() {
        return successCode.equals(this.code);
    }
}
