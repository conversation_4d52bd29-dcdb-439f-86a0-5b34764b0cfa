/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.issue;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * This is the entity for issue monitor operate history record.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueOperateHistoryDTO {

  /**
   * <p>
   * Represents the operate title of the supervisor issue. The default value is null. It's
   * changeable.
   * </p>
   */
  private String operateTitle;

  /**
   * <p>
   * Represents the operate user of the issue operation. The default value is null. It's changeable.
   * </p>
   */
  private String operateUser;

  /**
   * <p>
   * Represents the operate time of the issue operation. The default value is null. It's changeable.
   * </p>
   */
  private Date operateTime;

  /**
   * <p>
   * Represents the operate message of the issue operation. The default value is null. It's
   * changeable.
   * </p>
   */
  private String operateMsg;

  /**
   * <p>
   * Represents the alarm id of the issue operation. The default value is null. It's changeable.
   * </p>
   */
  private Integer alarmId;

  /**
   * <p>
   * Represents the attachment url list of the issue operation. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<String> attachmentList;
}
