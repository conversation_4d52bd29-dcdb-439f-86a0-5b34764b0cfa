package com.jdx.rover.monitor.dto.mobile.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 小程序异常车辆信息
 */
@Data
public class ErrorVehicleInfoDTO {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 车辆业务类型(配送/售卖)
     */
    private String vehicleBusinessType;

    /**
     * 系统状态
     */
    private String systemState;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 剩余电量
     */
    private Double power;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 调度状态
     */
    private String scheduleState;

    /**
     * 调度任务
     */
    private String taskType;

    /**
     * 调度信息
     */
    private ErrorVehicleScheduleDTO schedule;

    /**
     * 告警事件
     */
    private List<ErrorVehicleAlarmEventDTO> alarmEventList;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date recordTime;
}
