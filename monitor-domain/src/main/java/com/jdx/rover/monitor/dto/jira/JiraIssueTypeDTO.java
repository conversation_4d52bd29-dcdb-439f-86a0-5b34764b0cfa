/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.jira;

import lombok.Data;

/**
 * <p>
 * This is a jira issue type data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraIssueTypeDTO {

  /**
   * <p>
   * Represents the issue type's id. The default value is null. It's changeable.
   * </p>
   */
  private Long id;

  /**
   * <p>
   * Represents the issue type's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the description of issue type. The default value is null. It's changeable.
   * </p>
   */
  private String description;

}
