package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 京ME推送配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccidentJdmeConfig extends BaseModel {
    /**
     * 固定的领导群号
     */
    private String fixedGroupId;
    /**
     * 日报发送群
     */
    private String dailyReportGroupId;
    /**
     * 周报发送群
     */
    private String weeklyReportGroupId;
    /**
     * 转人工处理的人工ERP
     */
    private String manualPin;
    /**
     * 一事一群管理员
     */
    private String newGroupOwner;

    /**
     * 事故简报咨询人工
     */
    private String accidentReportPin;
}
