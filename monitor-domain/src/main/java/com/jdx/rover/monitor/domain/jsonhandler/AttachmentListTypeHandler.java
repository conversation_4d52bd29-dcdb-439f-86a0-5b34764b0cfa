/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.domain.jsonhandler;

import com.alibaba.fastjson2.TypeReference;
import com.jdx.rover.monitor.po.mapcollection.json.Attachment;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16 13:17
 * @description 附件映射泛型
 */
public class AttachmentListTypeHandler extends ListTypeHandler<Attachment> {

    @Override
    protected TypeReference<List<Attachment>> specificType() {
        return new TypeReference<>() {};
    }
}
