package com.jdx.rover.monitor.dto.mobile.map;

import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/17 19:39
 * @description 车辆规划路径
 */
@Data
public class MapVehiclePathDTO {

    /**
     * 车辆当前位置
     */
    private Position vehiclePosition;

    /**
     * 停靠点点位
     */
    private List<Position> stopPointList;

    /**
     * pnc规划点位
     */
    private List<MonitorRoutingPointEntity> routingPointList;

    /**
     * 位置信息
     */
    @Data
    @NoArgsConstructor
    public static class Position {

        /**
         * 经度
         */
        private Double longitude;

        /**
         * 纬度
         */
        private Double latitude;

        /**
         * Constructor
         *
         * @param longitude longitude
         * @param latitude latitude
         */
        public Position(Double longitude, Double latitude) {
            this.longitude = longitude;
            this.latitude = latitude;
        }
    }
}
