/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

/**
 * <p>
 * 机器人设备异常信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotReportAbnormalDTO {

    /**
     * 设备编号
     */
    private String deviceName;

    /**
     * 模块
     */
    private String moduleName;

    /**
     * 告警码
     */
    private Integer code;

    /**
     * 告警级别
     */
    private String alarmLevel;

    /**
     * 异常消息
     */
    private String message;

    /**
     * 时间
     */
    private Long timestamp;

    /**
     * 判断产生或者消失
     */
    private Boolean active;

    /**
     * 任务标识
     */
    private String taskId;

    /**
     * 启动标识
     */
    private Long bootId;

}
