/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.List;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a view object for pass traffic light command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorPostTrafficLightCommandVO extends MonitorRemoteCommandVO {

  /**
   * <p>
   * Represents the id of traffic light. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private List<String> trafficLightId;

  /**
   * <p>
   * Represents the pass or cancel. It's changeable.
   * </p>
   */
  @NotNull
  private Boolean passThrough;

}
