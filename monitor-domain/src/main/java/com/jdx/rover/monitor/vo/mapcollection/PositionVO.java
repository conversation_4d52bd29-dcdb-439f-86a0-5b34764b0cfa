/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:32
 * @description 地理位置，WGS84
 */
@Data
public class PositionVO implements Serializable {

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度ID不能为空")
    private Double longitude;
}
