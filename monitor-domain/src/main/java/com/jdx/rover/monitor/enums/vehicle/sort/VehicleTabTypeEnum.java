/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * TAB页面
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleTabTypeEnum {
  ERROR("ERROR", "失联及异常"),
  NORMAL("NORMAL", "正常"),
  OFFLINE("OFFLINE", "离线"),
  INTERESTED("INTERESTED", "关注"),
  ALL("ALL", "全部"),
  ;

  private final String value;

  private final String title;

  public static VehicleTabTypeEnum of(final String value) {
    if (StringUtils.isBlank(value)) {
      return null;
    }
    for (VehicleTabTypeEnum itemEnum : VehicleTabTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}

