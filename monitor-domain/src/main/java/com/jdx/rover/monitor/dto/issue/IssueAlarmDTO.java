/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.issue;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is the entity for issue alarm record.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmDTO {

  /**
   * <p>
   * Represents the alarm item's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the alarm item's event type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the alarm item's start date. The default value is null. It's changeable.
   * </p>
   */
  private Date startTimestamp;

  /**
   * <p>
   * Represents the alarm item's title. The default value is null. It's changeable.
   * </p>
   */
  private String title;

  /**
   * <p>
   * Represents the alarm item's description. The default value is null. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents whether the alarm event is selected. The default value is null. It's changeable.
   * </p>
   */
  private Boolean isSelected;

  /**
   * <p>
   * Represents the alarm vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the attachment list of the alarm item. The default value is null. It's changeable.
   * </p>
   */
  private List<String> attachmentList;
}
