/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.issue;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 工单来源枚举.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum IssueSourceEnum {
  /**
   * <p>
   * 工单来源.
   * </p>
   */
  OPERATION_ALARM("OPERATION_ALARM", "运营报警"),
  EXCEPTION_REMIND("EXCEPTION_REMIND", "异常提醒"),
  SUPPORT_ALARM("SUPPORT_ALARM", "技术支持报警");

  /**
   * <p>
   * 值.
   * </p>
   */
  private final String value;

  /**
   * <p>
   * 标题.
   * </p>
   */
  private final String title;

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static IssueSourceEnum of(final String value) {
    for (IssueSourceEnum itemEnum : IssueSourceEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
