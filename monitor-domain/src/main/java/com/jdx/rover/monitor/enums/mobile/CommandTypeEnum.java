package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: CommandTypeEnum
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-15 17:41
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum CommandTypeEnum {

    REMOTE_POWER_ON("REMOTE_POWER_ON", "远程开机"), REMOTE_SHUTDOWN("REMOTE_SHUTDOWN", "远程关机"), ROVER_REBOOT("ROVER_REBOOT", "ROVER重启"), VEHICLE_REBOOT("VEHICLE_REBOOT", "断电重启"), ANDROID_REBOOT("ANDROID_REBOOT", "安卓重启"), VIDEO_REBOOT("VIDEO_REBOOT", "视频重启"), MCU_REBOOT("MCU_REBOOT", "域控重启"), LOW_LIGHT_OPEN("LOW_LIGHT_OPEN", "打开近光灯"), LOW_LIGHT_CLOSE("LOW_LIGHT_CLOSE", "关闭近光灯"), LEFT_TURN_LIGHT_OPEN("LEFT_TURN_LIGHT_OPEN", "打开左转向灯"), LEFT_TURN_LIGHT_CLOSE("LEFT_TURN_LIGHT_CLOSE", "关闭左转向灯"), RIGHT_TURN_LIGHT_OPEN("RIGHT_TURN_LIGHT_OPEN", "打开右转向灯"), RIGHT_TURN_LIGHT_CLOSE("RIGHT_TURN_LIGHT_CLOSE", "关闭右转向灯"), FLASH_LIGHT_OPEN("FLASH_LIGHT_OPEN", "打开双闪"), FLASH_LIGHT_CLOSE("FLASH_LIGHT_CLOSE", "关闭双闪"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    /**
     * 获取枚举
     *
     * @param code code
     * @return CommandTypeEnum
     */
    public static CommandTypeEnum of(String code) {
        for (CommandTypeEnum commandTypeEnum : CommandTypeEnum.values()) {
            if (commandTypeEnum.getCode().equals(code)) {
                return commandTypeEnum;
            }
        }
        return null;
    }
}