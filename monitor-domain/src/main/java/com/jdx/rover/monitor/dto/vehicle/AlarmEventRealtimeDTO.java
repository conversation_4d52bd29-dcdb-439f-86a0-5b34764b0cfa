/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 监控单车页
 *
 * <AUTHOR>
 */
@Data
public class AlarmEventRealtimeDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 告警事件
   */
  private String alarmEvent;

  /**
   * 上报时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date reportTime;
}
