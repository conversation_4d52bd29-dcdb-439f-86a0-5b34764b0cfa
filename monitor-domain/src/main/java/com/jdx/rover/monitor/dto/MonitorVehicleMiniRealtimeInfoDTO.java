/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a vehicle mini realtime information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleMiniRealtimeInfoDTO {

 /**
   * <p>
   * Represents the state of the schedule. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the vehicle's alarm. The default value is null. It's changeable.
   * </p>
   */
  private String alarm;

  /**
   * <p>
   * Represents the speed of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private double speed;

    /**
   * <p>
   * Represents the global mileage of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private double globalMileage;

  /**
   * <p>
   * Represents the arrived mileage of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private double arrivedMileage;

}
