/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 远程遥控指令操作方
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RemoteCommandSourceEnum {
  UNKNOWN("UNKNOWN", ""),
  MONITOR("MONITOR", "监控"),
  MINI_MONITOR("MINI_MONITOR", "小程序"),
  WORKBENCH_MONITOR("WORKBENCH_MONITOR", "小哥工作台"),
  REMOTE_JOYSTICK("REMOTE_JOYSTICK", "驾舱")
  ;

  /**
   * 指令操作方
   */
  private String commandSource;

  /**
   * 指令操作方名称
   */
  private String commandSourceName;

  public static RemoteCommandSourceEnum of(String commandSource) {
    for (RemoteCommandSourceEnum em : RemoteCommandSourceEnum.values()) {
      if (Objects.equals(commandSource, em.getCommandSource())) {
        return em;
      }
    }
    return null;
  }

  public static String getCommandSourceName(String commandSource) {
    for (RemoteCommandSourceEnum em : RemoteCommandSourceEnum.values()) {
      if (Objects.equals(commandSource, em.getCommandSource())) {
        return em.getCommandSourceName();
      }
    }
    return null;
  }
}
