/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <p>
 * This is a view object for schedule stop request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopRequestVO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the id of stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the action of the stop. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

}
