/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is a view object for remote control calss.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorRemoteCommandVO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the timeStamp of user operate recovery request. It's changeable.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date timeStamp;

}
