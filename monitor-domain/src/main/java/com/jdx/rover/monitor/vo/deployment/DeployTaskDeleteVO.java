/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.deployment;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/25 10:47
 * @description 勘查任务删除VO
 */
@Data
public class DeployTaskDeleteVO {

    /**
     * 勘查任务ID
     */
    @NotNull(message = "勘查任务ID不能为空")
    private Integer taskId;

    /**
     * 勘查任务状态
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum
     */
    @NotBlank(message = "勘查任务状态不能为空")
    private String taskStatus;
}
