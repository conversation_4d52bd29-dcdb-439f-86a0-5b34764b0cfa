package com.jdx.rover.monitor.dto.transport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description: h5接驳用户操作指令数据传输对象
 * @author: wangguotai
 * @create: 2025-07-21 21:20
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UserOperateCommandDTO {

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作结果：1成功0失败
     */
    private Integer operateResult;
}