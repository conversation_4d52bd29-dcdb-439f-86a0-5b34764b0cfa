package com.jdx.rover.monitor.dto.mobile.message;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 事故操作传输对象
 */
@Data
public class AccidentOperateDTO {

    /**
     * 卡片ID
     */
    @NotNull(message = "卡片id不能为空")
    private Integer messageId;

    /**
     * 事故编号
     */
    @NotBlank(message = "事故编号不能为空")
    private String accidentNo;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String operateType;
}
