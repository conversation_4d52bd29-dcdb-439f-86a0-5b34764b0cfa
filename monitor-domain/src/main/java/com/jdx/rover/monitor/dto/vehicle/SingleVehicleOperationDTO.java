/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 监控单车页操作记录
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleOperationDTO {
  /**
   * 操作时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date operateTime;

  /**
   * 操作人员
   */
  private String operateUserName;

  /**
   * 操作信息
   */
  private String operateInfo;
}
