/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a guardian alarm info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianAbnormalBootModuleDTO {

  /**
   * <p>
   * 启动次数.
   * </p>
   */
  private Integer bootCount;

  /**
   * <p>
   * Represents the boot detail. It's changeable.
   * </p>
   */
  private List<MonitorVehicleAbnormalBootDTO> bootDetail;

}
