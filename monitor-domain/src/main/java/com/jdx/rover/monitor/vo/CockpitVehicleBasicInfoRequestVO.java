/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.vo;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 座席下车辆请求
 * </p>
 *
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class CockpitVehicleBasicInfoRequestVO {

  /**
   * <p>
   * 座席编号
   * </p>
   */
  private String cockpitNumber;

  /**
   * <p>
   * 车辆业务
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * 运营场景
   * </p>
   */
  private List<String> useCaseList;

}
