/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.vehicle.command;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * This is a remote operation type enum.
 * </p>
 *
 * <p>
 * <strong> remote operation type: </strong> enumeration of the class
 * operation type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RemoteOperationTypeEnum {
    /**
     * 操作类型
     */
    NONE("NONE", "无"),
    REMOTE_CONTROL("REMOTE_CONTROL", "远程遥控"),
    EMERGENCY_STOP("EMERGENCY_STOP", "急停"),
    RESTART("RESTART", "重启"),
    RECOVERY("RECOVERY", "急停恢复"),
    AS_ARRIVED("AS_ARRIVED", "视同到达"),
    RETURN("RETURN", ""),
    RESET_ABNORMAL("RESET_ABNORMAL", ""),
    PASS_TRAFFIC_LIGHT("PASS_TRAFFIC_LIGHT", "通过红绿灯"),
    CANCEL_PASS_TRAFFIC_LIGHT("CANCEL_PASS_TRAFFIC_LIGHT", "取消通过红绿灯"),
    REMOTE_DRIVE_ENTER_TAKE_OVER("REMOTE_DRIVE_ENTER_TAKE_OVER", "平行驾驶接管"),
    REMOTE_DRIVE_EXIT_TAKE_OVER("REMOTE_DRIVE_EXIT_TAKE_OVER", "退出平行驾驶接管"),
    SWITCH_VEHICLE_MODE("SWITCH_VEHICLE_MODE", "切换车辆模式"),
    ;


    private final String value;

    private final String title;
}
