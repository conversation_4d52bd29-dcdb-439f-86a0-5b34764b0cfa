/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.vehicle;

import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 车辆状态DO
 *
 * <AUTHOR>
 */
@Data
public class VehicleStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * guardian在线
     */
    private String guardianOnline;

    /**
     * 系统状态
     */
    private String systemState;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 调度状态
     */
    private String scheduleState;

    /**
     * 调度开始时间
     */
    private String scheduleStartTime;

    /**
     * 座舱编号
     */
    private String cockpitNumber;

    /**
     * 驾舱团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 驾舱用户名称
     */
    private String cockpitUserName;

    /**
     * 路口状态
     */
    private String intersectionStatus;

    /**
     * planning状态类型
     */
    private String planningStatusType;

    /**
     * 电量
     */
    private Double power;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 运行地图状态
     */
    private String runMapState;

    /**
     * gps信号
     */
    private String gpsSignal;

    /**
     * 定位置信度
     */
    private String sceneSignal;

    /**
     * 零偏状态
     */
    private String steerZero;

    /**
     * pdu在线
     */
    private String pduOnline;

    /**
     * 车辆采图模式
     *
     * @see CollectionModeEnum
     */
    private String collectionMode;

    /**
     * 车辆采集状态
     *
     * @see CollectionStatusEnum
     */
    private String collectionStatus;

    /**
     * 车辆绑定采集任务
     */
    private Integer taskId;

    /**
     * 采图任务总里程
     */
    private Double taskTotalMileage;

    /**
     * 开启采集/继续采集时间
     */
    private String openCollectionTime;

    /**
     * 上报最大速度值，xgflags，单位：m/s
     */
    private Double reportMaxVelocity;
}