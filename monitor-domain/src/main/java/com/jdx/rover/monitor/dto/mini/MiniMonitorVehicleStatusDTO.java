/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a view object for mini monitor vehicle status response.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorVehicleStatusDTO {
  /**
   * <p>
   * Represents the real-time speed of the vehicle. The default value is 0.0. It's
   * changeable.
   * </p>
   */
  private Double speed;

  /**
   * <p>
   * Represents the power usage of vehicle. The default value is 0. It's
   * changeable.
   * </p>
   */
  private Double power;

  /**
   * <p>
   * Represents the system state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String systemState;

  /**
   * <p>
   * Represents the vehicle state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleState;

  /**
   * <p>
   * Represents the vehicle state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double mileage;

  /**
   * <p>
   * Represents the finished mileage of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Double finishedMileage;

  /**
   * <p>
   * Represents the alarm event of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<MiniMonitorAlarmEventDTO> alarmEvent;

  /**
   * <p>
   * Represents the issue state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String issueState;

  /**
   * <p>
   * Represents the issue no of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the realtime state of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String realtimeState;
}
