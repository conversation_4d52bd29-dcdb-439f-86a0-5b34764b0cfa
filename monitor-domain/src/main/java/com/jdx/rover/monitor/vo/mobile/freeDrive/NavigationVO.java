package com.jdx.rover.monitor.vo.mobile.freeDrive;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @description: NavigationVO
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-16 18:25
 **/
@Data
public class NavigationVO {

    /**
     * 停靠点ID
     */
    @NotNull(message = "停靠点ID不能为空")
    private Integer stopId;

    /**
     * 停靠点名称
     */
    @NotBlank(message = "停靠点名称不能为空")
    private String stopName;

    /**
     * 已选车辆列表
     */
    @NotEmpty(message = "已选车辆列表不能为空")
    private List<String> vehicleNameList;
}