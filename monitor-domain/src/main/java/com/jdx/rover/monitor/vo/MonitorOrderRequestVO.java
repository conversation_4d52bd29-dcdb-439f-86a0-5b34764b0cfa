/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

/**
 * <p>
 * This is a view object for supervisor order request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorOrderRequestVO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the id of stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the id of goal. The default value is null. It's changeable.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represents the type of the stop. The default value is null. It's changeable.
   * </p>
   */
  @Deprecated
  private String stopType;

  /**
   * <p>
   * Represents the action of the stop. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

}
