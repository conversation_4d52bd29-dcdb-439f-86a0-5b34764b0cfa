package com.jdx.rover.monitor.dto.accident;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 事故标签传输对象
 */
@Data
@NoArgsConstructor
public class AccidentTagDTO {

    /**
     * <p>
     * 分类标签
     * </p>
     */
    private Integer value;

    /**
     * <p>
     * 分类值
     * </p>
     */
    private String name;

    /**
     * <p>
     * 子数据分类
     * </p>
     */
    private List<AccidentTagDTO> child;
}
