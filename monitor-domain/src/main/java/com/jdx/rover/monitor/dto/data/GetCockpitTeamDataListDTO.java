package com.jdx.rover.monitor.dto.data;

import lombok.Data;

/**
 * @description: GetCockpitTeamDataListDTO
 * @author: wangguotai
 * @create: 2024-06-20 10:52
 **/
@Data
public class GetCockpitTeamDataListDTO {

    /**
     * 所属团队
     */
    private String cockpitTeamName;

    /**
     * 技术支持数量
     */
    private Integer supportCount;

    /**
     * 监控座席数量
     */
    private Integer monitorCount;

    /**
     * 驾舱座席数量
     */
    private Integer remoteDriveCount;

    /**
     * 响应工单平均用时
     */
    private Integer responseAvgCost;

    /**
     * 处理工单平均用时
     */
    private Integer handleAvgCost;

    /**
     * 在线时间
     */
    private Integer onlineDuration;

    /**
     * 受理工单次数
     */
    private Integer acceptCount;

    /**
     * 监控受理工单次数
     */
    private Integer monitorAcceptCount;

    /**
     * 驾舱受理工单次数
     */
    private Integer remoteDriveAcceptCount;

    /**
     * 转单次数
     */
    private Integer transferCount;

    /**
     * 承接转单待受理次数
     */
    private Integer acceptTransferCount;

    /**
     * 抛单次数
     */
    private Integer discardCount;
}