package com.jdx.rover.monitor.jsf;

import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 业务校验工具
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-02-06 15:47
 **/
public class BusinessCheckUtil {

    /**
     * 检查对象是否为空，若为空则抛出业务异常。
     *
     * @param param         需要检查的对象。
     * @param serverTipEnum 业务异常的提示信息枚举。
     */
    public static void checkNull(Object param, MonitorErrorEnum serverTipEnum) {
        if (Objects.isNull(param)) {
            throw new BusinessException(serverTipEnum.getCode(), serverTipEnum.getMessage());
        }
    }

    /**
     * 检查对象是否为null，如果为null则抛出BusinessException异常。
     *
     * @param param         待检查的对象。
     * @param serverTipEnum 异常信息枚举。
     */
    public static void checkNonNull(Object param, MonitorErrorEnum serverTipEnum) {
        if (Objects.nonNull(param)) {
            throw new BusinessException(serverTipEnum.getCode(), serverTipEnum.getMessage());
        }
    }

    /**
     * 检查集合是否为null或者empty，如果为则抛出BusinessException异常
     *
     * @param collection    待检查的集合。
     * @param serverTipEnum 异常信息枚举。
     */
    public static void checkEmpty(Collection<?> collection, MonitorErrorEnum serverTipEnum) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(serverTipEnum.getCode(), serverTipEnum.getMessage());
        }
    }

    /**
     * 检查集合是否为null或者empty，如果为则抛出BusinessException异常
     *
     * @param collection    待检查的集合。
     * @param serverTipEnum 异常信息枚举。
     */
    public static void checkEmpty(Map<?, ?> collection, MonitorErrorEnum serverTipEnum) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(serverTipEnum.getCode(), serverTipEnum.getMessage());
        }
    }
}