/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.tnta;

import lombok.Data;

/**
 * <p>
 * This is a impassable area utm data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data

public class ImpassableAreaUtmDTO {
  /**
   * <p>
   * 响应码
   * </p>
   */
  private String stateCode;

  /**
   * <p>
   * 坐标方位
   * </p>
   */
  private String locations;

  /**
   * <p>
   * 坐标系
   * </p>
   */
  private String utmZone;

}
