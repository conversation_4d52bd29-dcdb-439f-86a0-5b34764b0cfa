/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:43
 * @description 线路类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum TaskRouteTypeEnum {
    SETOUT("SETOUT", "去程"),
    RETURN("RETURN", "返程"),
    UNKNOWN("UNKNOWN", "未知")
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 根据taskRouteType获取枚举
     *
     * @param taskRouteType taskRouteType
     * @return TaskRouteTypeEnum
     */
    public static TaskRouteTypeEnum of(String taskRouteType) {
        for (TaskRouteTypeEnum em : TaskRouteTypeEnum.values()) {
            if (em.getCode().equals(taskRouteType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
