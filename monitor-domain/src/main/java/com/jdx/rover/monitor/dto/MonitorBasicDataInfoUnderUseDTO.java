/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * This is a monitor station basic data under user transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorBasicDataInfoUnderUseDTO {

  /**
   * <p>
   * Represents the basic data info id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the basic data info name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the child data info. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorBasicDataInfoUnderUseDTO> child = new ArrayList<>();

}
