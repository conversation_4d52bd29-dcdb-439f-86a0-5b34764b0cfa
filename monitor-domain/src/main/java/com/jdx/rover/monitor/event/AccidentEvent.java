package com.jdx.rover.monitor.event;

import com.jdx.rover.monitor.po.Accident;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * 事故发布事件
 */
@Setter
@Getter
@ToString
public class AccidentEvent extends ApplicationEvent {

    private Accident accident;

    public AccidentEvent(Object source, Accident accident) {
        super(source);
        this.accident = accident;
    }
}
