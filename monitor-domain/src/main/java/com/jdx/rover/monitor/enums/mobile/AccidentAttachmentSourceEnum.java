package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故信息来源枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentAttachmentSourceEnum {

    OPERATION("OPERATION", "一线运营"),
    TECHNICAL_SUPPORT("TECHNICAL_SUPPORT", "技术支持"),
    SAFETY_GROUP("SAFETY_GROUP", "安全组"),
    VIDEO_SNAPSHOT("VIDEO_SNAPSHOT", "视频快照")
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentAttachmentSourceEnum accidentAttachmentSourceEnum : AccidentAttachmentSourceEnum.values()) {
            if (accidentAttachmentSourceEnum.getValue().equals(value)) {
                return accidentAttachmentSourceEnum.getName();
            }
        }
        return null;
    }
}
