package com.jdx.rover.monitor.vo.transport;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @description: 车辆遥控-获取车辆选择列表-请求对象
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-02-06 17:22
 **/
@Data
public class GetSelectVehicleListVO {

    /**
     * 经度
     */
    @NotNull(message = "longitude不能为空")
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull(message = "latitude不能为空")
    private Double latitude;
}