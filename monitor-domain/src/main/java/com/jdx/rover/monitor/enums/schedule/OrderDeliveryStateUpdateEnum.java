/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.schedule;

import com.jdx.rover.monitor.entity.ScheduleOrderEntity;
import com.jdx.rover.schedule.api.domain.enums.DeliveryStatus;
import com.jdx.rover.schedule.api.domain.enums.LoadMethodEnum;
import com.jdx.rover.schedule.api.domain.enums.TaskType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * 订单配送状态执行更新枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum OrderDeliveryStateUpdateEnum {
  NOTASK_COMMON(TaskType.NOTASK.getTaskType(), DeliveryStatus.COMPLETED.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(), schedule -> schedule.updateTotalDeliveryOrderNum(0)),

  LOAD_SUCCESS(TaskType.LOADTASK.getTaskType(), DeliveryStatus.ALREADY.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(1)),
  LOAD_DELETE(TaskType.LOADTASK.getTaskType(), DeliveryStatus.DELETE.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(-1)),
  LOAD_CANCEL(TaskType.LOADTASK.getTaskType(), DeliveryStatus.CANCEL.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateCanceledDeliveryOrderNum(1)),
  LOAD_USER_CANCEL(TaskType.LOADTASK.getTaskType(), DeliveryStatus.USERCANCEL.getDeliveryStatus(), null,schedule -> schedule.updateCanceledDeliveryOrderNum(1)),

  DELIVERY_LOAD_SUCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.ALREADY.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(1)),
  DELIVERY_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.COMPLETED.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(1)),
  DELIVERY_HALFWAY_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.COMPLETED.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(1)),
  DELIVERY_DELETE_ORIGINATE(TaskType.DELIVERY.getTaskType(), DeliveryStatus.DELETE.getDeliveryStatus(),  LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(-1)),
  DELIVERY_DELETE_HALF(TaskType.DELIVERY.getTaskType(), DeliveryStatus.DELETE.getDeliveryStatus(),  LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(-1)),
  DELIVERY_CANCEL(TaskType.DELIVERY.getTaskType(), DeliveryStatus.CANCEL.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateCanceledDeliveryOrderNum(1)),
  DELIVERY_ALREADY(TaskType.DELIVERY.getTaskType(), DeliveryStatus.ALREADY.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(1)),
  DELIVERY_DROPOFF_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.USERDROPPEDOFF.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedCollectOrderNum(1)),
  DELIVERY_DROPOFF_CANCEL(TaskType.DELIVERY.getTaskType(), DeliveryStatus.CANCELDROPOFF.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(), schedule -> schedule.updateCanceledCollectOrderNum(1)),
  DELIVERY_ASSIGNED_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.ASSIGNED.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),
  DELIVERY_ASSIGNED_HALFWAY_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.ASSIGNED.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),
  DELIVERY_HALFWAY_DROPOFF_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.USERDROPPEDOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateFinishedCollectOrderNum(1)),
  DELIVERY_HALFWAY_DROPOFF_CANCEL(TaskType.DELIVERY.getTaskType(), DeliveryStatus.CANCELDROPOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(), schedule -> schedule.updateCanceledCollectOrderNum(1)),
  DELIVERY_HALFWAY_TODROPOFF_SUCCESS(TaskType.DELIVERY.getTaskType(), DeliveryStatus.TODROPOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),

  UNLOAD_SUCCESS(TaskType.UNLOADTASK.getTaskType(), DeliveryStatus.UNLOAD.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(1)),
  UNLOAD_DELIVERY_SUCCESS(TaskType.UNLOADTASK.getTaskType(), DeliveryStatus.COMPLETED.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(-1)),

  DROPOFF_ASSIGN(TaskType.DROPOFF.getTaskType(), DeliveryStatus.ASSIGNED.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),
  DROPOFF_HALFWAY_ASSIGN(TaskType.DROPOFF.getTaskType(), DeliveryStatus.ASSIGNED.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),
  DROPOFF_SUCCESS(TaskType.DROPOFF.getTaskType(), DeliveryStatus.USERDROPPEDOFF.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(),schedule -> schedule.updateFinishedCollectOrderNum(1)),
  DROPOFF_CANCEL(TaskType.DROPOFF.getTaskType(), DeliveryStatus.CANCELDROPOFF.getDeliveryStatus(), LoadMethodEnum.ORIGINATE.getType(), schedule -> schedule.updateCanceledCollectOrderNum(1)),
  HALFWAY_DROPOFF_SUCCESS(TaskType.DROPOFF.getTaskType(), DeliveryStatus.USERDROPPEDOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateFinishedCollectOrderNum(1)),
  HALFWAY_DROPOFF_CANCEL(TaskType.DROPOFF.getTaskType(), DeliveryStatus.CANCELDROPOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(), schedule -> schedule.updateCanceledCollectOrderNum(1)),
  DROPOFF_TODROPOFF_SUCCESS(TaskType.DROPOFF.getTaskType(), DeliveryStatus.TODROPOFF.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalCollectOrderNum(1)),
  DROPOFF_HALFWAY_ALREADY(TaskType.DROPOFF.getTaskType(), DeliveryStatus.ALREADY.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(1)),
  DROPOFF_HALFWAY_SUCCESS(TaskType.DROPOFF.getTaskType(), DeliveryStatus.COMPLETED.getDeliveryStatus(), LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateFinishedDeliveryOrderNum(1)),
  DROPOFF_DELETE_HALF(TaskType.DROPOFF.getTaskType(), DeliveryStatus.DELETE.getDeliveryStatus(),  LoadMethodEnum.HALFWAY.getType(),schedule -> schedule.updateTotalDeliveryOrderNum(-1)),
  ;

  private final String taskType;

  private final String deliveryStatus;

  private final String loadMethod;

  private final Function<ScheduleOrderEntity, ScheduleOrderEntity> function;

  public static OrderDeliveryStateUpdateEnum valueOf(String taskType, String deliveryStatus, String loadMethod) {
    for (OrderDeliveryStateUpdateEnum em : OrderDeliveryStateUpdateEnum.values()) {
      if (StringUtils.equals(em.getTaskType(), taskType) &&
              StringUtils.equals(em.getDeliveryStatus(), deliveryStatus)
              && StringUtils.equals(em.getLoadMethod(), loadMethod)) {
        return em;
      }
    }
    return OrderDeliveryStateUpdateEnum.NOTASK_COMMON;
  }

}

