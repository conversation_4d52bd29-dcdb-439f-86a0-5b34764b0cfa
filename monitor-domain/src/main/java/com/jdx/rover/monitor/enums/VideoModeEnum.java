/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.*;

import java.util.Objects;

/**
 * <p>
 * 视频模式枚举
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/12
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VideoModeEnum {
  /**
   * 枚举类型
   */
  STANDARD("STANDARD", "标准"),
  /**
   * 高清
   */
  HIGH("HIGH", "高清"),
  /**
   * 流畅
   */
  SMOOTH("SMOOTH", "流畅"),
  /**
   * 极速
   */
  ULTRAFAST("ULTRAFAST", "极速");

  /**
   * <p>
   * 视频模式类型
   * </p>
   */
  private String value;

  /**
   * <p>
   * 模式名
   * </p>
   */
  private String name;

  public static VideoModeEnum of(String value) {
    for (VideoModeEnum em : VideoModeEnum.values()) {
      if (Objects.equals(value, em.getValue())) {
        return em;
      }
    }
    return VideoModeEnum.STANDARD;
  }
}
