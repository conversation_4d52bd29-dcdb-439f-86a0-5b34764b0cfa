package com.jdx.rover.monitor.vo.transport;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @description: 呼叫远驾-请求对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2025-02-06 16:50
 **/
@Data
public class CallCockpitVO {

    /**
     * 问题车辆
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 备注
     */
    private String remark;
}