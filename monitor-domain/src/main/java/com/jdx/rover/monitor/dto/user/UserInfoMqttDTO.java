package com.jdx.rover.monitor.dto.user;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11
 */
@Data
public class UserInfoMqttDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 今日历史工作时长(单位秒)
     */
    private Integer workTimeTotal;

    /**
     * 当次工作开始时间
     */
    private Date workStartTime;

    /**
     * 处理工单数
     */
    private Integer completeIssueCount;

    /**
     * 坐席模式(自由/听单/盯单/巡查)
     */
    private String cockpitMode;

    /**
     * 坐席状态
     */
    private String cockpitStatus;

    /**
     * 座席编号
     */
    private String cockpitNumber;

    /**
     * 坐席类型(监控座席/驾舱座席)
     */
    private String cockpitType;

    /**
     * 上次座席编号
     */
    private String lastCockpitNumber;

    /**
     * 接单状态
     */
    private String acceptIssueStatus;

    /**
     * 记录时间
     */
    private Date recordTime;
}