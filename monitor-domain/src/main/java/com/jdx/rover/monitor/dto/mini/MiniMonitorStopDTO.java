/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import lombok.Data;

/**
 * <p>
 * This is a mini-monitor stop info data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorStopDTO {

  /**
   * <p>
   * Represents the stop's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the stop's number. The default value is null. It's changeable.
   * </p>
   */
  private String number;

  /**
   * <p>
   * Represents the stop's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the stop's lat. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the stop's lon. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the stop's type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the stop's action type. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

  /**
   * <p>
   * Represents stop travel status. The default value is null. It's changeable.
   * </p>
   */
  private String travelStatus;

  /**
   * <p>
   * Represent the arrived timestamp. The default value is 0. It's changeable. The unit is
   * millisecond.
   * </p>
   */
  private Long arrivedTimestamp;

  /**
   * <p>
   * Represent the depart timestamp. The default value is 0. It's changeable. The unit is
   * millisecond.
   * </p>
   */
  private Long departTimestamp;

  /**
   * <p>
   * Represent the est depart timestamp. The default value is 0. It's changeable. The unit is
   * millisecond.
   * </p>
   */
  private Long estDepartTimestamp;

  /**
   * <p>
   * Represent the schedule goal start timestamp. The default value is 0. It's changeable. The unit is
   * millisecond.
   * </p>
   */
  private Long startTimestamp;

  /**
   * <p>
   * Represent the waiting duration of the stop. The default value is 0. It's changeable. The unit
   * is minute.
   * </p>
   */
  private Integer waitingTime;

}
