/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import java.util.Date;

import com.jdx.rover.monitor.dto.issue.IssueOperateHistoryDTO;

import lombok.Data;

/**
 * <p>
 * This is a model class of issue operate record.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueOperateHistory extends BaseDomain {

  /**
   * <p>
   * The issue no. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * The vehicle alarm id. The default value is null. It's changeable.
   * </p>
   */
  private Integer vehicleAlarmId;

  /**
   * <p>
   * The timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * <p>
   * The user name for creating. The default value is null. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * The title for creating. The default value is null. It's changeable.
   * </p>
   */
  private String title;

  /**
   * <p>
   * The message for creating. The default value is null. It's changeable.
   * </p>
   */
  private String message;

  /**
   * <p>
   * This method helps to convert model of VehicleAlarmRecord to VehicleAlarmRecordDto.
   * </p>
   * 
   * @return The data transfer object for vehicle alarm record.
   */
  public IssueOperateHistoryDTO toIssueOperateHistoryDto() {
    IssueOperateHistoryDTO issueOperateHistoryDto = new IssueOperateHistoryDTO();
    issueOperateHistoryDto.setOperateTitle(title);
    issueOperateHistoryDto.setOperateTime(timestamp);
    issueOperateHistoryDto.setOperateUser(userName);
    issueOperateHistoryDto.setOperateMsg(message);
    issueOperateHistoryDto.setAlarmId(vehicleAlarmId);
    return issueOperateHistoryDto;
  }
}
