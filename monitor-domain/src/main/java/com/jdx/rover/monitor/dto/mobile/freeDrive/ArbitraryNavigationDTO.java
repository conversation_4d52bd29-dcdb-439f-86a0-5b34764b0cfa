package com.jdx.rover.monitor.dto.mobile.freeDrive;

import lombok.Data;

import java.util.List;

/**
 * @description: ArbitraryNavigationDTO
 * @author: wangguotai
 * @create: 2024-07-16 18:25
 **/
@Data
public class ArbitraryNavigationDTO {

    /**
     * 任意点名称
     */
    private String stopName;

    /**
     * 任意点经度
     */
    private Double longitude;

    /**
     * 任意点纬度
     */
    private Double latitude;

    /**
     * 任意点朝向
     */
    private Double heading;

    /**
     * 失败车辆列表
     */
    private List<String> vehicleNameList;
}
