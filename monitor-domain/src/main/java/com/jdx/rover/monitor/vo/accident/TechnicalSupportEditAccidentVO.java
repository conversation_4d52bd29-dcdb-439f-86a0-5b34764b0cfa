package com.jdx.rover.monitor.vo.accident;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 技术支持编辑事故
 */
@Data
public class TechnicalSupportEditAccidentVO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 事故分类
     */
    @NotBlank(message = "事故分类不能为空")
    private String technicalSupportAccidentType;

    /**
     * 事故等级
     */
    @NotBlank(message = "事故等级不能为空")
    private String technicalSupportAccidentLevel;

    /**
     * 事故描述
     */
    private String technicalSupportDescription;

    /**
     * 事故凭证
     */
    private List<MonitorAccidentAttachmentVO> technicalSupportAttmentList;

    /**
     * 是否发送消息卡片
     */
    private boolean sendCard;
}
