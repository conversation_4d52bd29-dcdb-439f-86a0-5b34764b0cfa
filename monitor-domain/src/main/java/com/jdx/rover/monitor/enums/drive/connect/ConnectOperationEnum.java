/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.drive.connect;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 连接操作枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-02-19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ConnectOperationEnum {
  /**
   * 连接操作枚举
   */
  CONNECT("创建连接"),
  DISCONNECT("释放连接"),
  ;

  /**
   * 标题
   */
  private String title;
}