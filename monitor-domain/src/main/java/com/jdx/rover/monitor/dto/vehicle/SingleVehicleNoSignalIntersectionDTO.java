/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 通过无信号路口状态
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleNoSignalIntersectionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 记录时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 左转状态
     */
    private String status;

    /**
     * 左转线段ID
     */
    private List<String> groupLaneIdList;
}
