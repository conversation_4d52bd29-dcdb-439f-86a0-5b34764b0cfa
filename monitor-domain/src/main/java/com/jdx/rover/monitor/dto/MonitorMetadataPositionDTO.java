/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * 返回基础主数据的位置（站点、停靠点、车辆）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorMetadataPositionDTO {

  /**
   * <p>
   * 纬度
   * </p>
   */
  private Double latitude;

  /**
   * <p>
   * 经度
   * </p>
   */
  private Double longitude;

  /**
   * <p>
   * 朝向
   * </p>
   */
  private Double heading;

  /**
   * <p>
   * 偏转弧度
   * </p>
   */
  private Double radian;

}
