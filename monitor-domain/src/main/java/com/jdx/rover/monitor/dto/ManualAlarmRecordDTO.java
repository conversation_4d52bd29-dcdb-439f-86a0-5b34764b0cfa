/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 人工告警记录
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ManualAlarmRecordDTO {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 提报来源
   * </p>
   */
  private String source;

  /**
   * <p>
   * 提报用户
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * 提报时间
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date reportTime;

  /**
   * <p>
   * 描述
   * </p>
   */
  private String description;

  /**
   * <p>
   * 模糊手机号
   * </p>
   */
  private String phone;

}