/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.alarm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机动车道行驶告警缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class DrivingLaneAlarmDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 记录时间
   */
  private Date recordTime;

}
