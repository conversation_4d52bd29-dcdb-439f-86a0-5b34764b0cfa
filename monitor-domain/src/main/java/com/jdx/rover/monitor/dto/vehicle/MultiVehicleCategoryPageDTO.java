/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import java.util.List;
import lombok.Data;

/**
 * 监控多车页
 *
 * <AUTHOR>
 */
@Data
public class MultiVehicleCategoryPageDTO {

  /**
   * 当前页码(当前第几页)
   */
  private Integer pageNum;

  /**
   * 每页的数量(每页最多条数)
   */
  private Integer pageSize;

  /**
   * 总页数
   */
  private Integer pages;

  /**
   * 总记录数
   */
  private Integer total;

  /**
   * 分组车辆列表
   */
  private List<MultiVehicleDTO> vehicleList;
}
