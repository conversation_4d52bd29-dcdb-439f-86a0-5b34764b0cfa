///***************************************************************************
// *
// * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
// *
// **************************************************************************/
//package com.jdx.rover.monitor.po;
//
//import com.jdx.rover.monitor.base.BaseDomain;
//import lombok.Data;
//
//import java.util.Date;
//
///**
// * <p>
// * 事故信息
// * </p>
// *
// * <AUTHOR>
// * @date 2023/06/13
// */
//@Data
//public class AccidentInfo extends BaseDomain {
//
//    /**
//     * 事故分类
//     * @see com.jdx.rover.monitor.enums.accident.AccidentTypeEnum
//     */
//    private String accidentType;
//
//    /**
//     * 事故创建来源
//     * @see com.jdx.rover.monitor.enums.accident.AccidentSourceEnum
//     */
//    private String accidentSource;
//
//    /**
//     * 事故编号
//     */
//    private String accidentNo;
//
//    /**
//     * 事故描述
//     */
//    private String accidentDesc;
//
//    /**
//     * 事故发生时间 确定发生时间
//     */
//    private Date accidentTime;
//
//    /**
//     * 事故发生时间 不确定发生时间 使用时间段
//     */
//    private Date accidentStartTime;
//    private Date accidentEndTime;
//
//    /**
//     * 车辆名称
//     */
//    private String vehicleName;
//
//    /**
//     * 事故排查状态
//     * @see com.jdx.rover.monitor.enums.accident.AccidentStatusEnum
//     */
//    private String accidentStatus;
//
//    /**
//     * 提报的jira号
//     */
//    private String jiraNo;
//
//    /**
//     * xata 平台任务ID
//     */
//    private String xataTaskId;
//
//    /**
//     * 影子系统时间id
//     */
//    private String shadowEventNo;
//
//    /**
//     * 创建人
//     */
//    private String createdUser;
//
//    /**
//     * 事故提报时 rover 版本
//     */
//    private String roverVersion;
//
//    /**
//     * 事故提报时车辆所属站点名称
//     */
//    private String stationName;
//
//    /**
//     * 事故提报时车辆所属城市名称
//     */
//    private String cityName;
//
//    /**
//     * 最后修改者
//     */
//    private String modifiedUser;
//}
