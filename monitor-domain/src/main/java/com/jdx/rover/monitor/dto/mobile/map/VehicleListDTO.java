package com.jdx.rover.monitor.dto.mobile.map;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/17 10:39
 * @description 车辆列表信息
 */
@Data
public class VehicleListDTO {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 系统状态
     * @see com.jdx.rover.monitor.enums.mobile.SystemStatusEnum
     */
    private String systemStatus;

    /**
     * 任务状态
     */
    private Boolean businessStatus = false;

    /**
     * 车辆保单是否生效
     */
    private Boolean insuranceEffective = false;
}
