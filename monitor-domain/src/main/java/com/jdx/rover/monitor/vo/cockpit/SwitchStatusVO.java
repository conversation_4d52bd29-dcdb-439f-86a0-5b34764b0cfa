package com.jdx.rover.monitor.vo.cockpit;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @description: 切换状态VO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-06-11 19:56
 **/
@Data
public class SwitchStatusVO {

    /**
     * 座席编号
     */
    @NotBlank(message = "座席编号不能为空")
    private String cockpitNumber;

    /**
     * 座席状态
     */
    @NotBlank(message = "座席状态不能为空")
    private String cockpitStatus;

    /**
     * 接单状态
     */
    @NotBlank(message = "接单状态不能为空")
    private String acceptIssueStatus;
}