/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.deployment;

import com.jdx.rover.monitor.dto.mapcollection.PointInfoDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:43
 * @description 获取部署地图勘查任务列表
 */
@Data
public class DeployTaskListDTO {

    /**
     * 勘查任务列表
     */
    private List<DeployTaskInfoDTO> taskList;

    /**
     * 点位列表
     */
    private List<PointInfoDTO> pointList;

    /**
     * 地图元素聚焦纬度
     */
    private Double elementLat;

    /**
     * 地图元素聚焦经度
     */
    private Double elementLon;

    /**
     * 高精地图ID
     */
    private Integer mapId;

    /**
     * 高精地图版本
     */
    private Integer mapVersion;
}
