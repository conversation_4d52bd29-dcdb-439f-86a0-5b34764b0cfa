package com.jdx.rover.monitor.dto.transport;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * @description: 呼叫远驾-获取车辆列表-响应对象
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-02-06 14:47
 **/
@Data
public class GetCallCockpitVehicleListDTO {

    /**
     * 问题车辆列表
     */
    private List<String> vehicleNameList = Collections.emptyList();

    /**
     * 快捷备注列表
     */
    private List<String> phraseList = Collections.emptyList();
}