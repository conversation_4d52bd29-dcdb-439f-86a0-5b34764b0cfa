/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.command;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 操作状态
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RemoteOperationStateEnum {
  INIT("INIT", "初始化"),
  RUNNING("RUNNING", "执行中"),
  SUCCESS("SUCCESS", "成功"),
  FAIL("FAIL", "失败"),
  ;

  private final String value;

  private final String title;

  public static RemoteOperationStateEnum of(final String value) {
    for (RemoteOperationStateEnum em : RemoteOperationStateEnum.values()) {
      if (Objects.equals(em.getValue(), value)) {
        return em;
      }
    }
    return null;
  }
}
