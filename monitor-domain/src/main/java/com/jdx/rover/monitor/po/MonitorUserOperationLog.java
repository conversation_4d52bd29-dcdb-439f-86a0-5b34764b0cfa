/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a monitor user operation log entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserOperationLog extends BaseDomain {

  /**
   * <p>
   * Represents the vehicle entity of monitor user operation log. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the user entity of monitor user operation log. The default value is
   * CURRENT_TIMESTAMP. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the city entity of monitor user operation log. It's changeable.
   * </p>
   */
  private Integer cityId;

  /**
   * <p>
   * Represents the city entity of monitor user operation log. It's changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station entity of monitor user operation log. It's changeable.
   * </p>
   */
  private Integer stationId;

  /**
   * <p>
   * Represents the station entity of monitor user operation log. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the operation type of monitor user operation log. It's changeable.
   * </p>
   */
  private String operationType;

  /**
   * <p>
   * Represents the operation source of user operation log. It's changeable.
   * </p>
   */
  private String operationSource;

  /**
   * <p>
   * 调度状态
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the operation message of monitor user operation log. It's changeable.
   * </p>
   */
  private String operationMessage;

  /**
   * <p>
   * Represents the operate timestamp of monitor user operation log. The default value is
   * CURRENT_TIMESTAMP. It's changeable.
   * </p>
   */
  private Date operateTimestamp;

  /**
   * 请求ID
   */
  private String requestId;

  /**
   * 操作状态(INIT,RUNNING,SUCCESS,FAIL)
   */
  private String state;

  /**
   * 操作失败消息
   */
  private String failMessage;

  /**
   * <p>
   * convert the operation record from model to dto.
   * </p>
   * 
   */
  public MonitorOperationRecordDTO toOperationRecordDto() {
    MonitorOperationRecordDTO monitorOperationRecordDto = new MonitorOperationRecordDTO();
    monitorOperationRecordDto.setId(getId());
    monitorOperationRecordDto.setOperateTime(operateTimestamp);
    monitorOperationRecordDto.setCityName(cityName);
    monitorOperationRecordDto.setStationName(stationName);
    monitorOperationRecordDto.setVehicleName(vehicleName);
    monitorOperationRecordDto.setOperateUserName(userName);
    monitorOperationRecordDto.setOperateInfo(operationMessage);
    monitorOperationRecordDto.setState(state);
    monitorOperationRecordDto.setFailMessage(failMessage);
    return monitorOperationRecordDto;
  }
}
