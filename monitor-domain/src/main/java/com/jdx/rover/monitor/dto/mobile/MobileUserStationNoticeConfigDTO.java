/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.mobile;

import lombok.Data;

/**
 * <p>
 * 运营端用户关注站点碰撞列表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MobileUserStationNoticeConfigDTO {

  /**
   * <p>
   * 站点Id
   * </p>
   */
  private Integer stationId;

  /**
   * <p>
   * 站点名
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * 当前是否关注
   * </p>
   */
  private boolean opened = false;

  /**
   * <p>
   * 当前是否本人站点负责人
   * </p>
   */
  private boolean owner;

}
