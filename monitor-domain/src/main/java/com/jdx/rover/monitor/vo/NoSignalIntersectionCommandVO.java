/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 车辆无信号路口VO
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@Data
public class NoSignalIntersectionCommandVO extends MonitorRemoteCommandVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 左转线段ID
     */
    @Size(min = 1, message = "必须至少有一条路径")
    private List<String> groupLaneIdList;
}
