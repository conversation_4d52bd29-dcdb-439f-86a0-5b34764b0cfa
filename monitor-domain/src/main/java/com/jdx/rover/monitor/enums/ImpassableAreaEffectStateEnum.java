/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 不可通行区状态
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ImpassableAreaEffectStateEnum {

  /**
   * <p>
   * The enumerate effect state.
   * </p>
   */
  ENABLE("ENABLE ", 1, "启用"),
  DISABLE("DISABLE", 0, "停用");

  /**
   * <p>
   * The type of effect.
   * </p>
   */
  private final String type;

  /**
   * <p>
   * The value of effect.
   * </p>
   */
  private final Integer value;

  /**
   * <p>
   * The type name of effect.
   * </p>
   */
  private final String name;

  /**
   * <p>
   * 依据type获取enum
   * </p>
   */
  public static ImpassableAreaEffectStateEnum getByType(final String type) {
    for (ImpassableAreaEffectStateEnum itemEnum : ImpassableAreaEffectStateEnum.values()) {
      if (itemEnum.type.equals(type)) {
        return itemEnum;
      }
    }
    return null;
  }

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static ImpassableAreaEffectStateEnum getByValue(final String value) {
    for (ImpassableAreaEffectStateEnum itemEnum : ImpassableAreaEffectStateEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
