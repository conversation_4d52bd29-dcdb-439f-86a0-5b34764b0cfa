package com.jdx.rover.monitor.vo.mobile.login;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/16 22:11
 * @description 车端远程登录VO
 */
@Data
public class RemoteLoginVO {

    /**
     * 车辆名称
     */
    @NotBlank(message = "车辆名称不能为空")
    private String vehicleName;

    /**
     * 登录人手机号
     */
    @NotBlank(message = "登录人手机号不能为空")
    private String contact;
}
