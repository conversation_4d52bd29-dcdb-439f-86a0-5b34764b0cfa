/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.enums.accident;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 事故排查状态枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AccidentStatusEnum {

    /**
     * 待排查
     */
    WAIT("WAIT","待排查"),
    EFFICIENT("EFFICIENT","有效事故"),
    INVALID("INVALID","无效事故"),
    FALSE_ALARM("FALSE_ALARM","误报问题"),
    ;
    /**
     * 值.
     */
    private final String value;

    /**
     * 标题.
     */
    private final String title;
}
