package com.jdx.rover.monitor.entity.pda;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * pda在线状态
 *
 * <AUTHOR>
 */
@Data
public class PdaGroupOnlineStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分组编号
     */
    private String groupNo;

    /**
     * 离线数
     */
    private Integer offline = 0;

    /**
     * 在线数
     */
    private Integer online = 0;

    /**
     * 记录时间
     */
    private Date recordTime;
}
