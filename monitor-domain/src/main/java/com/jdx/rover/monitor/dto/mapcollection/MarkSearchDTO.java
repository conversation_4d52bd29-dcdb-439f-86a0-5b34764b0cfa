/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:09
 * @description 获取标记列表
 */
@Data
public class MarkSearchDTO implements Serializable {

    /**
     * 标记列表
     */
    private List<MarkDTO> markList;


    @Data
    public static class MarkDTO implements Serializable {

        /**
         * 标记ID
         */
        private Integer markId;

        /**
         * 标记类型
         */
        private String markType;

        /**
         * 纬度
         */
        private Double latitude;

        /**
         * 经度
         */
        private Double longitude;

        /**
         * 地址名称
         */
        private String addressName;

        /**
         * 备注
         */
        private String remark;

        /**
         * 附件列表
         */
        private List<AttachmentDTO> attachmentList;
    }

    @Data
    public static class AttachmentDTO implements Serializable {

        /**
         * 文件类型
         */
        private String type;

        /**
         * 文件key
         */
        private String fileKey;

        /**
         * 文件url
         */
        private String url;
    }
}
