/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @description: 完成采集请求对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-12-19 16:17
 **/
@Data
public class FinishCollectionVO {

    /**
     * 驾舱编号
     */
    @NotBlank(message = "cockpitNumber不能为空")
    private String cockpitNumber;

    /**
     * 车号
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 采集任务id
     */
    @NotNull(message = "taskId不能为空")
    private Integer taskId;
}