/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.vo.accident;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 监控事故查询入参
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Data
public class MonitorAccidentSearchVO extends PageVO {

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 事故排查状态
     */
    private List<String> accidentStatus;

    /**
     * 事故分类
     */
    private List<String> accidentType;

    /**
     * 创建人
     */
    private String createdUser;
}
