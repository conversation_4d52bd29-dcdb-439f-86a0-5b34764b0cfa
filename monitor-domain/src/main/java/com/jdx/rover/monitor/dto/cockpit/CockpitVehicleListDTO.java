/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import com.jdx.rover.metadata.api.domain.dto.vehicle.VehicleStationInfoDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 座席下车辆列表
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@NoArgsConstructor
public class CockpitVehicleListDTO {

    /**
     * 车辆信息列表
     */
    private List<Vehicle> content;

    @Data
    public static class Vehicle {

        /**
         * 车辆名称
         */
        private String vehicleName;

        /**
         * 站点编号
         */
        private String stationNumber;

        /**
         * 站点名称
         */
        private String stationName;

        /**
         * 城市编号
         */
        private String cityNumber;

        /**
         * 城市名称
         */
        private String cityName;

        /**
         * 车辆名称列表
         */
        private List<String> vehicleList;
    }

    /**
     * 构造入参
     *
     * @param data 主数据信息
     */
    public CockpitVehicleListDTO(List<VehicleStationInfoDTO> data) {
        if (data == null || data.size() == 0) {
            this.content = Collections.emptyList();
            return;
        }

        //数据根据站点进行分组
        Map<String, List<VehicleStationInfoDTO>> collect = data.stream().collect(Collectors.groupingBy(VehicleStationInfoDTO::getStationNumber));

        //初始化响应数据集合，并指定容量
        this.content = new ArrayList<>(collect.size());

        //参数封装
        collect.entrySet().forEach(entry -> {
            Vehicle vehicle = new Vehicle();
            VehicleStationInfoDTO vehicleInfo = entry.getValue().get(0);
            vehicle.setVehicleName(vehicleInfo.getVehicleName());
            vehicle.setCityName(vehicleInfo.getAddressInfo().getCityName());
            vehicle.setStationName(vehicleInfo.getStationName());
            vehicle.setStationNumber(vehicleInfo.getStationNumber());
            vehicle.setVehicleList(entry.getValue().stream().map(VehicleStationInfoDTO::getVehicleName).collect(Collectors.toList()));
            this.content.add(vehicle);
        });
    }
}
