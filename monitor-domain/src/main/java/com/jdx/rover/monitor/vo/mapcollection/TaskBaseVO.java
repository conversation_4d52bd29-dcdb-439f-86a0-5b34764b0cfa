/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:29
 * @description 勘查任务基础VO
 */
@Data
public class TaskBaseVO implements Serializable {

    /**
     * 勘查任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;
}
