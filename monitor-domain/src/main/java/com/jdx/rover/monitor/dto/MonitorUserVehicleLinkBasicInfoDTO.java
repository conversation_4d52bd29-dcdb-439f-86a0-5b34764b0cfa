/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.Map;
import lombok.Data;

/**
 * <p>
 * This is a basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserVehicleLinkBasicInfoDTO {

  /**
   * <p>
   * Represents the city list under user. The default value is null. It's changeable.
   * </p>
   */
  private Map<Integer, MonitorCityBasicInfoDTO> city;

  /**
   * <p>
   * Represents the num of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Integer allVehicleCount;

  /**
   * <p>
   * Represents the num of dispatch vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Integer dispatchVehicleCount;

  /**
   * <p>
   * Represents the num of vending vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Integer vendingVehicleCount;

}
