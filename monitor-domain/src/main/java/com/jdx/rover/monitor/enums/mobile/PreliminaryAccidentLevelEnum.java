package com.jdx.rover.monitor.enums.mobile;

import com.jdx.rover.monitor.enums.accident.AccidentTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 初判事故等级枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum PreliminaryAccidentLevelEnum {

    NO_FAULT("NO_FAULT", "无责"),
    LOW_RISK("LOW_RISK", "低风险"),
    MEDIUM_RISK("MEDIUM_RISK", "中风险"),
    HIGH_RISK("HIGH_RISK", "高风险");

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (PreliminaryAccidentLevelEnum preliminaryAccidentLevelEnum : PreliminaryAccidentLevelEnum.values()) {
            if (preliminaryAccidentLevelEnum.getValue().equals(value)) {
                return preliminaryAccidentLevelEnum.getName();
            }
        }
        return null;
    }


    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getValueByName(String name) {
        for (PreliminaryAccidentLevelEnum preliminaryAccidentLevelEnum : PreliminaryAccidentLevelEnum.values()) {
            if (preliminaryAccidentLevelEnum.getName().equals(name)) {
                return preliminaryAccidentLevelEnum.getValue();
            }
        }
        return null;
    }
}
