/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * This is a add view objects for impassable area.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorImpassableAreaAddVO {

  /**
   * <p>
   * 坐标
   * </p>
   */
  @NotBlank
  private String coords;

  /**
   * <p>
   * 坐标类型(UTM-2)
   * </p>
   */
  @NotNull
  private Integer coordType;

  /**
   * <p>
   * 操作类型
   * </p>
   */
  @NotBlank
  private String type;

  /**
   * <p>
   * utm时区
   * </p>
   */
  @NotBlank
  private String utmZone;

  /**
   * <p>
   * 不可通行区域有效时间(废弃)
   * </p>
   */
  @Deprecated
  private String duration;

  /**
   * <p>
   * 操作用户
   * </p>
   */
  private String userName;

  /**
   * <p>
   * 生效类别（永久性、周期性）
   * </p>
   */
  @NotNull
  private Integer effectType;

  /**
   * <p>
   * 启停用状态
   * </p>
   */
  @NotNull
  private Integer effectState;

  /**
   * <p>
   * 周期时长
   * </p>
   */
  private List<String> timeLimit;

  /**
   * <p>
   * 备注
   * </p>
   */
  private String remark;

}
