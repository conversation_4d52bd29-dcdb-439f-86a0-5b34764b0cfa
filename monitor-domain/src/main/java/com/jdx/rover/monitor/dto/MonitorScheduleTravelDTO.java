/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 调度详情行驶信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleTravelDTO {
  /**
   * <p>
   * 调度单号
   * </p>
   */
  private String scheduleNo;

  /**
   * <p>
   * 调度状态
   * </p>
   */
  private String scheduleState = VehicleScheduleState.WAITING.getVehicleScheduleState();

  /**
   * <p>
   * 调度开始时间
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date scheduleStartTime;

  /**
   * <p>
   * 停靠点状态
   * </p>
   */
  private List<MonitorScheduleStopTravelDTO> stop;

}
