package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 一线运营处理事故状态
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentOperationStatusEnum {
    NO_OPS("NO_OPS", "无操作"),
    PENDING("PENDING", "未处理"),
    IN_PROGRESS("IN_PROGRESS", "处理中"),
    COMPLETED("COMPLETED", "已处理"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentOperationStatusEnum accidentOperationStatusEnum : AccidentOperationStatusEnum.values()) {
            if (accidentOperationStatusEnum.getValue().equals(value)) {
                return accidentOperationStatusEnum.getName();
            }
        }
        return null;
    }
}
