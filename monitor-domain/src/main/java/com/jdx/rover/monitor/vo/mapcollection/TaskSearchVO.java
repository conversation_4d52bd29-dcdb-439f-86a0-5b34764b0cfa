/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:09
 * @description 获取勘查任务
 */
@Data
public class TaskSearchVO implements Serializable {

    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 创建人用户名
     */
    private String username;

    /**
     * Tab类型
     */
    @NotBlank(message = "Tab类型不能为空")
    private String tabType;
}
