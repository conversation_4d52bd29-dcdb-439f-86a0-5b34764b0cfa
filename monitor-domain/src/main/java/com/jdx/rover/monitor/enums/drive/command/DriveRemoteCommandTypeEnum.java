/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.drive.command;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 远程指令类型
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DriveRemoteCommandTypeEnum {
    /**
     * 远程指令类型
     */
    UNKNOWN("未知"),
    REMOTE_DRIVE_ENTER_TAKE_OVER("进入平行驾驶接管"),
    REMOTE_DRIVE_EXIT_TAKE_OVER("退出平行驾驶接管"),
    REMOTE_REQUEST_RELIEVE_BUTTON_STOP("退出按钮急停指令"),
    REMOTE_REQUEST_AS_ARRIVED("视同到达指令"),
    REMOTE_REQUEST_REMOTE_RESTART("软件重启指令"),
    REMOTE_CONTROL_POWER_REBOOT("电源管理断电重启"),
    REMOTE_CONTROL_POWER_OFF("电源管理远程下电"),
    REMOTE_REQUEST_PASS_LIGHT("一键通过红绿灯"),
    REMOTE_REQUEST_CANCEL_LIGHT("取消通过红绿灯"),
    REMOTE_NO_SIGNAL_INTERSECTION("无保护左转"),
    REMOTE_VELOCITY_CONTROL("速度控制"),
    MAX_TORQUE("油门控制"),
    RUN_HAVE_MAP("有图模式"),
    RUN_NO_MAP("无图模式"),
    GEAR_PARK("驻车档"),
    GEAR_DRIVE("前进档"),
    GEAR_NEUTRAL("空档"),
    GEAR_REVERSE("倒车档"),
    HAZARD_LIGHT_LEFT("开左转灯"),
    HAZARD_LIGHT_RIGHT("开右转灯"),
    HAZARD_LIGHT_BOTH("开双闪"),
    HAZARD_LIGHT_OFF("关警示灯"),
    HEAD_LIGHT_ON("开大灯"),
    HEAD_LIGHT_OFF("关大灯"),
    SUPER_MODE_ON("开启强制遥控"),
    SUPER_MODE_OFF("关闭强制遥控"),
    ;
    /**
     * 标题描述
     */
    private final String title;
}
