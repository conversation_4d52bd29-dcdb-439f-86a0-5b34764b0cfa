/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.bo.vehicle;

import lombok.Data;

import java.util.List;

/**
 * 车辆启动告警咚咚通知对象
 *
 * <AUTHOR>
 */
@Data
public class VehicleBootAlarmNoticeBO extends VehicleAlarmNoticeBO {
  /**
   * 节点名
   */
  private String nodeName;
  /**
   * 模块名
   */
  private String moduleName;

  /**
   * 车辆状态
   */
  private String vehicleState;

  /**
   * 耗时（S）
   */
  private Integer duration;

  /**
   * 模块耗时（S）
   */
  private Integer moduleDuration;

  /**
   * 告警详情
   */
  private String alarmDetail;

  /**
   * 通知用户
   */
  private List<String> user;

  /**
   * 监控地址
   */
  private String monitorUrl;
}
