/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a user meta data type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum UserMetaDataEnum {
  /**
   * <p>
   * The enumerate jira use case type.
   * </p>
   */
  VEHICLE("vehicle", "车辆"),
  CITY("city", "城市"),
  STATION("station", "站点"),
  STOP("stop", "停靠点");

  /**
   * <p>
   * The meta data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;

  /**
   * <p>
   * The meta data request name corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String name;
}
