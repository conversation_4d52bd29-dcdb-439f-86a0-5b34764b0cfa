/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * xata发送修改车辆模式
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorXataChangeModeCommandVO {

  /**
   * <p>
   * 车号
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * 操作用户
   * </p>
   */
  private String userName;

  /**
   * <p>
   * 车辆模式
   * </p>
   */
  private String vehicleMode;

}
