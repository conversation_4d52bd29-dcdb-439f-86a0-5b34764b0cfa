package com.jdx.rover.monitor.dto.jdme;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡片消息中的枚举
 */
public class JueCardDataEnums {
    /**
     * 字体颜色
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  ColorType {
        RED("red", "红色字体"),
        BLACK("black",  "黑色字体"),
        GRAY("gray", "灰色字体"),
        ;
        private final String code;
        private final String name;
    }

    /**
     * 按钮颜色
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  ButtonType {
        PRIMARY("primary", "红色白底"),
        BLUE("blue",  "蓝色白底"),
        DEFAULT("default", "灰黑"),
        ;
        private final String code;
        private final String name;
    }
    /**
     * 标签字体颜色
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  LabelColorType {
        RED("red", "红色"),
        BLUE("blue", "蓝色"),
        YELLOW("yellow", "黄色"),
        ORANGE("orange", "橙色"),
        GREEN("green",  "绿色"),
        CYAN("cyan",  "蓝绿色"),
        PURPLE("purple",  "紫色"),
        GRAY("gray", "灰色"),
        DEFAULT("default", "默认"),
        ;
        private final String code;
        private final String name;
    }

    /**
     * 卡片消息文本类型
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  ElementValueType {
        TEXT("text", "文本"),
        LINK("link",  "链接"),
        ;
        private final String code;
        private final String name;
    }

    /**
     * 卡片按钮点击事件行为
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  ButtonBehaviorMethod {
        JOIN_GROUP_CHAT("joinGroupChat", "加入群聊"),
        SEND_MESSAGE("sendMessage",  "发送消息"),
        ;
        private final String code;
        private final String name;
    }

    /**
     * 卡片按布局样式
     */
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    @Getter
    public enum  ButtonLayout {
        ROW("row", "横向布局"),
        COLUMN("column",  "纵向布局"),
        ;
        private final String code;
        private final String name;
    }
}
