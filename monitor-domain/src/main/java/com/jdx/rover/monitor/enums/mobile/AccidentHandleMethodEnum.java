package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故处理方式枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentHandleMethodEnum {

    QUICK_SETTLEMENT("QUICK_SETTLEMENT", "快速处理(私了)"),
    POLICE_DETERMINATION_NO_INSURANCE("POLICE_DETERMINATION_NO_INSURANCE", "需交警定责，无需保险报案"),
    POLICE_DETERMINATION_WITH_INSURANCE("POLICE_DETERMINATION_WITH_INSURANCE" ,"需交警定责，需保险报案");

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentHandleMethodEnum accidentHandleMethodEnum : AccidentHandleMethodEnum.values()) {
            if (accidentHandleMethodEnum.getValue().equals(value)) {
                return accidentHandleMethodEnum.getName();
            }
        }
        return null;
    }
}
