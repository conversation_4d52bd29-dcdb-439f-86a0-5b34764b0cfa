/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;

import lombok.Data;

/**
 * <p>
 * This is a assist jira attachment entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AssistJiraAttachment extends BaseDomain {

  /**
   * <p>
   * Represents the assist jira record of assist jira attachment. The default value is null It's
   * changeable.
   * </p>
   */
  private Integer jiraRecordId;

  /**
   * <p>
   * Represents the is enabled of assist jira attachment. The default value is null It's changeable.
   * </p>
   */
  private Boolean isEnabled;

  /**
   * <p>
   * Represents the attachment name of assist jira attachment. The default value is null It's
   * changeable.
   * </p>
   */
  private String attachmentName;

  /**
   * <p>
   * Represents the attachment key of assist jira attachment. The default value is null It's
   * changeable.
   * </p>
   */
  private String attachmentKey;

  /**
   * <p>
   * Represents the bucket name of assist jira attachment. The default value is null It's
   * changeable.
   * </p>
   */
  private String bucketName;

}
