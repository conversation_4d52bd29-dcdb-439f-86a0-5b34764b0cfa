/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.issue;

import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is a vehicle alarm attachment data transform object entity.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmAttachmentDTO {
  /**
   * <p>
   * The id of attachment. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * The issue alarm id. The default value is null. It's changeable.
   * </p>
   */
  private Integer alarmId;

  /**
   * <p>
   * The issue no. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * The timestamp of report alarm. The default value is null. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * <p>
   * The url of report alarm. The default value is null. It's changeable.
   * </p>
   */
  private String url;

}
