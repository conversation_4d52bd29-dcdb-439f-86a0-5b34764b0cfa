package com.jdx.rover.monitor.dto.mobile.message;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 维修消息传输对象
 */
@Data
public class GetRepairDetailDTO {

    /**
     * 卡片ID
     */
    private Integer messageId;

    /**
     * 维修单号
     */
    private String repairNumber;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 类型
     */
    private String type;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /**
     * 维修完成描述
     */
    private String completeRequireDesc;

    /**
     * 驳回时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date rejectTime;

    /**
     * 驳回描述
     */
    private String rejectDesc;

    /**
     * 提报时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /**
     * 是否影响运营
     */
    private Integer isInfluenceOperation;

    /**
     * 是否影响运营名称
     */
    private String isInfluenceOperationName;

    /**
     * 提报描述
     */
    private String reportDesc;

    /**
     * 维修位置
     */
    private String requireHardwareTypeNames;

    /**
     * 附件
     */
    private List<RequireAttachmentDTO> attachmentList;

    /**
     * 维修记录
     */
    private List<RequireRecordDTO> recordList;
}
