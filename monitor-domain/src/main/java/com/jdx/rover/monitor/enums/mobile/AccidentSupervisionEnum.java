package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 是否需要上报监管
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentSupervisionEnum {

    YES(1, "需要上报"),
    NO(0, "无需上报"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentSupervisionEnum accidentSupervisionEnum : AccidentSupervisionEnum.values()) {
            if (accidentSupervisionEnum.getValue().equals(value)) {
                return accidentSupervisionEnum.getName();
            }
        }
        return null;
    }
}
