package com.jdx.rover.monitor.bo.accident;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jdx.rover.monitor.dto.accident.AccidentExportDTO;
import com.jdx.rover.monitor.enums.accident.AccidentSourceEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentHandleMethodEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentModuleEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentResolutionStatusEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentSupervisionEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentTagEnum;
import com.jdx.rover.monitor.enums.mobile.CompensateTypeEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentTypeEnum;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 事故导出数据对象
 */
@Data
public class AccidentExportBO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 事故提报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accidentReportTime;

    /**
     * 事故来源
     */
    private String accidentSource;

    /**
     * 事故地址
     */
    private String accidentAddress;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 处理方式
     */
    private String operationHandleMethod;

    /**
     * 是否需要赔偿
     */
    private Integer operationCompensated;

    /**
     * 金额
     */
    private Double operationAmount;

    /**
     * 是否上报车网
     */
    private Integer operationIsReportVehicleNet;

    /**
     * bug编号
     */
    private String bugCode;

    /**
     * 安全组定责事故分类
     */
    private String safetyGroupAccidentType;

    /**
     * 安全组定责事故等级
     */
    private String safetyGroupAccidentLevel;

    /**
     * 安全组描述
     */
    private String safetyGroupDescription;

    /**
     * 事故模块
     */
    private Long safetyGroupAccidentModule;

    /**
     * 事故标签
     */
    private Long safetyGroupAccidentTag;

    /**
     * 解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 解决情况
     */
    private String safetyGroupAccidentResolutionStatus;

    /**
     * 挂起原因
     */
    private String safetyGroupAccidentSuspendReason;

    public AccidentExportDTO convertBoToDto(AccidentExportBO accidentExportBO){
        AccidentExportDTO accidentExportDTO = new AccidentExportDTO();
        accidentExportDTO.setAccidentNo(accidentExportBO.getAccidentNo());
        accidentExportDTO.setVehicleName(accidentExportBO.getVehicleName());
        accidentExportDTO.setStationName(accidentExportBO.getStationName());
        accidentExportDTO.setAccidentReportTime(accidentExportBO.getAccidentReportTime());
        accidentExportDTO.setAccidentSourceName(AccidentSourceEnum.getNameByValue(accidentExportBO.getAccidentSource()));
        accidentExportDTO.setAccidentAddress(accidentExportBO.getAccidentAddress());
        accidentExportDTO.setOperationUser(accidentExportBO.getOperationUser());
        accidentExportDTO.setOperationHandleName(AccidentHandleMethodEnum.getNameByValue(accidentExportBO.getOperationHandleMethod()));
        accidentExportDTO.setOperationCompensatedName(CompensateTypeEnum.getNameByValue(accidentExportBO.getOperationCompensated()));
        accidentExportDTO.setOperationAmount(accidentExportBO.getOperationAmount());
        accidentExportDTO.setOperationIsReportVehicleNetName(AccidentSupervisionEnum.getNameByValue(accidentExportBO.getOperationIsReportVehicleNet()));
        accidentExportDTO.setBugCode(accidentExportBO.getBugCode());
        accidentExportDTO.setSafetyGroupAccidentLevelName(SafetyGroupAccidentLevelEnum.getNameByValue(accidentExportBO.getSafetyGroupAccidentLevel()));
        accidentExportDTO.setSafetyGroupAccidentTypeName(SafetyGroupAccidentTypeEnum.getNameByValue(accidentExportBO.getSafetyGroupAccidentType()));
        accidentExportDTO.setSafetyGroupDescription(accidentExportBO.getSafetyGroupDescription());
        accidentExportDTO.setSafetyGroupAccidentSolution(accidentExportBO.getSafetyGroupAccidentSolution());
        accidentExportDTO.setSafetyGroupAccidentResolutionStatusName(AccidentResolutionStatusEnum.getNameByValue(accidentExportBO.getSafetyGroupAccidentResolutionStatus()));
        accidentExportDTO.setSafetyGroupAccidentSuspendReason(accidentExportBO.getSafetyGroupAccidentSuspendReason());

        //解析模块与标签
        //设置事故标签
        Long accidentTag = accidentExportBO.getSafetyGroupAccidentTag();
        List<String> accidentTagNameList = new ArrayList<>();
        if (accidentTag != null && accidentTag > 0) {
            for (AccidentTagEnum tagEnum : AccidentTagEnum.values()) {
                Integer value = tagEnum.getValue();
                if ((accidentTag & 1L << value) > 0) {
                    accidentTagNameList.add(tagEnum.getName());
                }
            }
        }
        accidentExportDTO.setSafetyGroupAccidentTagName(CollectionUtils.isEmpty(accidentTagNameList) ? null : String.join(",", accidentTagNameList));
        //设置事故模块
        Long accidentModule = accidentExportBO.getSafetyGroupAccidentModule();
        List<String> accidentModuleNameList = new ArrayList<>();
        if (accidentModule != null && accidentModule > 0) {
            for (AccidentModuleEnum moduleEnum : AccidentModuleEnum.values()) {
                Integer value = moduleEnum.getValue();
                if ((accidentModule & 1L << value) > 0) {
                    accidentModuleNameList.add(moduleEnum.getName());
                }
            }
        }
        accidentExportDTO.setSafetyGroupAccidentModuleName(CollectionUtils.isEmpty(accidentModuleNameList) ? null : String.join(",", accidentModuleNameList));
        return accidentExportDTO;
    }
}
