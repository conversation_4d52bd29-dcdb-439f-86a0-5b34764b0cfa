/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.po.pda;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

/**
 * <p>
 * pda 实时状态表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PdaRealtimeInfo extends BaseDomain {

  /**
   * <p>
   * 产品标识
   * </p>
   */
  private String productKey;

  /**
   * <p>
   * 设备编号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelNo;

  /**
   * <p>
   * 设备型号
   * </p>
   */
  private String productModelName;

  /**
   * <p>
   * 实时状态(在线/离线/异常)
   * </p>
   */
  private String realtimeStatus;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupOne;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupTwo;

  /**
   * <p>
   * 分组
   * </p>
   */
  private String groupThree;

  /**
   * <p>
   * 分组名称
   * </p>
   */
  private String groupName;

  /**
   * <p>
   * 网络类型（Wi-Fi在线/蜂窝在线）
   * </p>
   */
  private Integer networkType;

  /**
   * <p>
   * 定位状态（开/关）
   * </p>
   */
  private Integer positionStatus;

  /**
   * <p>
   * 埋点状态（开/关）
   * </p>
   */
  private Integer burialStatus;

  /**
   * <p>
   * 统计个数
   * </p>
   */
  @TableField(value = "count(*)",insertStrategy = FieldStrategy.NEVER,updateStrategy = FieldStrategy.NEVER)
  private Integer countNum;

}
