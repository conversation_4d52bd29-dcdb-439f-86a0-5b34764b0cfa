/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.alarm;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 车辆告警缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class VehicleAlarmDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 报警事件列表
   */
  private List<VehicleAlarmEventDO> alarmEventList;


  /**
   * 告警列表
   */
  @Data
  @NoArgsConstructor
  public static class VehicleAlarmEventDO  {

    /**
     * 报警类型
     */
    private String type;

    /**
     * 上报时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 报警来源
     */
    private String source;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 硬件启动ID
     */
    private String runtimeUuid;

  }

}
