package com.jdx.rover.monitor.bo.accident;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 事故看板-事故详情查询对象
 */
@Data
public class AccidentBoardDetailQueryBO {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 事故等级
     */
    private String accidentLevel;

    /**
     * 缺陷状态
     */
    private List<String> bugStatusList;

    /**
     * 安全组是否编辑
     */
    private Integer isSafetyGroupEdit;
}
