/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.minimonitor;

import lombok.Data;

/**
 * <p>
 * This is a station data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorStationDTO {

  /**
   * <p>
   * Represents the station id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the station name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the station type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the station person user name. The default value is null. It's changeable.
   * </p>
   */
  private String user;

  /**
   * <p>
   * Represents the station contact. The default value is null. It's changeable.
   * </p>
   */
  private String contact;

  /**
   * <p>
   * Represents the station's latitude. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the station's longitude. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

}
