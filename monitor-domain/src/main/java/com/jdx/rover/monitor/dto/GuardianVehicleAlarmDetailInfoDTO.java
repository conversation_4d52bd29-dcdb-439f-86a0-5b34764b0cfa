/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a guardian alarm info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAlarmDetailInfoDTO {

  /**
   * <p>
   * Represents the id of guardian alarm info. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the vehicle entity of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the alarmEvent of guardian alarm info. It's changeable.
   * </p>
   */
  private String alarmEvent;

  /**
   * <p>
   * Represents the description of guardian alarm info. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date operateTimestamp;

  /**
   * <p>
   * Represents the station entity of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the boot detail. It's changeable.
   * </p>
   */
  private List<GuardianAbnormalBootModuleDTO> bootDetail;

}
