/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.domain.jsonhandler;

import com.alibaba.fastjson2.TypeReference;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:23
 * @description String列表映射泛型
 */
public class StringListTypeHandler extends ListTypeHandler<String> {

    @Override
    protected TypeReference<List<String>> specificType() {
        return new TypeReference<>() {};
    }
}
