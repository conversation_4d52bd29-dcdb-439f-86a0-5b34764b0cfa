/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

/**
 * <p>
 * 启动任务状态信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotReportBootTaskDTO {

    /**
     * 设备编号
     */
    private String deviceName;

    /**
     * 启动标识
     */
    private Long bootId;

    /**
     * 启动状态
     */
    private String bootStatus;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 启动时间
     */
    private Long startTime;

    /**
     * 启动时间
     */
    private Long endTime;

}
