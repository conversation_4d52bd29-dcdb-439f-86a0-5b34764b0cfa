/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 小程序用户配置枚举
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MobileUserConfigEnum {

    /**
     * 配置列表
     */
    CONFIG_ACCIDENT_VOICE_NOTIFY("CONFIG_ACCIDENT_VOICE_NOTIFY", "configAccidentVoiceNotify", "碰撞电话语音通知"),
    CONFIG_ACCIDENT_MOBILE_NOTIFY("CONFIG_ACCIDENT_MOBILE_NOTIFY", "configAccidentMobileNotify", "碰撞事件小程序通知"),
    CONFIG_MAP_UPGRADE_NOTIFY("CONFIG_MAP_UPGRADE_NOTIFY","configMapNotifyNotify", "地图升级任务小程序通知"),
    CONFIG_BUG_RECORD_COPY_NOTIFY("CONFIG_BUG_RECORD_COPY_NOTIFY", "configBugRecordCopyNotify", "缺陷拷贝小程序通知"),


 ;
  /**
   * 类型
   */
  private String value;

    /**
     * 操作
     */
    private String operate;

  /**
   * 描述
   */
  private String name;

    public static MobileUserConfigEnum of(String value) {
        for (MobileUserConfigEnum em : MobileUserConfigEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }


}