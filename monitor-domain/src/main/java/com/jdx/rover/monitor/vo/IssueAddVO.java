/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is a view object for issue add request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAddVO {
  /**
   * <p>
   * 车辆名称
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 工单号
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * 操作结果
   * </p>
   */
  private List<Integer> alarmIdList;


  /**
   * <p>
   * 技术支持生成工单告警来源
   * </p>
   */
  private List<IssueAlarmAddVO> alarmList;

  /**
   * <p>
   * 操作结果
   * </p>
   */
  private String operateResult;

  /**
   * <p>
   * 提交用户
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * 操作记录
   * </p>
   */
  private List<IssueOperateHistoryAddVO> issueHistoryList;
}
