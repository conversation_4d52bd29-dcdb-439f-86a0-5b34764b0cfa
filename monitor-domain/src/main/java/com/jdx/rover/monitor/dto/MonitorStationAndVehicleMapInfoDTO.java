/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.Map;

/**
 * <p>
 * This is a vehicle map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorStationAndVehicleMapInfoDTO {

  /**
   * <p>
   * Represents the station map info. The default value is null. It's changeable.
   * </p>
   */
  private MonitorStationMapInfoDTO station;

  /**
   * <p>
   * Represents the vehicle map info. The default value is null. It's changeable.
   * </p>
   */
  private Map<String, MonitorStationVehicleMapInfoDTO> vehicle;

}
