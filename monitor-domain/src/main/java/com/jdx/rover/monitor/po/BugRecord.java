package com.jdx.rover.monitor.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;

/**
 * BUG记录表
 */
@Data
@TableName("monitor_bug_record")
public class BugRecord extends BaseModel {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * bug编号
     */
    private String bugCode;

    /**
     * bug标题
     */
    private String topic;

    /**
     * 详细描述
     */
    private String description;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 严重程度
     */
    private String severity;

    /**
     * 问题模块
     */
    private String module;

    /**
     * rover版本号
     */
    private String version;

    /**
     * 事故等级
     */
    private String level;

    /**
     * 提报时间
     */
    private String debugTime;

    /**
     * 来源
     */
    private String jiraSource;

    /**
     * 来源编号
     */
    private String issueNo;
}
