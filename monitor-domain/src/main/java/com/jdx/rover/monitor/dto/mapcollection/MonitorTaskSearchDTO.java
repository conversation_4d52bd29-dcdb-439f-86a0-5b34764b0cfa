/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024/12/20 12:13
 * @description 监控单车/多车页任务列表请求DTO
 */
@Data
public class MonitorTaskSearchDTO implements Serializable {

    /**
     * 勘查任务ID
     */
    private Integer taskId;

    /**
     * 线路名称
     */
    private String taskName;

    /**
     * 勘查任务状态
     */
    private String taskStatus;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 线路里程
     */
    private Double totalMileage;

    /**
     * 任务提交时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date taskSubmitTime;

}
