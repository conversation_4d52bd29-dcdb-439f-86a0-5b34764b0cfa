/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.domain.jsonhandler;

import com.alibaba.fastjson2.TypeReference;
import com.jdx.rover.monitor.po.mapcollection.json.TaskRoutePoint;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:23
 * @description 勘查路线点位列表映射泛型
 */
public class TaskRouteListTypeHandler extends ListTypeHandler<TaskRoutePoint> {

    @Override
    protected TypeReference<List<TaskRoutePoint>> specificType() {
        return new TypeReference<>() {};
    }
}
