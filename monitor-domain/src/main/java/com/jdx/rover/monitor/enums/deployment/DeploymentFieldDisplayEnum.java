/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.deployment;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.enums.mapcollection.PreciseTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum;
import com.jdx.rover.monitor.enums.mapcollection.RoutePlanTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskRouteTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/18 16:47
 * @description 部署枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum DeploymentFieldDisplayEnum {
    DEFAULT("DEFAULT", "默认值", null, Lists.newArrayList("code", "name"), Lists.newArrayList("code", "name")),
    ELEMENT_TYPE_ENUM("ELEMENT_TYPE", "元素类型", ElementTypeEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    TASK_STATUS("TASK_STATUS", "勘查任务状态", TaskStatusEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    TASK_ROUTE_COLOR("TASK_ROUTE_COLOR", "线路颜色", RouteColorEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    PRECISE_TYPE("PRECISE_TYPE", "精度方案", PreciseTypeEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    ROUTE_PLAN_TYPE("ROUTE_PLAN_TYPE", "线路方案", RoutePlanTypeEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    TASK_ROUTE_TYPE("TASK_ROUTE_TYPE", "线路类型", TaskRouteTypeEnum.class, DEFAULT.fieldNameList, Lists.newArrayList("enumValue", "enumName")),
    ;

    /**
     * 值
     */
    private final String value;
    /**
     * 名称
     */
    private final String title;
    /**
     * 类型
     */
    private final Class clazz;
    /**
     * 字段列表
     */
    private final List<String> fieldNameList;
    /**
     * 展示名称
     */
    private final List<String> displayNameList;
}
