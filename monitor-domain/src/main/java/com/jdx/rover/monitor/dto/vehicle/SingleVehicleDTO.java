/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 监控单车页
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleDTO {

  /**
   * 配送状态
   */
  private String scheduleState;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 剩余电量
   */
  private Double power;

  /**
   * 剩余电量
   */
  private Double speed;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 当前调度完成里程
   */
  private Double currentScheduleFinishedMileage;

  /**
   * 调度信息
   */
  private VehicleDrivableDirectionDTO vehicleDrivableDirection;

  /**
   * 告警事件
   */
  private List<AlarmEventDTO> alarmEventList;

  /**
   * 记录时间
   */
  private Date recordTime;

    /**
     * 车辆是否有限速配置
     */
    private Boolean isSpeedLimitEnabled = false;
}
