/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.alarm;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 机器人告警缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotAlarmDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String deviceName;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 启动ID
   */
  private Long bootId;

  /**
   * 报警事件列表
   */
  private List<DeviceAlarmEventDO> alarmEventList;


  /**
   * 告警列表
   */
  @Data
  @NoArgsConstructor
  public static class DeviceAlarmEventDO  {

    /**
     * <p>
     * 性能模块
     * </p>
     */
    private String performance;

    /**
     * <p>
     * 异常模块
     * </p>
     */
    private String abnormalModule;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 处理用户
     */
    private String followUser;

  }

}
