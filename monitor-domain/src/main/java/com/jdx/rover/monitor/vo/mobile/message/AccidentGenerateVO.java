package com.jdx.rover.monitor.vo.mobile.message;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 生成事故单请求实体
 */
@Data
public class AccidentGenerateVO {

    /**
     * 卡片id
     */
    @NotNull(message = "卡片id不能为空")
    private Integer messageId;

    /**
     * 事故编号
     */
    @NotBlank(message = "事故编号不能为空")
    private String accidentNo;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    /**
     * 处理方式
     */
    private String handleMethod;

    /**
     * 是否需要赔偿
     */
    private Boolean compensated;

    /**
     * 金额
     */
    private String amount;

    /**
     * 是否上报监管
     */
    private Boolean isReportVehicleNet;

    /**
     * 附件
     */
    private List<AccidentAttachmentVO> attachmentList;

    /**
     * 事故分类
     */
    private String accidentType;

    /**
     * 事故责任
     */
    private String accidentJudge;

    /**
     * 原因
     */
    private String reason;
}
