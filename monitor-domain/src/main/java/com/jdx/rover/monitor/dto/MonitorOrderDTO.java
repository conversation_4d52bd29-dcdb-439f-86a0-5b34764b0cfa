/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a monitor order data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorOrderDTO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the task type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * Represents the stop name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String stopName;

  /**
   * <p>
   * Represents the date of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Date scheduleStartTime;

  /**
   * <p>
   * Represents the order list of vehicle current task. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorOrderInfoDTO> order;

}
