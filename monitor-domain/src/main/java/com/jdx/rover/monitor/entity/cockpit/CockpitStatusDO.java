package com.jdx.rover.monitor.entity.cockpit;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 驾驶舱状态信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Data
public class CockpitStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 驾驶舱编号
     */
    private String cockpitNumber;

    /**
     * 方向盘连接状态
     */
    private String steeringStatus;

    /**
     * 刹车连接状态
     */
    private String brakeStatus;

    /**
     * 大屏连接状态
     */
    private String screenStatus;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 驾驶舱用户名称
     */
    private String cockpitUserName;

    /**
     * 座席状态
     */
    private String cockpitStatus;

    /**
     * 座席模式
     */
    private String cockpitMode;

    /**
     * 坐席类型(监控座席/驾舱座席)
     */
    private String cockpitType;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 接单状态
     */
    private String acceptIssueStatus;
}
