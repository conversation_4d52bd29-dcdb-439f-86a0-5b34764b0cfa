/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.GuardianVehicleAbnormalInfoDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a guardian abnormal info entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAbnormalInfo extends BaseDomain {

  /**
   * <p>
   * Represents the vehicle entity of guardian abnormal info. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the vehicle entity of station info. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the error code of guardian abnormal info. It's changeable.
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * Represents the error level of guardian abnormal info. It's changeable.
   * </p>
   */
  private String errorLevel;

  /**
   * <p>
   * Represents the error message of guardian abnormal info. It's changeable.
   * </p>
   */
  private String errorMessage;

  /**
   * <p>
   * Represents the start timestamp of guardian abnormal info. It's changeable.
   * </p>
   */
  private Date startTimestamp;

  /**
   * <p>
   * Represents the end timestamp of guardian abnormal info. It's changeable.
   * </p>
   */
  private Date endTimestamp;

  /**
   * <p>
   * guardian abnormal info data transform object
   * </p>
   * 
   * @return The guardian abnormal info data transform object
   */
  public GuardianVehicleAbnormalInfoDTO toGuardianAbnormalInfoDto() {
    GuardianVehicleAbnormalInfoDTO guardianAbnormalInfoDto = new GuardianVehicleAbnormalInfoDTO();
    guardianAbnormalInfoDto.setId(getId());
    guardianAbnormalInfoDto.setVehicleName(vehicleName);
    guardianAbnormalInfoDto.setStationName(stationName);
    guardianAbnormalInfoDto.setErrorCode(errorCode);
    guardianAbnormalInfoDto.setErrorLevel(errorLevel);
    guardianAbnormalInfoDto.setErrorMessage(errorMessage);
    guardianAbnormalInfoDto.setStartTimestamp(startTimestamp);
    return guardianAbnormalInfoDto;
  }
}
