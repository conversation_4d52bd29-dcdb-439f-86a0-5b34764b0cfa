/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 接口返回值风格样式枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleSystemStateSortEnum {
    SELF_REPAIR(10, "自修复中"),
    MALFUNCTION(20, "系统故障"),
    NORMAL(30, "正常"),
    CONNECTION_LOST_HAVE_TASK(40, "有任务失联"),
    /**
     * 无任务和已到达的失联
     */
    CONNECTION_LOST_NO_TASK(50, "无任务失联"),
    OFFLINE(60, "离线"),
    ;

    private final Integer value;

    private final String title;

    public static VehicleSystemStateSortEnum of(String systemState) {
        for (VehicleSystemStateSortEnum em : VehicleSystemStateSortEnum.values()) {
            if (em.name().equals(systemState)) {
                return em;
            }
        }
        return VehicleSystemStateSortEnum.OFFLINE;
    }
}

