package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故看板明细类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentBoardTypeEnum {

    TODAY_HIGH_ACCIDENT("TODAY_HIGH_ACCIDENT","今日高风险"),
    TODAY_MEDIUM_ACCIDENT("TODAY_MEDIUM_ACCIDENT", "今日中风险"),
    TODAY_NO_FOLLOW_ACCIDENT("TODAY_NO_FOLLOW_ACCIDENT","今日未跟进"),
    MORE_THAN_30_DAYS_UNRESOLVED_ACCIDENT("MORE_THAN_30_DAYS_UNRESOLVED_ACCIDENT","超过30天未解决"),
    MORE_THAN_90_DAYS_UNRESOLVED_ACCIDENT("MORE_THAN_90_DAYS_UNRESOLVED_ACCIDENT","超过90天未解决"),
    SUSPENDED_ACCIDENT("SUSPENDED_ACCIDENT","挂起事故"),
    WEEK_TOTAL_ACCIDENT("WEEK_TOTAL_ACCIDENT", "本周总事故"),
    WEEK_HIGH_ACCIDENT("WEEK_HIGH_ACCIDENT","本周高风险"),
    WEEK_MEDIUM_ACCIDENT("WEEK_MEDIUM_ACCIDENT","本周中风险"),
    MONTH_TOTAL_ACCIDENT("MONTH_TOTAL_ACCIDENT", "本月总事故"),
    MONTH_HIGH_ACCIDENT("MONTH_HIGH_ACCIDENT","本月高风险"),
    MONTH_MEDIUM_ACCIDENT("MONTH_MEDIUM_ACCIDENT","本月中风险"),
    ;

    /**
     * 值
     */
    private final String value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;
}
