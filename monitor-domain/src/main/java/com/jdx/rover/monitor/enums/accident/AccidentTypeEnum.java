/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.enums.accident;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 事故类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AccidentTypeEnum {

    /**
     * 主动碰撞
     */
    INITIATIVE_CRASH("INITIATIVE_CRASH","主动碰撞"),
    PASSIVE_CRASH("PASSIVE_CRASH","被动碰撞"),
    BRAKES_REAR_END("BRAKES_REAR_END","急刹追尾"),
    SCRATCH("SCRATCH","剐蹭"),
    OTHER("OTHER","其他事故"),
    WAIT_CONFIRM("WAIT_CONFIRM","待确认"),
    NONE("NONE","无事故"),
    ;
    /**
     * 值.
     */
    private final String value;

    /**
     * 标题.
     */
    private final String title;
}
