/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import java.util.Date;

import com.jdx.rover.monitor.dto.issue.IssueAlarmAttachmentDTO;

import lombok.Data;

/**
 * <p>
 * This is a model class of issue alarm attachment.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmAttachment extends BaseDomain {

  /**
   * <p>
   * The issue alarm id. The default value is null. It's changeable.
   * </p>
   */
  private Integer alarmId;

  /**
   * <p>
   * The error code. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * The timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * <p>
   * The issue attachment url. The default value is null. It's changeable.
   * </p>
   */
  private String url;

  /**
   * <p>
   * This method helps to convert model of attachmentDto to IssueAlarmAttachmentDto.
   * </p>
   * 
   * @return The data transfer object for vehicle alarm record.
   */
  public IssueAlarmAttachmentDTO toIssueAlarmAttachmentDto() {
    IssueAlarmAttachmentDTO attachmentDto = new IssueAlarmAttachmentDTO();
    attachmentDto.setId(getId());
    attachmentDto.setAlarmId(alarmId);
    attachmentDto.setIssueNo(issueNo);
    attachmentDto.setTimestamp(timestamp);
    attachmentDto.setUrl(url);
    return attachmentDto;
  }
}
