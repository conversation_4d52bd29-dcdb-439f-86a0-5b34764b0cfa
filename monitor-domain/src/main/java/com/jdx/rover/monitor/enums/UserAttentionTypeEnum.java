/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a user attention type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum UserAttentionTypeEnum {
  /**
   * <p>
   * The enumerate type.
   * </p>
   */
  BUMP("bump", "bump碰撞");

  /**
   * <p>
   * The type data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;

  /**
   * <p>
   * The name data request name corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String name;
}
