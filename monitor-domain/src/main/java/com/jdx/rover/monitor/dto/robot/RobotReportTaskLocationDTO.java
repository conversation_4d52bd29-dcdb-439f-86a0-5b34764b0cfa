/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 机器人设备任务点及路径信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotReportTaskLocationDTO {

    /**
     * 设备编号
     */
    private String robotId;

    /**
     * 地图标识
     */
    private String mapId;

    /**
     * 车辆当前坐标
     */
    private PoiInfo position;

    /**
     * 目标点
     */
    private PoiInfo goalPose;

    /**
     * 线速度
     */
    private Double linearVelocity;

    /**
     * 角速度
     */
    private Double angularVelocity;

    /**
     * 功能码 0-车辆无任务，1-自由导航任务状态，2-故障状态
     */
    private Integer functionCode;

    /**
     * 规划路径
     */
    private List<PoiInfo> path;

    /**
     * 点位信息
     */
    @Data
    public static class PoiInfo {

        /**
         * X坐标
         */
        private Double x;

        /**
         * Y坐标
         */
        private Double y;

        /**
         * yaw坐标
         */
        private Double yaw;

    }

}
