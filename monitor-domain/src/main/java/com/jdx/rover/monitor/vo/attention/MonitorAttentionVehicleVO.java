/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.attention;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/16 19:39
 * @description MonitorAttentionVehicleVO
 */
@Data
public class MonitorAttentionVehicleVO {

    /**
     * 车辆名称
     */
    @NotBlank(message = "车辆名称不能为空")
    private String vehicleName;
}
