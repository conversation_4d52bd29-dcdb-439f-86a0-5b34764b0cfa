package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 车辆采集状态枚举
 * @author: wang<PERSON>otai
 * @create: 2024-12-19 10:34
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum CollectionStatusEnum {

    NOT_STARTED("NOT_STARTED", "未开始"),
    PROCESSING("PROCESSING", "进行中"),
    PAUSED("PAUSED", "已暂停"),
    ;

    /**
     * 采集状态
     */
    private final String collectionStatus;

    /**
     * 采集状态名称
     */
    private final String collectionStatusName;

    /**
     * 获取枚举
     *
     * @param collectionStatus collectionStatus
     * @return CollectionStatusEnum
     */
    public static CollectionStatusEnum of(String collectionStatus) {
        for (CollectionStatusEnum collectionStatusEnum : CollectionStatusEnum.values()) {
            if (collectionStatusEnum.getCollectionStatus().equals(collectionStatus)) {
                return collectionStatusEnum;
            }
        }
        return null;
    }
}