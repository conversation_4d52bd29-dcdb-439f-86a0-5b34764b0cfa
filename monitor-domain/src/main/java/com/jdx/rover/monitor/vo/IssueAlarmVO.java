/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is a view object for issue alarm vo.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmVO {
   /**
   * <p>
   * 报警编号.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * 报警事件类型.
   * </p>
   */
  private String alarmEventType;

  /**
   * <p>
   * 上报时间/首次报警时间.
   * </p>
   */
  private Date reportTime;
}
