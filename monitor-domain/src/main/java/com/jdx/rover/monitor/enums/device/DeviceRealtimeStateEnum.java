/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 设备在线状态
 * </p>
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DeviceRealtimeStateEnum {

    /**
     * 车端上报
     */
    OFFLINE("OFFLINE",0, "离线"),
    ONLINE("ONLINE", 1, "在线"),
    ABNORMAL("ABNORMAL", 2, "异常"),
    ;
    /**
     * 值.
     */
    private final String value;

    /**
     * 属性值.
     */
    private final Integer property;

    /**
     * 标题.
     */
    private final String title;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (DeviceRealtimeStateEnum deviceRealtimeStateEnum : DeviceRealtimeStateEnum.values()) {
            if (deviceRealtimeStateEnum.getValue().equals(value)) {
                return deviceRealtimeStateEnum.getTitle();
            }
        }
        return null;
    }

    /**
     * <p>
     * 获取值
     * </p>
     */
    public static String getTitleByProperty(Integer property) {
        for (DeviceRealtimeStateEnum deviceRealtimeStateEnum : DeviceRealtimeStateEnum.values()) {
            if (deviceRealtimeStateEnum.getProperty().equals(property)) {
                return deviceRealtimeStateEnum.getTitle();
            }
        }
        return null;
    }
}
