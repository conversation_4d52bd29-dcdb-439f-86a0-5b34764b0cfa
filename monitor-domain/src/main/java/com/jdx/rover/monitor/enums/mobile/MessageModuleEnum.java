package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 消息模块枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum MessageModuleEnum {

    ACCIDENT("ACCIDENT", "事故"),
    REPAIR("REPAIR", "维修单"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (MessageModuleEnum messageModuleEnum : MessageModuleEnum.values()) {
            if (messageModuleEnum.getValue().equals(value)) {
                return messageModuleEnum.getName();
            }
        }
        return null;
    }
}
