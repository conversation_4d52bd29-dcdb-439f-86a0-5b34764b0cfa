/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.vehicle.single;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 红绿灯来源
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum TrafficLightSourceEnum {
  UNKNOWN("UNKNOWN", "未知"), PERCEPTION("PERCEPTION", "感知"), SUPERVISOR("SUPERVISOR", "监控");

  private final String value;

  private final String title;
}
