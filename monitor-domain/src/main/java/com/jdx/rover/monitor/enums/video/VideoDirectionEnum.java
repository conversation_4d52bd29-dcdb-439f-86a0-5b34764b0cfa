package com.jdx.rover.monitor.enums.video;

import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum VideoDirectionEnum {

    ALL("ALL", "全部"),
    FRONT("FRONT", "前"),
    RIGHT("RIGHT", "右"),
    BACK("BACK", "后"),
    LEFT("LEFT", "左"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (VideoDirectionEnum videoDirectionEnum : VideoDirectionEnum.values()) {
            if (videoDirectionEnum.getValue().equals(value)) {
                return videoDirectionEnum.getName();
            }
        }
        return null;
    }
}
