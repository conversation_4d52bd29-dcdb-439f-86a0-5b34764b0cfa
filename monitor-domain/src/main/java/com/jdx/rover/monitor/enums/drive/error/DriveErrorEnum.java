/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.drive.error;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 错误码枚举
 * A开头 客户端错误
 * B开头 服务端错误
 * C开头 第三方错误
 * 10 平行驾驶 01 web
 * 10 平行驾驶 02 worker
 * 01 guardian 02 Hermes
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-02-19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DriveErrorEnum {
  // OK
  OK("00000", "正常"),

  // A开头,客户端错误
  FORBID_ENTER_TAKE_OVER("A100101", "用户%s已经通过%s接管车辆%s,不允许被驾驶舱%s重复接管!"),
  FORBID_EXIT_TAKE_OVER("A100102", "用户%s已经通过%s接管车辆%s,不允许被驾驶舱%s退出接管!"),
  FORBID_NOT_TAKE_OVER("A100103", "用户%s没有通过%s接管车辆%s,不允许操作!"),

  VEHICLE_NOT_STOP_SWITCH_MAP("A100201", "请先停车，再进行切换操作!"),

  // B开头,服务端错误
  ERROR_GET_PROVIDER_URL("B021001", "服务端错误获取远程调用URL"),

  // C开头,三方调用错误
  ERROR_CALL_SERVICE("C021001", "服务端远程调用服务异常"),
  ERROR_CALL_UPLOAD("C021002", "服务端远程调用上传异常"),
  ERROR_CALL_DOWNLOAD("C021003", "服务端远程调用下载异常"),

  ERROR_HERMES_ABSENT("C021004", "车端HERMES连接不存在"),
  ERROR_POWER_MANAGER_ABSENT("C021005", "车端电源管理连接不存在"),
  ;

  /**
   * 错误码
   */
  private String value;

  /**
   * 描述
   */
  private String title;
}