package com.jdx.rover.monitor.dto.mobile.message;

import lombok.Data;

import java.util.List;

/**
 * 事故详情传输对象
 */
@Data
public class GetAccidentDetailDTO {

    /**
     * 消息id
     */
    private Integer messageId;

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 描述
     */
    private String description;

    /**
     * 影子系统编号
     */
    private Integer shadowEventId;

    /**
     * 是否有暂存事故单
     */
    private Boolean isHaveTemporaryIncident;

    /**
     * 工单编号
     */
    private String issueNumber;

    /**
     * 工单状态
     */
    private String issueStatus;

    /**
     * 工单状态名称
     */
    private String issueStatusName;

    /**
     * 地址
     */
    private String address;

    /**
     * 远驾跟进人
     */
    private String remoteUser;

    /**
     * 一线跟进人
     */
    private String operationUser;

    /**
     * 操作记录
     */
    private List<AccidentRecordDTO> recordList;

    /**
     * 附件
     */
    private List<AccidentAttachmentDTO> attachmentList;
}
