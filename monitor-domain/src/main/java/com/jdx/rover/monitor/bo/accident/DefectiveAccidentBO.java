package com.jdx.rover.monitor.bo.accident;

import lombok.Data;

import java.util.Date;

/**
 * 有缺陷事故数传输对象
 */
@Data
public class DefectiveAccidentBO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 安全组是否编辑过
     */
    private Integer isSafetyGroupEdit;

    /**
     * 事故等级
     */
    private String technicalSupportAccidentLevel;

    /**
     * 事故创建日期
     */
    private Date accidentDayTime;
}
