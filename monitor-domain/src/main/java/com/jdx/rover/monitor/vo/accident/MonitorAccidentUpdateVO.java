/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.vo.accident;

import cn.hutool.core.date.DatePattern;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <p>
 * 监控端事故信息更新入参
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/15
 */
@Data
public class MonitorAccidentUpdateVO {

    /**
     * 事故编号
     */
    @NotBlank(message = "事故编号不能为空！")
    private String accidentNo;

    /**
     * 事故排查状态
     */
    private String accidentStatus;

    /**
     * 事故分类
     */
    private String accidentType;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * 事故发生时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date accidentTime;
}
