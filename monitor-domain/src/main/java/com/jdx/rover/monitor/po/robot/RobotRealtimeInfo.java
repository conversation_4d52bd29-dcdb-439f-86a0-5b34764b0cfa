/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.po.robot;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <p>
 * 机器人(多合一/巡检) 实时状态表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RobotRealtimeInfo extends RobotBasicGroupInfo {

  /**
   * <p>
   * 实时状态(在线/离线/异常)
   * </p>
   */
  private String realtimeStatus;

  /**
   * <p>
   * 统计个数
   * </p>
   */
  @TableField(value = "count(*)",insertStrategy = FieldStrategy.NEVER,updateStrategy = FieldStrategy.NEVER)
  private Integer countNum;

}
