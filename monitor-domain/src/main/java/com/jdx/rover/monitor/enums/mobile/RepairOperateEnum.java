package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 维修卡片操作类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum RepairOperateEnum {

    CONFIRM("CONFIRM", "确认可用"),
    KNOW("KNOW", "我已知晓"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (RepairOperateEnum repairOperateEnum : RepairOperateEnum.values()) {
            if (repairOperateEnum.getValue().equals(value)) {
                return repairOperateEnum.getName();
            }
        }
        return null;
    }
}
