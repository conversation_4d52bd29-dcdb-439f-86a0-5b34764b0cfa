/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is a view object for reset abnormal control command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorResetAbnormalControlCommandVO {

  /**
   * <p>
   * Represents the id of request. The default value is 0. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Integer id;

  /**
   * <p>
   * Represents the command type. It's changeable.
   * </p>
   */
  private String commandType;

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  @NotBlank
  private String moduleName;

  /**
   * <p>
   * Represents the timeStamp of user operate remoteControl request. It's changeable.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date timeStamp;

}
