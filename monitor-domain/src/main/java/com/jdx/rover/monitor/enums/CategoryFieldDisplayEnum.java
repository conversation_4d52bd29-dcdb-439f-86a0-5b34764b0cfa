/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 字段映射枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum CategoryFieldDisplayEnum {
  VEHICLE_ALARM_CATEGORY_ENUM("VEHICLE_ALARM_CATEGORY_ENUM", "车辆告警分类", AlarmEventCategoryEnum.class, Lists.newArrayList("name", "child"), VehicleAlarmEnum.class, Lists.newArrayList("value", "name")),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 名称
   */
  private final String title;
  /**
   * 类型
   */
  private final Class clazz;
  /**
   * 字段列表
   */
  private final List<String> fieldNameList;
  /**
   * 子类型
   */
  private final Class childClazz;
  /**
   * 子类字段列表
   */
  private final List<String> childFieldNameList;
}