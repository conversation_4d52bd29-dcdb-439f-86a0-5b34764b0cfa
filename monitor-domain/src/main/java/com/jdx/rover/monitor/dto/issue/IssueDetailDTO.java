/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.issue;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a monitor issue detail data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueDetailDTO {

  /**
   * <p>
   * Represents the number of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the vehicle name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the station name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the state of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueState;

  /**
   * <p>
   * Represents the state name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueStateName;

  /**
   * <p>
   * Represents the operate user name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String operateUserName;

  /**
   * <p>
   * Represents the report user name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String reportUserName;

  /**
   * <p>
   * Represents the report time of the issue. The default value is null. It's changeable.
   * </p>
   */
  private Date reportTime;

  /**
   * <p>
   * Represents the accept time of the issue. The default value is null. It's changeable.
   * </p>
   */
  private Date acceptTime;

  /**
   * <p>
   * Represents the finish time of the issue. The default value is null. It's changeable.
   * </p>
   */
  private Date finishTime;

  /**
   * <p>
   * Represents the jira number of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String jiraNo;

  /**
   * <p>
   * Represents the alarm type of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the alarm time of the issue. The default value is null. It's changeable.
   * </p>
   */
  private Date alarmTime;

  /**
   * <p>
   * Represents the operate result of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String operateResult;

  /**
   * <p>
   * Represents the operate result name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String operateResultName;

  /**
   * <p>
   * Represents the alarm item list of the issue. The default value is null. It's changeable.
   * </p>
   */
  private List<IssueAlarmDTO> alarmList;

  /**
   * <p>
   * Represents the history list of the issue. The default value is null. It's changeable.
   * </p>
   */
  private List<IssueOperateHistoryDTO> historyList;
}
