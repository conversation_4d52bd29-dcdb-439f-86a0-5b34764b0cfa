/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 机器人异常响应列表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotAbnormalPageDTO {

  /**
   * <p>
   * 索引值
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * 车牌号
   * </p>
   */
  private String remarkName;

  /**
   * <p>
   * 设备编号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 错误类型
   * </p>
   */
  private String errorTypeName;

  /**
   * <p>
   * 错误编号
   * </p>
   */
  private Integer errorNumber;

  /**
   * <p>
   * 错误级别
   * </p>
   */
  private String errorLevelName;

  /**
   * <p>
   * 错误消息
   * </p>
   */
  private String errorMsg;

  /**
   * <p>
   * 触发时间
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * 结束时间
   * </p>
   */
  private Date endTime;

  /**
   * <p>
   * 工作模式
   * </p>
   */
  private String workModeName;

  /**
   * 站点名称
   */
  private String stationName;

  /**
   * 任务号
   */
  private String taskNo;

  /**
   * 位置
   */
  private String point;

  /**
   * 解决方案
   */
  private String solutionName;

  /**
   * 处理方式
   */
  private String processModeName;

  /**
   * 跟进用户
   */
  private String followUser;

}
