/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MiniMonitorEventEnum {
  // 控车指令
  MINI_MONITOR_VEHICLE_CONTROL("MINI_MONITOR_VEHICLE_CONTROL", "MINI_MONITOR75838499", "监控运营端控车", ""),
  MINI_MONITOR_VEHICLE_EMERGENCY("MINI_MONITOR_VEHICLE_EMERGENCY", "MINI_MONITOR75794523", "监控运营端急停", ""),
  MINI_MONITOR_START_CONTROL("MINI_MONITOR_REMOTE_CONTROL", "MINI_MONITOR690744580", "监控运营端开启远程遥控", ""),
  MINI_MONITOR_TAP_CONTROL("MINI_MONITOR_TAP_CONTROL", "MINI_MONITOR2034835033", "监控运营端档位修改", ""),
  MINI_MONITOR_SPEED_CONTROL("MINI_MONITOR_SPEED_CONTROL", "MINI_MONITOR194161206", "监控运营端速度修改", ""),
  MINI_MONITOR_ANGEL_CONTROL("MINI_MONITOR_ANGEL_CONTROL", "MINI_MONITOR146108323", "监控运营端角度修改", ""),
  MINI_MONITOR_LAMP_CONTROL("MINI_MONITOR_LAMP_CONTROL", "MINI_MONITOR1963851871", "监控运营端开关大灯", ""),
  MINI_MONITOR_END_CONTROL("MINI_MONITOR_END_CONTROL", "MINI_MONITOR98088001", "监控运营端结束远程遥控", ""),
  MINI_MONITOR_VEHICLE_RECOVERY("MINI_MONITOR_VEHICLE_RECOVERY", "MINI_MONITOR75798639", "监控运营端恢复", ""),
  // 运营操作
  MINI_MONITOR_AS_ARRIVED("MINI_MONITOR_AS_ARRIVED", "MINI_MONITOR143171480", "监控运营端视同到达", ""),
  MINI_MONITOR_SINGLE_SCHEDULE("MINI_MONITOR_SINGLE_SCHEDULE", "MINI_MONITOR1882958987", "监控运营端单点调度", ""),
  MINI_MONITOR_REMOTE_LOGIN("MINI_MONITOR_REMOTE_LOGIN", "MINI_MONITOR315101622", "监控运营端远程登录车端", ""),
  MINI_MONITOR_CLEAR_SCHEDULE("MINI_MONITOR_CLEAR_SCHEDULE", "MINI_MONITOR540059546", "监控运营端清除空调度", ""),
  MINI_MONITOR_START_NEXT_STOP("MINI_MONITOR_START_NEXT_STOP", "MINI_MONITOR97419333", "监控运营端立即前往下一点", ""),
  MINI_MONITOR_START_RETURN_HOME("MINI_MONITOR_START_RETURN_HOME", "MINI_MONITOR29259587", "监控运营端立即返程", ""),
  MINI_MONITOR_EXTEND_STOP_WAITTIME("MINI_MONITOR_EXTEND_STOP_WAITTIME", "MINI_MONITOR335948314", "监控运营端延长停靠时长", ""),
  MINI_MONITOR_SET_STOP_WAITTIME("MINI_MONITOR_SET_STOP_WAITTIME", "MINI_MONITOR1201018049", "监控运营端设置停靠时长", ""),
  MINI_MONITOR_SET_RETURN_TIME("MINI_MONITOR_SET_RETURN_TIME", "MINI_MONITOR1677828848", "监控运营端设置返程时刻", ""),
  MINI_MONITOR_START_LOAD("MINI_MONITOR_START_LOAD", "MINI_MONITOR199576911", "监控运营端去装载", ""),
  MINI_MONITOR_LOAD_SUCCESS("MINI_MONITOR_LOAD_SUCCESS", "MINI_MONITOR150201312", "监控运营端装载完成", ""),
  MINI_MONITOR_STOP_NOW_START("MINI_MONITOR_STOP_NOW_START", "MINI_MONITOR28758947", "监控运营端立即出发", ""),
  MINI_MONITOR_STOP_TIMING_START("MINI_MONITOR_STOP_TIMING_START", "MINI_MONITOR1942915639", "监控运营端定时出发", ""),
  MINI_MONITOR_ORDER_REMINDER("MINI_MONITOR_ORDER_REMINDER", "MINI_MONITOR75672493", "监控运营端催单", ""),
  MINI_MONITOR_ORDER_ALL_REMINDER("MINI_MONITOR_ORDER_ALL_REMINDER", "MINI_MONITOR1539926120", "监控运营端一键催取寄", ""),
  MINI_MONITOR_ASSIGN_COLLECT_ORDER("MINI_MONITOR_ASSIGN_COLLECT_ORDER", "MINI_MONITOR318974513", "监控运营端分配揽收单", ""),
  MINI_MONITOR_CANCEL_COLLECT_ORDER("MINI_MONITOR_CANCEL_COLLECT_ORDER", "MINI_MONITOR474692070", "监控运营端取消揽收单", ""),
  MINI_MONITOR_DELETE_ORDER("MINI_MONITOR_DELETE_ORDER", "MINI_MONITOR1948217696", "监控运营端移动装载删除订单", ""),
  MINI_MONITOR_LOAD_OPEN_BOX("MINI_MONITOR_LOAD_OPEN_BOX", "MINI_MONITOR453517594", "监控运营端移动装载开箱", ""),
  MINI_MONITOR_REMOTE_LOAD_SUCCESS("MINI_MONITOR_REMOTE_LOAD_SUCCESS", "MINI_MONITOR215404421", "监控运营端移动装载装载完成", ""),
  MINI_MONITOR_LOAD_CONFIRM("MINI_MONITOR_LOAD_CONFIRM", "MINI_MONITOR1780804698", "监控运营端确认装载（格口维度）", ""),
  MINI_MONITOR_LOAD_REOPEN_BOX("MINI_MONITOR_LOAD_OPEN_BOX", "MINI_MONITOR1867648361", "监控运营端再次开箱", ""),

 ;
  /**
   * 类型
   */
  private String eventType;

  /**
   * 事件关键字
   */
  private String eventNo;

  /**
   * 事件名称
   */
  private String eventName;

  /**
   * 事件描述
   */
  private String description;


}