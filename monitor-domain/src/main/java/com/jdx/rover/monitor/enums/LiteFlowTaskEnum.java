/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * This is a liteFlow task type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum LiteFlowTaskEnum {
  /**
   * <p>
   * The enumerate task type.
   * </p>
   */
  SCHEDULECHAIN("scheduleChain"),
  CH<PERSON><PERSON>CHEDULE("checkSchedule"),
  UPDATESCHEDULE("updateSchedule"),
  UPDATEORDER("updateOrder"),
  INITSCHEDULESTOPORDER("initScheduleStopOrder"),
  UPDATESCHEDULEORDER("updateScheduleOrder"),
  UPDATESCHEDULESTOPORDER("updateScheduleStopOrder"),
  DELSCHEDULE("delSchedule");

  /**
   * <p>
   * The meta data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  private String type;

}
