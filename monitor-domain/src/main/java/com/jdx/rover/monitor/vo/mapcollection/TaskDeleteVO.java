/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/26 16:53
 * @description 勘查任务删除VO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskDeleteVO extends TaskBaseVO implements Serializable {

    /**
     * 点击删除时Tab类型
     * @see com.jdx.rover.monitor.enums.mapcollection.TabTypeEnum
     */
    @NotBlank(message = "Tab类型不能为空")
    private String tabType;
}
