/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.mini;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a mini-monitor order info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorOrderInfoDTO {

  /**
   * <p>
   * Represents the id of the order. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the order id of the order. The default value is null. It's changeable.
   * </p>
   */
  private String orderId;

  /**
   * <p>
   * Represents the number of the package. The default value is null. It's changeable.
   * </p>
   */
  private Integer packageTotal;

  /**
   * <p>
   * Represents the delivery state of the order. The default value is null. It's changeable.
   * </p>
   */
  private String deliveryState;

  /**
   * <p>
   * Represents the contact of the order. The default value is null. It's changeable.
   * </p>
   */
  private String contact;

  /**
   * <p>
   * Represents the name of the order. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the stop id of order. The default value is null. It's changeable.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the stop id of order. The default value is null. It's changeable.
   * </p>
   */
  private String stopName;

  /**
   * <p>
   * Represents the load stop id of order. The default value is null. It's changeable.
   * </p>
   */
  private Integer loadStopId;

  /**
   * <p>
   * Represents the grid number list of order. The default value is null. It's changeable.
   * </p>
   */
  private List<Integer> gridNoList;

  /**
   * <p>
   * Represents the load method of order. The default value is null. It's changeable.
   * </p>
   */
  private String loadMethod;

  /**
   * <p>
   * Represents the delivery model of order. The default value is null. It's changeable.
   * </p>
   */
  private String deliveryModel;

  /**
   * <p>
   * Represents the verify code of order. The default value is null. It's changeable.
   * </p>
   */
  private String verifyCode;

  /**
   * <p>
   * Represents the vehicle name of order. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;
}
