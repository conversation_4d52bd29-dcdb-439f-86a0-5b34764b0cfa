/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.dto.websocket;

import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 调用服务返回结果信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class WsResult<T> implements Serializable {
  private static final long serialVersionUID = 1L;
  /**
   * 成功代码
   */
  public static final String STATUS_SUCCESS = HttpCodeEnum.OK.getValue();
  /**
   * 失败代码
   */
  public static final String STATUS_ERROR = HttpCodeEnum.INNER_SERVER_ERROR.getValue();
  /**
   * 事件类型
   */
  private String eventType;

  /**
   * 返回编码
   */
  private String code;

  /**
   * 返回信息
   */
  private String message;

  /**
   * 返回业务数据
   */
  private T data;

  /**
   * 请求成功, 无数据返回
   *
   * @param eventType 事件类型
   * @return success: code, message
   */
  public static WsResult success(String eventType) {
    return success(eventType, null);
  }

  /**
   * 请求成功, 返回数据
   *
   * @param eventType 事件类型
   * @param data      请求成功的数据
   * @return success: code, message, data
   */
  public static <T> WsResult success(String eventType, T data) {
    WsResult wsResult = new WsResult();
    wsResult.setEventType(eventType);
    wsResult.setCode(HttpCodeEnum.OK.getValue());
    wsResult.setData(data);
    return wsResult;
  }

  /**
   * 请求失败, 返回自定义 code 和自定义 message
   *
   * @param eventType    事件类型
   * @param code         自定义 code
   * @param errorMessage 自定义 message
   * @return fail: code, message
   */
  public static WsResult error(String eventType, String code, String errorMessage) {
    WsResult wsResult = new WsResult();
    wsResult.setEventType(eventType);
    wsResult.setCode(code);
    wsResult.setMessage(errorMessage);
    return wsResult;
  }

  /**
   * 请求失败, 返回错误信息描述
   *
   * @param eventType    事件类型
   * @param errorMessage 定制错误信息描述,默认是500错误码
   * @return fail: code, message
   */
  public static WsResult error(String eventType, String errorMessage) {
    return error(eventType, HttpCodeEnum.INNER_SERVER_ERROR.getValue(), errorMessage);
  }

  /**
   * 请求失败, 返回500服务端错误
   *
   * @param eventType 事件类型
   * @return fail: code, message
   */
  public static WsResult error(String eventType) {
    return error(eventType, HttpCodeEnum.INNER_SERVER_ERROR.getValue(), HttpCodeEnum.INNER_SERVER_ERROR.getTitle());
  }

  /**
   * 判断返回结果是否成功
   */
  public static boolean isSuccess(WsResult wsResult) {
    return Objects.nonNull(wsResult) && STATUS_SUCCESS.equals(wsResult.getCode());
  }
}
