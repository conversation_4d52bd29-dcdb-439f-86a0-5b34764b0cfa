/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.cockpit;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 座舱看板页列表
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MultiCockpitPageDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 工作座席
   */
  private List<MultiCockpitDTO> workCockpitList;

  /**
   * 休息座席
   */
  private List<MultiCockpitDTO> restCockpitList;

  /**
   * 离线座席
   */
  private List<MultiCockpitDTO> offlineCockpitList;

}
