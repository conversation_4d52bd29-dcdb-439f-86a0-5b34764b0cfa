/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.vehicle;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 车辆规划状态DO
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Data
public class VehiclePlanningStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 是否路口
     */
    private Boolean isInIntersection;

    /**
     * 是否在门/杆前
     */
    private Boolean isInFrontOfGate;

    /**
     * 是否在停靠
     */
    private Boolean isInPark;
}
