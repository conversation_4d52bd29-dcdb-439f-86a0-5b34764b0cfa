/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.vo.vehicle;

import lombok.Data;

import java.util.List;

/**
 * 监控多车页
 *
 * <AUTHOR>
 */
@Data
public class MultiVehicleRealtimeVO {
  /**
   * 用户名
   */
  private String username;
  /**
   * 座席编号
   */
  private String cockpitNumber;
  /**
   * 车辆业务类型(配送/售卖)
   */
  private String vehicleBusinessType;

  /**
   * 车辆用途(TEST测试/OPERATION运营)
   */
  private List<String> vehicleUseCaseList;

  /**
   * 当前分类(失联/碰撞/传感器异常/运营报警/停车/电量/胎压/正常/离线/全部)
   */
  private String currentCategory;

  /**
   * 排序类型(VEHICLE_NAME,BUSINESS_TYPE,POWER)
   */
  private String sortType;

  /**
   * 标签类型(ERROR,NORMAL,OFFLINE,ALL)
   */
  private String tabType;

  /**
   * 多车页分页对象
   */
  private List<MultiVehiclePageVO> multiVehiclePageList;
}
