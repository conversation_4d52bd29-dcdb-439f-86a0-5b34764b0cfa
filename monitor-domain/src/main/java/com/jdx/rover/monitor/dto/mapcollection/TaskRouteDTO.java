/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:06
 * @description 获取勘查任务关联线路
 */
@Data
public class TaskRouteDTO implements Serializable {

    /**
     * 勘查任务名称
     */
    private String taskName;

    /**
     * 勘查线路总里程
     */
    private Double totalMileage;

    /**
     * 线路颜色
     * @see com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum
     */
    private String taskRouteColor;

    /**
     * 点位列表
     */
    private List<PositionDTO> taskRouteList;

    /**
     * 路线名称列表
     */
    private List<String> roadNameList;

    /**
     * 四车道列表
     */
    private List<FourLaneDTO> fourLaneList;

    @Data
    public static class PositionDTO implements Serializable {

        /**
         * 纬度
         */
        private Double latitude;

        /**
         * 经度
         */
        private Double longitude;
    }

    @Data
    public static class FourLaneDTO implements Serializable {

        /**
         * 四车道起点名称
         */
        private String startAddress;

        /**
         * 四车道终点名称
         */
        private String endAddress;

        /**
         * 四车道点位对应taskRouteList下标
         */
        private List<Integer> routeIndexList;
    }
}
