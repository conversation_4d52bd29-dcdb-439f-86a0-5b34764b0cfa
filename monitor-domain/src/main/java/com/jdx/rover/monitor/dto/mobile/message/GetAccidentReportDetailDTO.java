package com.jdx.rover.monitor.dto.mobile.message;

import lombok.Data;

import java.util.List;

/**
 * 事故单详情
 */
@Data
public class GetAccidentReportDetailDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 处理方式
     */
    private String handleMethod;

    /**
     * 是否需要赔偿
     */
    private Boolean compensated;

    /**
     * 金额
     */
    private String amount;

    /**
     * 附件
     */
    private List<AccidentAttachmentDTO> attachmentList;

    /**
     * 是否需要上报监管
     */
    private Boolean isReportVehicleNet;

    /**
     * 事故分类
     */
    private String accidentType;

    /**
     * 事故责任
     */
    private String accidentJudge;

    /**
     * 原因
     */
    private String reason;
}
