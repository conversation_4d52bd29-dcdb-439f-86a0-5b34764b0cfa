/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.jira;

import java.util.List;

import lombok.Data;

/**
 * <p>
 * This is a jira field data transform object entity..
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraFieldDTO {

  /**
   * <p>
   * Represents the issue type list of jira field. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<JiraIssueTypeDTO> issueType;

  /**
   * <p>
   * Represents the basic component list of jira field. The default value is null.
   * It's changeable.
   * </p>
   */
  private List<JiraBasicComponentDTO> basicComponent;

  /**
   * <p>
   * Represents the basic priority list of jira field. The default value is null.
   * It's changeable.
   * </p>
   */
  private List<JiraBasicPriorityDTO> basicPriority;

  /**
   * <p>
   * Represents the defect type list of jira field. The default value is null.
   * It's changeable.
   * </p>
   */
  private List<JiraCustomFieldDTO> defectType;

  /**
   * <p>
   * Represents the severity list of jira field. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<JiraCustomFieldDTO> severity;

  /**
   * <p>
   * Represents the discovery phase list of jira field. The default value is null.
   * It's changeable.
   * </p>
   */
  private List<JiraCustomFieldDTO> discoveryPhase;

  /**
   * <p>
   * Represents the discover situation list of jira field. The default value is
   * null. It's changeable.
   * </p>
   */
  private List<JiraCustomFieldDTO> discoverSituation;

  /**
   * <p>
   * Represents the version list of jira field. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<JiraVersionDTO> version;

}
