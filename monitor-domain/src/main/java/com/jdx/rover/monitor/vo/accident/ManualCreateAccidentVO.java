package com.jdx.rover.monitor.vo.accident;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 手工创建事故
 */
@Data
public class ManualCreateAccidentVO {

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 事故分类
     */
    @NotBlank(message = "事故分类不能为空")
    private String technicalSupportAccidentType;

    /**
     * 事故等级
     */
    @NotBlank(message = "事故等级不能为空")
    private String technicalSupportAccidentLevel;

    /**
     * 事故描述
     */
    private String technicalSupportDescription;

    /**
     * 事故发生时间
     */
    private Date accidentTime;

    /**
     * 事故发生时间
     */
    private Date accidentStartTime;

    /**
     * 事故发生时间
     */
    private Date accidentEndTime;

    /**
     * 事故发生地址
     */
    @NotBlank(message = "事故发生地址不能为空")
    private String accidentAddress;

    /**
     * 事故附件
     */
    private List<MonitorAccidentAttachmentVO> technicalSupportAttmentList;
}
