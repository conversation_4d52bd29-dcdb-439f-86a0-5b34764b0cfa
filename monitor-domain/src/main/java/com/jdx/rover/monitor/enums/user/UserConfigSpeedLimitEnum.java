package com.jdx.rover.monitor.enums.user;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

import java.util.Map;

/**
 * 速度状态枚举类
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
@Slf4j
public enum UserConfigSpeedLimitEnum {
    /**
     * 速度状态
     */
    SPEED_LIMIT_5(5, "5km/h", 1.38),
    SPEED_LIMIT_10(10, "10km/h", 2.77),
    SPEED_LIMIT_15(15, "15km/h", 4.16),
    SPEED_LIMIT_20(20, "20km/h", 5.55),
    SPEED_LIMIT_25(25, "25km/h", 6.94),
    ;

    private static final Map<Integer, UserConfigSpeedLimitEnum> CODE_MAP = EnumUtils.getEnumMap(UserConfigSpeedLimitEnum.class, UserConfigSpeedLimitEnum::getCode);

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 每秒速度
     */
    private final Double speedSecond;

    /**
     * 通过code获取枚举
     */
    public static UserConfigSpeedLimitEnum getByCode(final Integer code) {
        return CODE_MAP.getOrDefault(code, SPEED_LIMIT_15);
    }
}
