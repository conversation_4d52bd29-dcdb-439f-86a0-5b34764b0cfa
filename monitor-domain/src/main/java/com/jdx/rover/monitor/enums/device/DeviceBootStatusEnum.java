/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 启动状态类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceBootStatusEnum {

    /**
     * 开始启动。
     */
    BOOT_STATUS_START("BOOT_STATUS_START", "开始启动"),
    /**
     * 启动中。
     */
    BOOT_STATUS_PROCESS("BOOT_STATUS_PROCESS", "启动中"),

    /**
     * 启动成功。
     */
    BOOT_STATUS_SUCCESS("BOOT_STATUS_SUCCESS", "启动成功"),
    /**
     * 启动失败。
     */
    BOOT_STATUS_FAIL("BOOT_STATUS_FAIL", "启动失败"),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceBootStatusEnum 枚举类型。
     */
    public static DeviceBootStatusEnum getByValue(String value) {
        for (DeviceBootStatusEnum em : DeviceBootStatusEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}
