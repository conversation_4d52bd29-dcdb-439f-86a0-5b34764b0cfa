package com.jdx.rover.monitor.enums.message;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;


/**
 * 通知消息类型
 * @Date 2024/11/1
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum NotifyMessageTypeEnum {
  BUMP("BUMP", "碰撞"),
  ACCIDENT("ACCIDENT", "事故"),
  DRIVE_LANE("DRIVE_LANE", "机动车道"),


  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;


  /**
   * 根据字符串值获取对应的 NotifyMessageTypeEnum 枚举类型。
   * @param value 要查找的枚举类型的字符串值。
   * @return 对应的 NotifyMessageTypeEnum 枚举类型，若未找到则返回 null。
   */
  public static NotifyMessageTypeEnum of(String value) {
    for (NotifyMessageTypeEnum em : NotifyMessageTypeEnum.values()) {
      if (Objects.equals(value, em.getValue())) {
        return em;
      }
    }
    return null;
  }
}
