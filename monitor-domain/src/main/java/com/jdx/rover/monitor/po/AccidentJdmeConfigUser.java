package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 事故消息推送配置用户关联表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AccidentJdmeConfigUser extends BaseModel {
    /**
     * 配置编号：accident_jdme_config.id
     */
    private String configId;
    /**
     * 关联用户类型：member-成员用户；at-需要艾特的用户
     */
    private String type;
    /**
     * 用户ERP
     */
    private String pin;
    /**
     * 用户昵称
     */
    private String nickname;
}
