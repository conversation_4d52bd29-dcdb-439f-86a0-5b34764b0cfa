package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故模块枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentModuleEnum {

    HARDWARE(1, "硬件"),
    PERCEPTION(2, "感知"),
    LOCALIZATION(3, "定位"),
    PNC(4, "PNC"),
    ARCHITECTURE(5, "架构"),
    REMOTE_SECURITY_OFFICER(6, "远程安全员"),
    MAP(7, "地图"),
//    TECHNICAL_SUPPORT(8, "技术支持"),
    OPERATIONS_PERSONNEL(9, "运营人员"),
    OTHER(10, "其他")
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentModuleEnum accidentModuleEnum : AccidentModuleEnum.values()) {
            if (accidentModuleEnum.getValue().equals(value)) {
                return accidentModuleEnum.getName();
            }
        }
        return null;
    }
}
