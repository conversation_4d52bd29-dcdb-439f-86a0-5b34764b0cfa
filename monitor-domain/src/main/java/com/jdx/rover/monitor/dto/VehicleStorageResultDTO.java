package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.List;

/**
 * 车辆硬盘空间返回对象
 */
@Data
public class VehicleStorageResultDTO {

    private Integer total;

    private List<VehicleStorage> rows;

    /**
     * 车辆储存空间
     */
    @Data
    public static class VehicleStorage {

        /**
         * 车辆硬盘信息
         */
        private VehicleDiskInfo vehicleDiskInfoVo;

        /**
         * 车牌号
         */
        private String vehicleName;

        /**
         * 是否在线
         */
        private boolean isOnline;
    }

    @Data
    public static class VehicleDiskInfo {

        /**
         * 总共大小
         */
        private Double total;

        /**
         * 剩余空间
         */
        private Double free;
    }
}
