/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

import java.io.Serializable;

/**
 * 视同到达信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Data
public class RobotAsArrivedDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 当前机器人任务标识
   */
  private String taskId;


}
