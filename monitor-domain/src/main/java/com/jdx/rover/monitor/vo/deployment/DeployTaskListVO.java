/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.deployment;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:31
 * @description 获取部署地图勘查任务列表
 */
@Data
public class DeployTaskListVO {

    /**
     * 纬度，WGS84
     */
    private Double latitude;

    /**
     * 经度，WGS84
     */
    private Double longitude;

    /**
     * 元素类型
     * @see com.jdx.rover.monitor.enums.deployment.ElementTypeEnum
     */
    private String elementType;

    /**
     * 元素id
     */
    private String elementId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 任务创建人
     */
    private String creatorUsername;

    /**
     * 任务状态
     */
    private String taskStatus;
}
