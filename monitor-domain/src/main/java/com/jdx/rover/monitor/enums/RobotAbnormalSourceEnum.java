/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums;

import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 机器人异常模块来源
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RobotAbnormalSourceEnum {
  // 硬件、算法、本体、节点程序、
  HARDWARE("hardware", "硬件"),
  ALGORITHM("algorithm", "算法"),
  NOUMENON("NOUMENON", "本体"),
  NODE("node", "节点程序"),

  ;

  /**
   * 来源
   */
  private String source;

  /**
   * 来源名
   */
  private String sourceName;

  public static RobotAbnormalSourceEnum of(String source) {
    for (RobotAbnormalSourceEnum em : RobotAbnormalSourceEnum.values()) {
      if (Objects.equals(source, em.getSource())) {
        return em;
      }
    }
    return null;
  }

}
