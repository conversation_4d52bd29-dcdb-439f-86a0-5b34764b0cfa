/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.vo.drive;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 远程指令VO
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
public class DriveRemoteCommandVO implements Serializable {
    /**
     * 序列化ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    @NotBlank(message = "车号不能为空")
    private String vehicleName;

    /**
     * 驾驶舱编号
     */
    @NotBlank(message = "驾驶舱号不能为空")
    private String cockpitNumber;

    /**
     * 驾驶舱团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 驾驶舱用户名称
     */
    @NotBlank(message = "驾驶舱用户名不能为空")
    private String cockpitUserName;

    /**
     * 远程指令类型
     */
    private String remoteCommandType;

    /**
     * 操作消息
     */
    private String operationMessage;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date requestTime;

    /**
     * 数据
     */
    private Float data;
}
