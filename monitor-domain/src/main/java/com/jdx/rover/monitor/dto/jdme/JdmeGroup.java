package com.jdx.rover.monitor.dto.jdme;

import lombok.Data;

import java.util.List;

@Data
public class JdmeGroup {
    //群名称
    private String name;
    //群公告
    private String notice;
    //群头像icon的URL地址
    private String avatar;
    //群简介
    private String intro;
    //群主
    private JdmeGroupUser owner;
    //群成员, 人数上限100，机器人上限5个
    private List<JdmeGroupUser> members;
    //建群唯一key，如果相同则是重复群，拒绝创建
    private String uniqueKey;
}
