package com.jdx.rover.monitor.domain.jsonhandler;

import com.alibaba.fastjson2.TypeReference;
import com.jdx.rover.monitor.po.mapcollection.json.RealRoutePoint;

import java.util.List;

/**
 * 采图任务点位映射表
 */
public class RealRouteListTypeHandler extends ListTypeHandler<RealRoutePoint>{
    @Override
    protected TypeReference<List<RealRoutePoint>> specificType() {
        return new TypeReference<>() {};
    }
}
