package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故标签枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentTagEnum {

    RADAR(1,"雷达", AccidentModuleEnum.HARDWARE),
    CAMERA(2,"相机", AccidentModuleEnum.HARDWARE),
    IMU(3, "IMU", AccidentModuleEnum.HARDWARE),
    CHASSIS(4, "底盘", AccidentModuleEnum.HARDWARE),
    CALIBRATION_EXCEPTION(5,"标定异常", AccidentModuleEnum.HARDWARE),
    STATIC_OBSTACLE_WITH_SPEED(6, "静止障碍物带速度", AccidentModuleEnum.PERCEPTION),
    SPEED_OUTPUT_ABNORMAL(7, "速度大小输出异常", AccidentModuleEnum.PERCEPTION),
    SPEED_DIRECTION_OUTPUT_ABNORMAL(8, "速度方向输出异常", AccidentModuleEnum.PERCEPTION),
    LOW_OBSTACLE_DETECTION(9, "低矮漏检测", AccidentModuleEnum.PERCEPTION),
    MODEL_TYPE_ABNORMAL(10,"模型类型异常", AccidentModuleEnum.PERCEPTION),
    OBSTACLE_TYPE_RECOGNITION_ABNORMAL(11, "障碍物类型识别异常", AccidentModuleEnum.PERCEPTION),
    TREE_MISS_DIVISION(12, "树枝误分割", AccidentModuleEnum.PERCEPTION),
    GROUND_MISS_DIVISION(13, "地面误分割", AccidentModuleEnum.PERCEPTION),
    SEMANTIC_SEGMENTATION_ERROR(14, "语义分割错误", AccidentModuleEnum.PERCEPTION),
    PERCEPTION_HZ_FATAL(15,"感知Hz Fatal", AccidentModuleEnum.PERCEPTION),
    LOCALIZATION_INITIALIZATION_DRIFT(16,"定位初始化飘", AccidentModuleEnum.LOCALIZATION),
    CONTINUOUS_LOCALIZATION_DRIFT(17,"连续定位飘", AccidentModuleEnum.LOCALIZATION),
    LOCALIZATION_MODULE_HZ_FATAL(18, "定位模块hz FATAL", AccidentModuleEnum.LOCALIZATION),
    OBSTACLE_INTENTION_INACCURATE(19, "障碍物意图不准", AccidentModuleEnum.PNC),
    MULTI_VEHICLE_INTERACTION(20, "多无人车交互", AccidentModuleEnum.PNC),
    PNC_SOLVING_FAILURE(21, "PNC求解失败", AccidentModuleEnum.PNC),
    PATH_PLANNING_UNREASONABLE(22, "路径规划不合理", AccidentModuleEnum.PNC),
    PREDICTION_ROUTE_INACCURATE(23, "预测路线不准确", AccidentModuleEnum.PNC),
    SPEED_PLANNING_UNREASONABLE(24, "速度规划不合理", AccidentModuleEnum.PNC),
    PNC_HZ_FATAL(25, "PNC hz Fatal", AccidentModuleEnum.PNC),
    CPU_OVERLOAD(26, "CPU过高", AccidentModuleEnum.ARCHITECTURE),
    GPU_OVERLOAD(27, "GPU过高", AccidentModuleEnum.ARCHITECTURE),
    MEMORY_OVERLOAD(28, "内存过高", AccidentModuleEnum.ARCHITECTURE),
    MODULE_HZ_FATAL(29, "模块hz FATAL", AccidentModuleEnum.ARCHITECTURE),
    CORE_DUMP(30, "core", AccidentModuleEnum.ARCHITECTURE),
    NETWORK_EXCEPTION(31, "网络异常", AccidentModuleEnum.REMOTE_SECURITY_OFFICER),
    UNREGULATED_OPERATION(32, "人员操作不规范", AccidentModuleEnum.REMOTE_SECURITY_OFFICER),
    MAP_NOT_UPDATED(33, "地图未更新", AccidentModuleEnum.MAP),
//    SUPPORT_UNREGULATED_OPERATION(34, "人员操作不规范", AccidentModuleEnum.TECHNICAL_SUPPORT),
    OPERATION_UNREGULATED_OPERATION(35, "人员操作不规范", AccidentModuleEnum.OPERATIONS_PERSONNEL),
    NO_FAULT_ACCIDENT(36, "无责事故", AccidentModuleEnum.OTHER),
    OUT_OF_ODD(37, "ODD外", AccidentModuleEnum.OTHER),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * 父级枚举
     */
    private final AccidentModuleEnum accidentModuleEnum;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentTagEnum accidentTagEnum : AccidentTagEnum.values()) {
            if (accidentTagEnum.getValue().equals(value)) {
                return accidentTagEnum.getName();
            }
        }
        return null;
    }
}
