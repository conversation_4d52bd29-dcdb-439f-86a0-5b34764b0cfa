package com.jdx.rover.monitor.dto.mobile.common;

import lombok.Data;

/**
 * @description: GetSelectVehicleListDTO
 * @author: wangguotai
 * @create: 2024-07-15 14:23
 **/
@Data
public class GetSelectVehicleListDTO {

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 系统状态
     */
    private String systemStatus;

    /**
     * 经度（cj02坐标系）
     */
    private Double longitude;

    /**
     * 纬度（cj02坐标系）
     */
    private Double latitude;

    /**
     * 接管状态
     */
    private String takeoverStatus;

    /**
     * 接管来源
     */
    private String takeoverSource;

    /**
     * 接管用户
     */
    private String takeoverUserName;

    /**
     * 系统状态（排序）
     */
    private Integer systemStatusSort;

    /**
     * 距离（千米）
     */
    private Double distance;
}