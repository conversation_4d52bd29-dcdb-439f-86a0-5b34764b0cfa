package com.jdx.rover.monitor.dto.transport;

import lombok.Data;

/**
 * @description: 车辆遥控-获取车辆选择列表-响应对象
 * @author: wangguotai
 * @create: 2025-02-06 17:22
 **/
@Data
public class GetSelectVehicleListDTO {

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 系统状态
     */
    private String systemStatus;

    /**
     * 接管状态
     */
    private String takeoverStatus;

    /**
     * 接管来源
     */
    private String takeoverSource;

    /**
     * 接管用户
     */
    private String takeoverUserName;

    /**
     * 系统状态（排序）
     */
    private Integer systemStatusSort;

    /**
     * 距离（千米）
     */
    private Double distance;
}