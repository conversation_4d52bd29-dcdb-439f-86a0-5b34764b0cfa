/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import lombok.Data;

import java.util.List;

/**
 * 勘查任务导出请求VO
 */
@Data
public class MapCollectionTaskExportVO {

    /**
     * 任务状态列表，为空则查询全部
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum
     */
    private List<String> taskStatusList;

    /**
     * 城市ID，为空则查询全部
     */
    private List<Integer> cityId;

    /**
     * 站点ID，为空则查询全部
     */
    private List<Integer> stationId;

    /**
     * 任务ID列表，为空则查询全部
     */
    private List<Integer> taskIdList;
}
