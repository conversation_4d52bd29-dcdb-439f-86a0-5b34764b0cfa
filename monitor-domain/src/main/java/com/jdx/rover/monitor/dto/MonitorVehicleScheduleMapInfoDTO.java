/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a vehicle map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleScheduleMapInfoDTO {

  /**
   * <p>
   * Represents the vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the vehicle lat. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the vehicle lon. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the station map info. The default value is null. It's changeable.
   * </p>
   */
  private MonitorStationMapInfoDTO station;

  /**
   * <p>
   * Represents the stop map info. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorStopMapInfoDTO> stop;

  /**
   * <p>
   * Represents the finished routing point. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointEntity> finishedRoutingPoint;

  /**
   * <p>
   * Represents the planning routing point. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointEntity> planningRoutingPoint;

}
