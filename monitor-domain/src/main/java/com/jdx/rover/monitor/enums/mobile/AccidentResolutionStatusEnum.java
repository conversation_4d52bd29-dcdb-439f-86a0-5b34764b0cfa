package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故解决情况枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentResolutionStatusEnum {

    PENDING("PENDING","待解决"),
    IN_PROGRESS("IN_PROGRESS", "解决中"),
    RELEASED_OBSERVING("RELEASED_OBSERVING", "已发版待观察"),
    RESOLVED("RESOLVED", "已解决"),
    NOT_REQUIRED("NOT_REQUIRED", "无需解决"),
    ON_HOLD( "ON_HOLD","挂起");
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentResolutionStatusEnum accidentResolutionStatusEnum : AccidentResolutionStatusEnum.values()) {
            if (accidentResolutionStatusEnum.getValue().equals(value)) {
                return accidentResolutionStatusEnum.getName();
            }
        }
        return null;
    }
}
