/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.cockpit;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 驾驶舱状态信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Data
public class CockpitTeamStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 驾驶团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 驾驶团队名称
     */
    private String cockpitTeamName;

    /**
     * 处理工单数
     */
    private Integer completeIssueCount;

    /**
     * 待处理工单数
     */
    private Integer waitAcceptIssueCount;

    /**
     * 工单数据统计日期 格式yyyy-MM-dd
     */
    private String issueStatisticDate;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * 座席列表
     */
    private List<String> cockpitList;
}
