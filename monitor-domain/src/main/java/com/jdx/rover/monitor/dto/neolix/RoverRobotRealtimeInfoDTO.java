/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.neolix;

import com.jdx.rover.monitor.dto.robot.RobotDeviceBasicDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 无人车机器人实时心跳信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/03/05
 */
@Data
public class RoverRobotRealtimeInfoDTO extends RobotDeviceBasicDTO {

    /**
     * 在线状态
     */
    private Integer onlineStatus;

    /**
     * 车辆状态
     * 自动驾驶AUTO_DRIVE
     * 手动遥控REMOTE_MOBILE_CTRL
     * 远程驾驶REMOTE_JOYSTICK_CTRL
     */
    private String vehicleState;

    /**
     * 系统状态
     * 正常 NORMAL/异常ABNORMAL
     */
    private String systemState;

    /**
     * 是否充电
     */
    private Integer charging;

    /**
     * 朝向
     */
    private Double heading;

    /**
     * 当前经度位置
     */
    private Double latitude;

    /**
     * 当前纬度位置
     */
    private Double longitude;

    /**
     * 当前电量
     */
    private Double power;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 停靠点
     */
    private Integer currentStopId;

    /**
     * 里程
     */
    private Double mileageToNextStop;

    /**
     * 完成里程
     */
    private Double currentStopFinishedMileage;

    /**
     * 时间
     */
    private Date reportTime;

}
