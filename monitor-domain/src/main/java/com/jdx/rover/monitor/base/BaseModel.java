package com.jdx.rover.monitor.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @description: 领域基础模型（包含逻辑删除）
 * @author: wanggu<PERSON>i
 * @create: 2024-05-24 09:17
 **/
@Data
public class BaseModel {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 最后修改者
     */
    private String modifyUser;

    /**
     * 最近修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除
     */
    @TableLogic(value = "'1970-01-01 08:00:01'", delval = "now()")
    private Date deleted;
}
