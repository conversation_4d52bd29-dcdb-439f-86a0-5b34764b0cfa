/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.vehicle.online;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * The enumerate online state of vehicle.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleOnlineEnum {
  /**
   * The enumerate online state of vehicle.
   */
  ONLINE("ONLINE", "在线"),
  ;

  /**
   * The online state of vehicle.
   */
  private String online;

  /**
   * The online state name of vehicle.
   */
  private String onlineName;
}
