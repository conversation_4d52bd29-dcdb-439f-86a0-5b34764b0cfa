package com.jdx.rover.monitor.dto.data;

import lombok.Data;

/**
 * 驾舱信息传输对象
 */
@Data
public class GetCockpitDataPageDTO {

    /**
     * 坐席号
     */
    private String cockpitNumber;

    /**
     * 坐席类型
     */
    private String cockpitType;

    /**
     * 坐席类型名称
     */
    private String cockpitTypeName;

    /**
     * 所属团队
     */
    private String cockpitTeamName;

    /**
     * 在线时间
     */
    private Integer onlineDuration;

    /**
     * 休息时间
     */
    private Integer restDuration;

    /**
     * 听单总用时
     */
    private Integer listeningModeCost;

    /**
     * 受理工单次数
     */
    private Integer acceptCount;

    /**
     * 完单次数
     */
    private Integer finishCount;

    /**
     * 转单次数
     */
    private Integer transferCount;

    /**
     * 承接转单待受理次数
     */
    private Integer acceptTransferCount;

    /**
     * 抛单次数
     */
    private Integer discardCount;

    /**
     * 巡查提报工单次数
     */
    private Integer patrolReportCount;
}
