/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:58
 * @description 获取全部城市及站点
 */
@Data
public class CityStationDTO implements Serializable {

    /**
     * 城市列表
     */
    private List<CityDTO> cityList;

    @Data
    public static class CityDTO implements Serializable {

        /**
         * 城市ID
         */
        private Integer cityId;

        /**
         * 城市名称
         */
        private String cityName;

        /**
         * 站点列表
         */
        private List<StationDTO> stationList;
    }

    @Data
    public static class StationDTO implements Serializable {

        /**
         * 站点ID
         */
        private Integer stationId;

        /**
         * 站点名称
         */
        private String stationName;
    }
}
