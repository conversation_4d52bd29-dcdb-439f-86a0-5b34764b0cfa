/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <p>
 * This is a view object for mini monitor station info identity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorStationInfoRequestVO {

  /**
   * <p>
   * Reprensents the request station id. The default value is null. It's changeable.
   * </p>
   */
  private Integer stationId;

  /**
   * <p>
   * Reprensents the business type. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleBusinessType;

}
