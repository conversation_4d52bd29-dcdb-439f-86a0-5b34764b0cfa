/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.dto.drive;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 命令执行结果
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
public class CommandExecuteResultDTO implements Serializable {
    /**
     * 序列化ID
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 远程指令类型
     */
    private String remoteCommandType;

    /**
     * 操作消息
     */
    private String operationMessage;

    /**
     * 消息状态(RUNNING,SUCCESS,FAIL)
     */
    private String messageState;

    /**
     * 失败信息
     */
    private String failInfo;

    /**
     * 请求时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;
}
