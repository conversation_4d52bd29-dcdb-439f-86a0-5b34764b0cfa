/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 事件类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceEventTypeEnum {

    /**
     * 上报机器人异常事件。
     */
    REPORT_ROBOT_ERROR("REPORT_ROBOT_ERROR", "上报异常"),
    /**
     * 停靠点状态上报事件。
     */
    REPORT_TASK_STATUS("REPORT_TASK_STATUS", "停靠点状态上报"),
    /**
     * 启动任务上报。
     */
    REPORT_BOOT_TASK("REPORT_BOOT_TASK", "启动任务上报"),
    /**
     * HMI远程呼叫。
     */
    EVENT_OPERATION_REMOTE_CALL("EVENT_OPERATION_REMOTE_CALL", "HMI远程呼叫"),
    /**
     * 车辆意图上报事件。
     */
    REPORT_V2V_INFO("REPORT_V2V_INFO", "车辆意图"),

    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceEventTypeEnum 枚举类型。
     */
    public static DeviceEventTypeEnum getByValue(String value) {
        for (DeviceEventTypeEnum em : DeviceEventTypeEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}
