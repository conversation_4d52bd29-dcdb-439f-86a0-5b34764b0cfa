/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.ToString;

/**
 * <p>
 * This is a alarm attachment type enum.
 * </p>
 * 
 * <p>
 * </strong> alarm attachment type: </strong> enumeration of the class alarm attachment type.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum AlarmAttachmentTypeEnum {

  /**
   * <p>
   * The enumerate alam attachment type.
   * </p>
   */
  PHOTO("PHOTO", "图片"), VIDEO("VIDEO", "视频");

  /**
   * <p>
   * The type of alram event.
   * </p>
   */
  private final String value;

  /**
   * <p>
   * The type name of alarm event.
   * </p>
   */
  private final String name;

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static AlarmAttachmentTypeEnum of(final String value) {
    for (AlarmAttachmentTypeEnum itemEnum : AlarmAttachmentTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
