/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * 导航栏车辆实时状态
 * entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorNavibarVehicleRealtimeInfoDTO {

  /**
   * <p>
   * Represents the schedule state of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the schedule task type of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * Represents the business type of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the alarm event of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String alarmEvent;

  /**
   * <p>
   * Represents the speed of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private Double speed;

  /**
   * <p>
   * Represents the mileage of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private Double mileage;

}
