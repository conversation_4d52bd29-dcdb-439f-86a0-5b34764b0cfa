/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 在线状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum OnlineStatusEnum {
    // errorLevel
    UNKNOWN("未知"),
    ONLINE("在线"),
    OFFLINE("离线"),
    ;
    /**
     * 标题描述
     */
    private final String title;
}
