/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 关联任务请求实体
 */
@Data
public class AssociateTaskVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;

    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Integer taskId;

    /**
     * 动作: 0 取消认领；1 认领
     */
    @NotNull(message = "动作不能为空")
    private Integer action;

    /**
     * 驾驶舱号
     */
    private String cockpitNumber;
}
