/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import java.util.List;
import lombok.Data;

/**
 * 监控多车页调度信息
 *
 * <AUTHOR>
 */
@Data
public class MultiVehicleScheduleDTO {
  /**
   * 总订单数
   */
  private Integer totalOrderNum;

  /**
   * 完成订单数
   */
  private Integer finishedOrderNum;

  /**
   * 当前规划总里程
   */
  private Double globalMileage;

  /**
   * 当前规划已走里程
   */
  private Double arrivedMileage;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 停靠点列表
   */
  private List<MultiVehicleScheduleStopDTO> stopList;
}
