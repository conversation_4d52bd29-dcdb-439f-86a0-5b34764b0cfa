/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.drive.command;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * xata切换车辆模式枚举
 *
 * <AUTHOR>
 * @date 2025/05/22
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum XataSwitchVehilcleModeEnum {
    /**
     * 远程指令类型
     */
    RUN_HAVE_MAP("RUN_HAVE_MAP", "有图模式"),
    RUN_CALIBRATION("RUN_CALIBRATION","标定模式"),

    ;

    /**
     * 标题描述
     */
    private final String value;

    /**
     * 标题描述
     */
    private final String title;
}
