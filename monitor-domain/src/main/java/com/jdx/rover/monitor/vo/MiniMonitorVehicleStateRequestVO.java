/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is a view object for mini monitor vehicle request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorVehicleStateRequestVO {

  /**
   * <p>
   * Represents the id of the station. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private Integer stationId;

  /**
   * <p>
   * Represents the id of the stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer stopId;

  /**
   * <p>
   * Represents the business type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotEmpty
  private String vehicleBusinessType;

  /**
   * <p>
   * Represents the vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the state of schedule. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the vehicle online of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleOnline;

}
