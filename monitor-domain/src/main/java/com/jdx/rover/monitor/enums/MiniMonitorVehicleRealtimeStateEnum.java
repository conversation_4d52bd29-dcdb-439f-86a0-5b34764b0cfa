/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 运营端车辆实时状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MiniMonitorVehicleRealtimeStateEnum {
  NEAR_STATION("NEAR_STATION", "站点"),
  NORMAL("NORMAL", "正常"),
  ALARM("ALARM", "告警");

  /**
   * 值
   */
  private String value;

  /**
   * 名称
   */
  private String name;
}
