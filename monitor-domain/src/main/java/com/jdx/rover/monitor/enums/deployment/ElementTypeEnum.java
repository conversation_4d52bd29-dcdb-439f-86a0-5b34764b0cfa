/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.deployment;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/18 16:32
 * @description 元素类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum ElementTypeEnum {
    STATION("STATION", "站点"),
    STOP("STOP", "停靠点"),
    VEHICLE("VEHICLE", "车辆"),
    MAP_COLLECTION_TASK("MAP_COLLECTION_TASK", "勘查任务"),
    UNKNOWN("UNKNOWN", "未知");

    /**
     * code
     */
    private final String code;

    /**
     * name
     */
    private final String name;

    /**
     * 根据elementType获取枚举
     *
     * @param elementType elementType
     * @return ElementTypeEnum
     */
    public static ElementTypeEnum of(String elementType) {
        for (ElementTypeEnum em : ElementTypeEnum.values()) {
            if (em.getCode().equals(elementType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
