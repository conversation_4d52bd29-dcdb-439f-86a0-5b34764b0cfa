/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import cn.hutool.core.collection.CollUtil;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:46
 * @description 线路勘查筛选TAB类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum TabTypeEnum {
    // 勘察中
    PENDING_EXPLORATION("PENDING_EXPLORATION", "待勘查", List.of(TaskStatusEnum.UNDER_EXPLORATION)),

    // 待认领、采集中
    PENDING_COLLECTION("PENDING_COLLECTION", "待采集", List.of(TaskStatusEnum.PENDING_CLAIM, TaskStatusEnum.COLLECTING)),

    // 待拷贝、拷贝中
    PENDING_COPY("PENDING_COPY", "待拷贝", List.of(TaskStatusEnum.PENDING_COPY, TaskStatusEnum.COPYING)),

    // 未知
    UNKNOWN("UNKNOWN", "未知", List.of())
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 勘查任务状态枚举映射
     */
    private final List<TaskStatusEnum> taskStatusEnums;

    /**
     * 根据tabType获取枚举
     *
     * @param tabType tabType
     * @return TabTypeEnum
     */
    public static TabTypeEnum of(String tabType) {
        for (TabTypeEnum em : TabTypeEnum.values()) {
            if (em.getCode().equals(tabType)) {
                return em;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据Tab类型获取任务状态
     *
     * @param tabType tabType
     * @return List<String>
     */
    public static List<String> mappingStatus(String tabType) {
        List<TaskStatusEnum> taskStatusEnums = TabTypeEnum.of(tabType).getTaskStatusEnums();
        if (CollUtil.isEmpty(taskStatusEnums)) {
            return Collections.emptyList();
        }
        return taskStatusEnums.stream().map(TaskStatusEnum::getCode).collect(Collectors.toList());
    }
}
