/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * 远程广播
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorRemoteShoutVO {

  /**
   * <p>
   * 内容
   * </p>
   */
  private String voiceMsg;

  /**
   * <p>
   * 次数
   * </p>
   */
  private Integer rate;

  /**
   * 车辆名称
   */
  @NotBlank(message = "车辆名称不能为空")
  private String vehicleName;
}
