/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 * 机器人工单缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Data
public class RobotIssueDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String deviceName;

  /**
   * 记录时间
   */
  private Date recordTime;

  /**
   * 工单编号
   */
  private String issueNo;

  /**
   * 远程呼叫
   */
  private boolean remoteCall;

  /**
   * 跟进用户
   */
  private Map<String, String> followUserMap = new LinkedHashMap<>();


  /**
   * 告警列表
   */
  @Data
  @NoArgsConstructor
  public static class DeviceAlarmEventDO  {

    /**
     * <p>
     * 性能模块
     * </p>
     */
    private String performance;

    /**
     * <p>
     * 异常模块
     * </p>
     */
    private String abnormalModule;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误级别
     */
    private String errorLevel;

    /**
     * 错误消息
     */
    private String errorMsg;

  }

}
