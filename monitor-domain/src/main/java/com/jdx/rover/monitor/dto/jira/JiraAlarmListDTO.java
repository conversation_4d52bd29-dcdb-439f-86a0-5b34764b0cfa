/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.jira;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 提报缺陷告警列表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraAlarmListDTO {

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Rover版本
   * </p>
   */
  private String roverVersion;

  /**
   * <p>
   * 告警事件
   * </p>
   */
  private List<AlarmEvent> alarmList;

  /**
   * 告警事件
   */
  @Data
  public static class AlarmEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 告警事件
     */
    private String alarmEvent;

    /**
     * 告警编号
     */
    private String alarmNumber;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 告警描述
     */
    private String alarmMessage;
  }


}
