/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 机器人调度任务缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Data
public class RobotMapCongestionInfoDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 地图信息
   */
  private String mapId;

  /**
   * 存储当前地图所有拥堵区域的信息。
   */
  private List<CongestionArea> congestionAreaList;

  /**
   * 拥堵区域
   */
  @Data
  public static class CongestionArea {
    /**
     * H3网格索引，用于标识拥堵区域的位置。
     */
    private String h3Index;
    /**
     * 存储在该拥堵区域内的机器人ID列表。
     */
    private List<String> robotIdList;
    /**
     * 存储拥堵区域的H3网格边界点列表。
     */
    private List<H3Point> cellBoundary;
  }

  /**
   * 拥堵区域网格边界点
   */
  @Data
  public static class H3Point {
    /**
     * x坐标值，用于表示拥堵区域网格边界点的位置。
     */
    private double x;
    /**
     * y坐标值，用于表示拥堵区域网格边界点的位置。
     */
    private double y;
  }
}
