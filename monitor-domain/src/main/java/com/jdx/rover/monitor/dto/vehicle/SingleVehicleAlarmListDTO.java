/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 监控单车页告警变化信息
 *
 * <AUTHOR>
 * @date 2025/03/31
 */
@Data
public class SingleVehicleAlarmListDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 上报时间
   */
  private Date reportTime;

  /**
   * 告警事件
   */
  private List<AlarmEventDTO> alarmEventList = new ArrayList<>();
}
