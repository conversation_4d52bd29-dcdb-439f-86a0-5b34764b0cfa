/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.issue;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 报警类型枚举.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum IssueAlarmTypeEnum {
  /**
   * <p>
   * 报警类型.
   * </p>
   */
  OPERATION_ALARM("OPERATION_ALARM", "运营报警"),
  VEHICLE_ALARM("VEHICLE_ALARM", "车辆报警"),
  ;

  /**
   * <p>
   * 值.
   * </p>
   */
  private final String value;

  /**
   * <p>
   * 标题.
   * </p>
   */
  private final String title;

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static IssueAlarmTypeEnum of(final String value) {
    for (IssueAlarmTypeEnum itemEnum : IssueAlarmTypeEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
