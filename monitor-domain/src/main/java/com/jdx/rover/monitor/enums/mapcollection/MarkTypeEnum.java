/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:32
 * @description 地图标记类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum MarkTypeEnum {
    INTERSECTION("INTERSECTION", "路口"),
    ROAD_GAP("ROAD_GAP", "豁口"),
    UNKNOWN("UNKNOWN", "未知")
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 根据markType获取枚举
     *
     * @param markType markType
     * @return MarkTypeEnum
     */
    public static MarkTypeEnum of(String markType) {
        for (MarkTypeEnum em : MarkTypeEnum.values()) {
            if (em.getCode().equals(markType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
