package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 维修卡片类型转换器枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum RequireResourceTypeConvertEnum {

    PICTURE("PICTURE", "image"),
    VIDEO("VIDEO", "video"),
    ;


    private String value;

    private String name;

    /**
     * 获取名称
     */
    public static String getNameByValue(String value) {
        for (RequireResourceTypeConvertEnum requireResourceTypeConvertEnum : RequireResourceTypeConvertEnum.values()) {
            if (requireResourceTypeConvertEnum.getValue().equals(value)) {
                return requireResourceTypeConvertEnum.getName();
            }
        }
        return null;
    }
}
