/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a issue vehicle operate type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum IssueVehicleOperationEnum {
  /**
   * <p>
   * The enumerate issue operate type.
   * </p>
   */
  REMOTE_REQUEST_EMERGENCY_STOP("REMOTE_REQUEST_EMERGENCY_STOP", "点击【急停】按钮"),
  REMOTE_REQUEST_RECOVERY("REMOTE_REQUEST_RECOVERY", "点击【恢复】按钮"),
  REMOTE_REQUEST_REMOTE_RESTART("REMOTE_REQUEST_REMOTE_RESTART", "点击【远程重启】按钮"),
  REMOTE_REQUEST_PASS_LIGHT("REMOTE_REQUEST_PASS_LIGHT", "点击【通过路口】按钮"),
  REMOTE_REQUEST_CANCEL_LIGHT("REMOTE_REQUEST_CANCEL_LIGHT", "点击【取消通过】按钮"),
  REMOTE_REQUEST_AS_ARRIVED("REMOTE_REQUEST_AS_ARRIVED", "点击【视同到达】按钮"),
  REMOTE_REQUEST_REMOTE_CONTROL("REMOTE_REQUEST_REMOTE_CONTROL", "点击【远程遥控】按钮");

  /**
   * <p>
   * The meta data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;

  /**
   * <p>
   * The meta data request name corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String name;
  
  public static IssueVehicleOperationEnum of(String operationType) {
    for (IssueVehicleOperationEnum em : IssueVehicleOperationEnum.values()) {
      if (Objects.equals(operationType, em.getType())) {
        return em;
      }
    }
    return null;
  }
}
