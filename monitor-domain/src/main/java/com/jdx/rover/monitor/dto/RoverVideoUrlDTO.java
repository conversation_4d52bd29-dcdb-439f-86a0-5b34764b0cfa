/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a rover video data transform object entity..
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class RoverVideoUrlDTO<T> {

  /**
   * <p>
   * Represents the event type. The default value is null. It's changeable.
   * </p>
   */
  private String eventType;

  /**
   * <p>
   * Represents the error code. The default value is null. It's changeable.
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * Represents the message. The default value is null. It's changeable.
   * </p>
   */
  private String message;

  /**
   * <p>
   * Represents the data. The default value is null. It's changeable.
   * </p>
   */
  private T data;


}
