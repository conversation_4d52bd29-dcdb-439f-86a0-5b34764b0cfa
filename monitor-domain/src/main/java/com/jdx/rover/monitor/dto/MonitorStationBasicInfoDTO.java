/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.Map;
import lombok.Data;

/**
 * <p>
 * This is a station basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorStationBasicInfoDTO {

  /**
   * <p>
   * Represents the station's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the station's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the station's type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the vehicle list. The default value is null. It's changeable.
   * </p>
   */
  private Map<String, MonitorVehiceBasicInfoDTO> vehicle;

}
