package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum H5RemotePowerEnum {

    POWER_ON("powerOn", "上电"),
    POWER_OFF("powerOff", "下电")
    ;

    /**
     * 编码
     */
    private final String value;

    /**
     * 值
     */
    private final String name;

    /**
     * 获取枚举
     *
     * @param code code
     * @return CommandTypeEnum
     */
    public static CommandTypeEnum of(String code) {
        for (CommandTypeEnum commandTypeEnum : CommandTypeEnum.values()) {
            if (commandTypeEnum.getCode().equals(code)) {
                return commandTypeEnum;
            }
        }
        return null;
    }
}
