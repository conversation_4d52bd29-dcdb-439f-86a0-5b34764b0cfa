/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 车辆分类
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleCategoryEnum {
  CONNECTION_LOST("CONNECTION_LOST", VehicleTabTypeEnum.ERROR.getValue(), 100000.0, 110000.0, "失联"),
  VEHICLE_CRASH("VEHICLE_CRASH", VehicleTabTypeEnum.ERROR.getValue(), 110000.0, 130000.0, "碰撞"),
  MANUAL_REPORT("MANUAL_REPORT",VehicleTabTypeEnum.ERROR.getValue(),130000.0, 150000.0,"人工告警"),
  SENSOR_ERROR("SENSOR_ERROR", VehicleTabTypeEnum.ERROR.getValue(), 150000.0, 170000.0, "传感器异常"),
  OPERATION_ALARM("OPERATION_ALARM", VehicleTabTypeEnum.ERROR.getValue(), 170000.0, 230000.0, "运营报警"),
  BOOT_ALARM("BOOT_ALARM", VehicleTabTypeEnum.ERROR.getValue(), 230000.0, 260000.0, "开机异常"),
  INTERSECTION_ABNORMAL("INTERSECTION_ABNORMAL", VehicleTabTypeEnum.ERROR.getValue(), 260000.0, 310000.0, "路口异常"),
  STOP("STOP", VehicleTabTypeEnum.ERROR.getValue(), 310000.0, 410000.0, "停车"),
  CLOUD_WARN("CLOUD_WARN",VehicleTabTypeEnum.ERROR.getValue(), 410000.0, 430000.0,"云端预警"),
  LOW_BATTERY("LOW_BATTERY", VehicleTabTypeEnum.ERROR.getValue(), 430000.0, 450000.0, "电量低"),
  LOW_PRESSURE("LOW_PRESSURE", VehicleTabTypeEnum.ERROR.getValue(), 450000.0, 500000.0, "胎压异常"),
  NORMAL(VehicleTabTypeEnum.NORMAL.getValue(), VehicleTabTypeEnum.NORMAL.getValue(), 500000.0, 510000.0, "正常"),
  OFFLINE(VehicleTabTypeEnum.OFFLINE.getValue(), VehicleTabTypeEnum.OFFLINE.getValue(), 510000.0, 520000.0, "离线"),
  INTERESTED(VehicleTabTypeEnum.INTERESTED.getValue(), VehicleTabTypeEnum.INTERESTED.getValue(), 100000.0, 990000.0, "关注"),
  ALL(VehicleTabTypeEnum.ALL.getValue(), VehicleTabTypeEnum.ALL.getValue(), 100000.0, 990000.0, "全部"),
  ;

  private final String value;

  private final String tabType;
  /**
   * 起始分数(包含)
   */
  private final Double startScore;
  /**
   * 结束分数(不包含)
   */
  private Double endScore;

  private final String title;

  public static VehicleCategoryEnum of(final String value) {
    if (StringUtils.isBlank(value)) {
      return null;
    }
    for (VehicleCategoryEnum itemEnum : VehicleCategoryEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}

