/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 监控单车页异常信息
 *
 * <AUTHOR>
 */
@Data
public class SingleVehicleExceptionDTO {
  /**
   * 上报时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportTime;

  /**
   * 错误代码
   */
  private String errorCode;

  /**
   * 错误级别
   */
  private String errorLevel;

  /**
   * 错误消息
   */
  private String errorMessage;

  /**
   * 翻译消息
   */
  private String translateMessage;
}
