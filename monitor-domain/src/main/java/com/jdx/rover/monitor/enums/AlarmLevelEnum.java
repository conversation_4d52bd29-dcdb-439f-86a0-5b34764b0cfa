/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 告警级别枚举
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AlarmLevelEnum {
    /**
     * 表示紧急告警级别的常量。
     */
    ALARM_URGENT("ALARM_URGENT", "一级故障"),
    /**
     * 表示严重告警级别的常量。
     */
    ALARM_MAJOR("ALARM_MAJOR", "二级故障"),

    /**
     * 表示一般告警级别的常量。
     */
    ALARM_NORMAL("ALARM_NORMAL", "三级故障"),

    /**
     * 表示轻微告警级别的常量。
     */
    ALARM_SLIGHT("ALARM_SLIGHT", "四级故障"),

    /**
     * 表示正常。
     */
    NORMAL("NORMAL", "正常"),
    ;
    /**
     * 值
     */
    private final String value;

    /**
     * 描述
     */
    private final String name;


}
