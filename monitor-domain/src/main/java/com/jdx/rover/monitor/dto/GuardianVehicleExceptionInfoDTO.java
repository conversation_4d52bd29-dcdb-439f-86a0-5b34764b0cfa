/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a guardian exception info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleExceptionInfoDTO {

  /**
   * <p>
   * Represents the id of guardian exception info. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the vehicle entity of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the error code of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * Represents the error level of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorLevel;

  /**
   * <p>
   * Represents the error message of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorMessage;

  /**
   * <p>
   * 中文映射消息
   * </p>
   */
  private String translateMessage;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date operateTimestamp;

  /**
   * <p>
   * Represents the city entity of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station entity of guardian exception info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

}
