/***************************************************************************
 *
 * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.enums.accident;

import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 事故创建来源枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2023/06/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AccidentSourceEnum {

    /**
     * 车端上报
     */
    VEHICLE_REPORT("VEHICLE_REPORT","车端上报"),
    MANUALLY_CREATED("MANUALLY_CREATED","手动创建"),
    ;
    /**
     * 值.
     */
    private final String value;

    /**
     * 标题.
     */
    private final String title;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentSourceEnum accidentSourceEnum : AccidentSourceEnum.values()) {
            if (accidentSourceEnum.getValue().equals(value)) {
                return accidentSourceEnum.getTitle();
            }
        }
        return null;
    }
}
