/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.issue;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * This is a monitor issue change data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssuePendingDTO {

  /**
   * <p>
   * Represents the process list of the issue. The default value is null. It's changeable.
   * </p>
   */
  private List<IssueChangeDTO> process = new ArrayList<>();

  /**
   * <p>
   * Represents the finished list of the issue. The default value is null. It's changeable.
   * </p>
   */
  private List<IssueChangeDTO> finished = new ArrayList<>();
}
