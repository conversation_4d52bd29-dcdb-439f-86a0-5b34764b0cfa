/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is a view object for mini monitor alarm attention identity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorAlarmAttentionAddVO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the start report timestamp of alarm. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startTimestamp;

  /**
   * <p>
   * Represents the type of alarm event. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String alarmEventType;

}
