/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 机器人设备停靠点任务状态信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotReportTaskGoalDTO {

    /**
     * 设备编号
     */
    private String deviceName;

    /**
     * 任务标识
     */
    private String taskId;

    /**
     * 停靠点
     */
    private PoiInfo startPoint;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 原因
     */
    private String reason;

    /**
     * 当前停靠点
     */
    private PoiInfo targetPoint;

    /**
     * 规划路径
     */
    private List<String> globalPath;

    /**
     * 路径长度
     */
    private Double pathLength;

    /**
     * eta
     */
    private Double eta;

    /**
     * 点位信息
     */
    @Data
    public static class PoiInfo {
        /**
         * 点位编号
         */
        private String pointId;

        /**
         * 点位名称
         */
        private String pointName;

        /**
         * 点位类型
         */
        private Integer pointType;

        /**
         * 点位索引
         */
        private String buildId;

        /**
         * 地图编号
         */
        private String mapId;

        /**
         * 目标区域
         */
        private String regionId;

        /**
         * X坐标
         */
        private Double x;

        /**
         * Y坐标
         */
        private Double y;

        /**
         * yaw坐标
         */
        private Double yaw;

        /**
         * 停靠站ID从TaskCreate中的RobotTask中获取
         */
        private String stationId;

        /**
         * 停靠站类型，从TaskCreate中的RobotTask中获取
         */
        private String  stationType ;

    }

}
