package com.jdx.rover.monitor.dto.vehicle;

import java.io.Serializable;
import lombok.Data;

/**
 * 四路使能
 *
 * <AUTHOR>
 */
@Data
public class VehicleDrivableDirectionDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 前
   */
  private Boolean enableFront;

  /**
   * 后
   */
  private Boolean enableBack;

  /**
   * 左
   */
  private Boolean enableLeft;

  /**
   * 右
   */
  private Boolean enableRight;
}
