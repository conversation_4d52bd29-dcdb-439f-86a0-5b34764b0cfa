/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.vehicle.operate;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 允许接管状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AllowOperateEnum {
    FORBID("禁止"),
    ALLOW("允许"),
    EXIT("退出"),
    ;
    /**
     * 标题描述
     */
    private final String title;
}
