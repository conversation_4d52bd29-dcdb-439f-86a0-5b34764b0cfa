/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 拉列表请求对象
 *
 * <AUTHOR>
 */
@Data
public class CommonDrownListVO {

  /**
   * 下拉列表关键字列表
   */
  @NotEmpty(message = "下拉列表关键字列表不能为空")
  private List<String> keyList;
}
