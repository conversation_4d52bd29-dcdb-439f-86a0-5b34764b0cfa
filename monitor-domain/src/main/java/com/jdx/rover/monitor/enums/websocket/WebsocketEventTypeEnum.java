package com.jdx.rover.monitor.enums.websocket;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Websocket事件类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum WebsocketEventTypeEnum {
  HEARTBEAT	("HEARTBEAT", "PULL", "心跳"),
  MULTI_VEHICLE("MULTI_VEHICLE", "PULL", "多车页数据"),

  SINGLE_VEHICLE("SINGLE_VEHICLE", "PUSH", "单车页数据"),
  SINGLE_VEHICLE_TAKEOVER_ISSUE("SINGLE_VEHICLE_TAKEOVER_ISSUE", "PUSH", "单车页接管和工单数据"),
  SINGLE_VEHICLE_SCHEDULE("SINGLE_VEHICLE_SCHEDULE", "PUSH", "单车页调度数据"),
  SINGLE_VEHICLE_ALARM("SINGLE_VEHICLE_ALARM", "PUSH", "单车页告警列表"),
  SINGLE_VEHICLE_EXCEPTION("SINGLE_VEHICLE_EXCEPTION", "PUSH", "单车页错误信息"),
  SINGLE_VEHICLE_OPERATION("SINGLE_VEHICLE_OPERATION", "PUSH", "单车页操作日志信息"),
  SINGLE_VEHICLE_ACCIDENT("SINGLE_VEHICLE_ACCIDENT", "PUSH", "单车页事故信息"),
  SINGLE_VEHICLE_TRAFFIC_LIGHT("SINGLE_VEHICLE_TRAFFIC_LIGHT", "PUSH", "单车页红绿灯信息"),
  SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION("SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION", "PUSH", "单车页无信号路口信息"),
  SINGLE_VEHICLE_GPS("SINGLE_VEHICLE_GPS", "PUSH", "单车页GPS信号"),

  MAP_VEHICLE_SCHEDULE_REQUEST("MAP_VEHICLE_SCHEDULE_REQUEST", "PULL", "订阅地图页车辆调度变化"),
  MAP_VEHICLE_SCHEDULE_CANCEL_REQUEST("MAP_VEHICLE_SCHEDULE_CANCEL_REQUEST", "PULL", "取消订阅地图页车辆调度变化"),
  MAP_VEHICLE_POSITION_REQUEST("MAP_VEHICLE_POSITION_REQUEST", "PULL", "订阅地图页车辆实时位置"),
  MAP_VEHICLE_POSITION_UPDATE("MAP_VEHICLE_POSITION_UPDATE", "PUSH", "更新地图页车辆实时位置"),
  MAP_VEHICLE_SCHEDULE_UPDATE("MAP_VEHICLE_SCHEDULE_UPDATE", "PUSH", "更新地图页车辆调度信息"),
  MAP_SINGLE_VEHICLE_REQUEST("MAP_SINGLE_VEHICLE_REQUEST", "PULL", "订阅地图页车辆单车信息"),
  MAP_CONGESTION_INFO_UPDATE("MAP_CONGESTION_INFO_UPDATE", "PUSH", "更新地图页车辆拥堵信息"),

  ALL_ALARM_EVENT("ALL_ALARM_EVENT", "PUSH", "全部告警事件"),
  CHANGE_ALARM_EVENT("CHANGE_ALARM_EVENT", "PUSH", "变化告警事件"),
  CHANGE_VEHICLE_CONNECT("CHANGE_CONNECT", "PUSH", "连接状态变化"),
  COMMON_MESSAGE("COMMON_MESSAGE", "PUSH", "通用消息"),
  CHANGE_VEHICLE_STATE_EVENT("CHANGE_VEHICLE_STATE_EVENT", "PUSH", "变化车辆状态事件"),
  VEHICLE_VIDEO_MODE_INFO("VEHICLE_VIDEO_MODE_INFO", "PUSH", "变化视频模式事件"),

  ISSUE_SUBSCRIBE_REQUEST("ISSUE_SUBSCRIBE_REQUEST", "PUSH", "订阅工单变化"),
  ISSUE_UNSUBSCRIBE_REQUEST("ISSUE_UNSUBSCRIBE_REQUEST", "PUSH", "取消订阅工单变化"),
  ISSUE_UPDATE_EVENT("ISSUE_UPDATE_EVENT", "PUSH", "工单变化"),

  STOP("STOP", "STOP", "停止推送"),
  VEHICLE_DRIVABLE_DIRECTION("VEHICLE_DRIVABLE_DIRECTION", "PUSH", "车辆使能信息"),
  OPERATION_RECORD("OPERATION_RECORD", "PUSH", "操作记录"),
  VEHICLE_EXCEPTION("VEHICLE_EXCEPTION", "PUSH", "车辆异常信息"),

  REMOTE_REQUEST_EMERGENCY_STOP("REMOTE_REQUEST_EMERGENCY_STOP", "PULL", "急停指令"),

  REMOTE_REQUEST_TEMPORARY_STOP("REMOTE_REQUEST_TEMPORARY_STOP", "PULL", "临时接管"),
  REMOTE_REQUEST_EMERGENCY_BRAKE("REMOTE_REQUEST_EMERGENCY_BRAKE", "PULL", "急刹指令"),
  REMOTE_REQUEST_RECOVERY("REMOTE_REQUEST_RECOVERY", "PULL", "恢复指令"),
  REMOTE_REQUEST_REMOTE_RESTART("REMOTE_REQUEST_REMOTE_RESTART", "PULL", "重启指令"),
  REMOTE_REQUEST_REMOTE_CONTROL("REMOTE_REQUEST_REMOTE_CONTROL", "PULL", "遥控指令"),
  REMOTE_REQUEST_REMOTE_SUPER_CONTROL("REMOTE_REQUEST_REMOTE_SUPER_CONTROL", "PULL", "超级遥控指令"),
  REMOTE_REQUEST_REMOTE_MOBILE_CONTROL("REMOTE_REQUEST_REMOTE_MOBILE_CONTROL", "PULL", "遥杆遥控指令"),
  REMOTE_REQUEST_RESET_ABNORMAL_CONTROL("REMOTE_REQUEST_RESET_ABNORMAL_CONTROL", "PULL", "异常恢复指令"),
  REMOTE_REQUEST_ENTER_CONTROL("REMOTE_REQUEST_ENTER_CONTROL", "PULL", "进入远程遥控"),
  REMOTE_REQUEST_PASS_LIGHT("REMOTE_REQUEST_PASS_LIGHT", "PULL", "一键通过红绿灯"),
  REMOTE_REQUEST_CANCEL_LIGHT("REMOTE_REQUEST_CANCEL_LIGHT", "PULL", "取消通过红绿灯"),
  REMOTE_REQUEST_AS_ARRIVED("REMOTE_REQUEST_AS_ARRIVED", "PULL", "视同到达指令"),
  REMOTE_REQUEST_LAMP_CONTROL("REMOTE_REQUEST_LAMP_CONTROL", "PULL", "控制车灯指令"),
  REMOTE_REQUEST_QUIT_CONTROL("REMOTE_REQUEST_QUIT_CONTROL", "PULL", "退出遥控操作"),
  REMOTE_REQUEST_RETURN("REMOTE_REQUEST_RETURN", "PULL", "返程指令"),
  REMOTE_REQUEST_RELIEVE_BUTTON_STOP("REMOTE_REQUEST_RELIEVE_BUTTON_STOP", "PULL", "退出按钮急停指令"),
  REMOTE_REQUEST_USER_CONTROL("REMOTE_REQUEST_USER_CONTROL", "PULL", "用户操作指令"),
  REMOTE_REQUEST_VEHICLE_RESPONSE("REMOTE_REQUEST_VEHICLE_RESPONSE", "PUSH", "车辆遥控响应"),
  REMOTE_REQUEST_VEHICLE_ENV("REMOTE_REQUEST_VEHICLE_ENV", "PULL", "车辆使能"),
  REMOTE_CONTROL_SESSION_CLOSE("REMOTE_CONTROL_SESSION_CLOSE", "PUSH", "遥控页面关闭通知"),
  REMOTE_CONTROL_POWER_REBOOT("REMOTE_CONTROL_POWER_REBOOT", "PULL", "电源管理断电重启"),
  REMOTE_CONTROL_POWER_OFF("REMOTE_CONTROL_POWER_OFF", "PULL", "电源管理远程下电"),

  REMOTE_NO_SIGNAL_INTERSECTION("REMOTE_NO_SIGNAL_INTERSECTION", "PULL", "无保护左转"),


  REMOTE_WEB_TERMINAL_TOOL("REMOTE_WEB_TERMINAL_TOOL", "PULL", "远程程工具"),
  WEB_TERMINAL_COMMAND_DATA("WEB_TERMINAL_COMMAND_DATA", "PUSH", "远程程工具指令数据"),

  REQUEST_VEHICLE_CLOUD_MAP("REQUEST_VEHICLE_CLOUD_MAP", "PULL", "请求点云地图"),
  VEHICLE_LOCATION_MAP_RESPONSE("VEHICLE_LOCATION_MAP_RESPONSE", "PUSH", "推送车端点云地图"),
  CLOUD_LOCATION_MAP_RESPONSE("CLOUD_LOCATION_MAP_RESPONSE", "PUSH", "推送云端点云地图"),
  REMOTE_REQUEST_VEHICLE_LOCATION_POSE("REMOTE_REQUEST_VEHICLE_LOCATION_POSE", "PULL", "下发人工辅助位姿"),

  ;

  private final String value;

  /**
   * 数据传输类型(PUSH/推 PULL/拉)
   */
  private final String dataTransferType;

  private final String title;
}
