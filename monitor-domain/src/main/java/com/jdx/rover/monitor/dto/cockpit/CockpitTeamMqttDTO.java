package com.jdx.rover.monitor.dto.cockpit;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11
 */
@Data
public class CockpitTeamMqttDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 驾驶团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 驾驶团队名称
     */
    private String cockpitTeamName;

    /**
     * 处理工单数
     */
    private Integer completeIssueCount;

    /**
     * 待处理工单数
     */
    private Integer waitAcceptIssueCount;

    /**
     * 工作中数
     */
    private Integer workTotal;

    /**
     * 未工任数
     */
    private Integer restTotal;

    /**
     * 离线数
     */
    private Integer offlineTotal;

    /**
     * 座舱信息列表
     */
    private List<Cockpit> cockpitList;

    @Data
    public static class Cockpit {

        /**
         * 座舱编号
         */
        private String cockpitNumber;

        /**
         * 云控车辆名称
         */
        private String vehicleName;

        /**
         * 云控用户编号
         */
        private String cockpitUserName;

        /**
         * 驾舱类型
         */
        private String cockpitType;

        /**
         * 工作状态
         */
        private String cockpitStatus;
    }
}