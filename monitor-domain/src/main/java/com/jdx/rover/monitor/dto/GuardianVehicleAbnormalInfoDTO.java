/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a guardian abnormal info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAbnormalInfoDTO {

  /**
   * <p>
   * Represents the id of guardian abnormal info. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the vehicle entity of guardian abnormal info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the vehicle entity of station info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the error code of guardian abnormal info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * Represents the error level of guardian abnormal info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorLevel;

  /**
   * <p>
   * Represents the error message of guardian abnormal info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String errorMessage;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian abnormal info. The default value is null. It's changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startTimestamp;

  /**
   * <p>
   * Represents the end timestamp of guardian abnormal info. The default value is null. It's changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date endTimestamp;

}
