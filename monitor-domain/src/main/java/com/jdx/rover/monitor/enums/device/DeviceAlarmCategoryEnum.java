/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 告警通知紧急程度枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceAlarmCategoryEnum {

    /**
     * 紧急
     */
    CRITICAL("CRITICAL", "紧急"),
    /**
     * 重要。
     */
    SEVERITY("SEVERITY", "重要"),
    /**
     * 正常。
     */
    NORMAL("NORMAL", "正常"),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceAlarmCategoryEnum 枚举类型。
     */
    public static DeviceAlarmCategoryEnum getByValue(String value) {
        for (DeviceAlarmCategoryEnum em : DeviceAlarmCategoryEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}
