package com.jdx.rover.monitor.bo.accident;

import com.jdx.rover.infrastructure.api.domain.enums.xingyun.StatusEnum;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentBoardDetailDTO;
import com.jdx.rover.monitor.constant.BugConstant;
import com.jdx.rover.monitor.enums.mobile.AccidentModuleEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentTagEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 事故看板-事故详情
 */
@Data
public class AccidentBoardDetailBO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 事故等级
     */
    private String accidentLevel;

    /**
     * 事故模块
     */
    private Long accidentModule;

    /**
     * 事故标签
     */
    private Long accidentTag;

    /**
     * bug描述
     */
    private String bugDescription;

    /**
     * 关键问题分析
     */
    private String safetyGroupDescription;

    /**
     * 事故时间
     */
    private Date accidentTime;

    /**
     * 缺陷创建时间
     */
    private Date  bugCreateTime;

    /**
     * 缺陷处理状态
     */
    private String bugStatus;

    /**
     * 缺陷编号
     */
    private String bugCode;

    /**
     * 转换BO为DTO
     * @param accidentBoardDetailBO
     * @return
     */
    public AccidentBoardDetailDTO convertBOToDTO(AccidentBoardDetailBO accidentBoardDetailBO) {
        AccidentBoardDetailDTO accidentBoardDetailDTO = new AccidentBoardDetailDTO();
        accidentBoardDetailDTO.setAccidentNo(accidentBoardDetailBO.getAccidentNo());
        accidentBoardDetailDTO.setVehicleName(accidentBoardDetailBO.getVehicleName());
        accidentBoardDetailDTO.setAccidentLevelName(PreliminaryAccidentLevelEnum.getNameByValue(accidentBoardDetailBO.getAccidentLevel()));
        //设置事故标签
        Long accidentTag = accidentBoardDetailBO.getAccidentTag();
        List<String> accidentTagNameList = new ArrayList<>();
        if (accidentTag != null && accidentTag > 0) {
            for (AccidentTagEnum tagEnum : AccidentTagEnum.values()) {
                Integer value = tagEnum.getValue();
                if ((accidentTag & 1L << value) > 0) {
                    accidentTagNameList.add(tagEnum.getName());
                }
            }
        }
        accidentBoardDetailDTO.setAccidentTagName(CollectionUtils.isEmpty(accidentTagNameList) ? null : String.join(",", accidentTagNameList));
        //设置事故模块
        Long accidentModule = accidentBoardDetailBO.getAccidentModule();
        List<String> accidentModuleNameList = new ArrayList<>();
        if (accidentModule != null && accidentModule > 0) {
            for (AccidentModuleEnum moduleEnum : AccidentModuleEnum.values()) {
                Integer value = moduleEnum.getValue();
                if ((accidentModule & 1L << value) > 0) {
                    accidentModuleNameList.add(moduleEnum.getName());
                }
            }
        }
        accidentBoardDetailDTO.setAccidentModuleName(CollectionUtils.isEmpty(accidentModuleNameList) ? null : String.join(",", accidentModuleNameList));
        accidentBoardDetailDTO.setBugDescription(accidentBoardDetailBO.getBugDescription());
        accidentBoardDetailDTO.setSafetyGroupDescription(accidentBoardDetailBO.getSafetyGroupDescription());
        accidentBoardDetailDTO.setAccidentTime(accidentBoardDetailBO.getAccidentTime());
        accidentBoardDetailDTO.setBugCreateTime(accidentBoardDetailBO.getBugCreateTime());
        accidentBoardDetailDTO.setBugStatusName(StatusEnum.getAliasByValue(accidentBoardDetailBO.getBugStatus()));
        accidentBoardDetailDTO.setBugUrl(getBugUrl(accidentBoardDetailBO.getBugCode()));
        return accidentBoardDetailDTO;
    }

    /**
     * 根据bugCode获取bugUrl
     * @param bugCode
     * @return
     */
    private String getBugUrl(String bugCode) {
        return BugConstant.url + (bugCode.length() > 7 ? bugCode.substring(bugCode.length() - 7) : bugCode);
    }
}
