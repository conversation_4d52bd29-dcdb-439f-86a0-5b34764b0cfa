/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.scene;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 行驶道路场景
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DriveRoadSceneEnum {
    PARK("PARK", "园区"),
    OPEN("OPEN","社会道路"),
    ;
    /**
     * 名称
     */
    private final String value;

    /**
     * 描述
     */
    private final String title;
}
