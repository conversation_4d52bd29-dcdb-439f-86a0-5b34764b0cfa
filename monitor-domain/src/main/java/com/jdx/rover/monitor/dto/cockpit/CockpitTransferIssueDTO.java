/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 座席可转单
 *
 * <AUTHOR>
 * @date 2023/06/14
 */
@Data
@NoArgsConstructor
public class CockpitTransferIssueDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 座席编号
     */
    private String cockpitNumber;

    /**
     * 座席类型
     */
    private String cockpitType;

    /**
     * 座席用户
     */
    private String cockpitUserName;

    /**
     * 工单数
     */
    private Integer issueTotal;

}
