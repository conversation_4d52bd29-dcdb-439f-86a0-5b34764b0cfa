/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.Optional;

/**
 * <p>
 * This is a guardian alarm info entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAlarmInfo extends BaseDomain {

  /**
   * <p>
   * Represents the vehicle entity of guardian alarm info. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the city entity of guardian alarm info. It's changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station entity of guardian alarm info. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the alarmEvent of guardian alarm info. It's changeable.
   * </p>
   */
  private String alarmEvent;

  /**
   * <p>
   * 告警编号
   * </p>
   */
  private String alarmNumber;

  /**
   * <p>
   * 告警来源
   * </p>
   */
  private String alarmSource;

  /**
   * <p>
   * 所属工单
   * </p>
   */
  private String issueNumber;

  /**
   * <p>
   * Represents the topic of guardian alarm info. It's changeable.
   * </p>
   */
  private String topic;

  /**
   * <p>
   * Represents the description of guardian alarm info. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the component id of guardian alarm info. It's changeable.
   * </p>
   */
  private String componentId;

  /**
   * <p>
   * Represents the component name of guardian alarm info. It's changeable.
   * </p>
   */
  private String componentName;

  /**
   * <p>
   * Represents the report timestamp of guardian alarm info. It's changeable.
   * </p>
   */
  private Date reportTimestamp;

  /**
   * <p>
   * Represents the report user name of guardian alarm info. It's changeable.
   * </p>
   */
  private String reportUserName;

  /**
   * <p>
   * Represents the version of vehicle. It's changeable.
   * </p>
   */
  private String vehicleVersion;

  /**
   * <p>
   * Represents the systemState of vehicle. It's changeable.
   * </p>
   */
  private String systemState;

  /**
   * <p>
   * Represents the useCase of vehicle. It's changeable.
   * </p>
   */
  private String useCase;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian alarm info. It's
   * changeable.
   * </p>
   */
  private Date operateTimestamp;

  /**
   * <p>
   * Represents the end timestamp of guardian alarm info. It's
   * changeable.
   * </p>
   */
  private Date endTimestamp;

  /**
   * <p>
   * Represents the exception info id of guardian alarm info. It's changeable.
   * </p>
   */
  private Integer exceptionInfoId;

  /**
   * <p>
   * Represents the boot uuid id of guardian alarm info. It's changeable.
   * </p>
   */
  private String bootUuid;

  /**
   * <p>
   * Represents the boot id of guardian alarm info. It's changeable.
   * </p>
   */
  private Integer bootId;

  /**
   * <p>
   * guardian alarm info to guardian alarm info data transform object
   * </p>
   * 
   * @return The guardian alarm info data transform object
   */
  public GuardianVehicleAlarmInfoDTO toGuardianAlarmInfoDto() {
    GuardianVehicleAlarmInfoDTO guardianExceptionInfoDto = new GuardianVehicleAlarmInfoDTO();
    guardianExceptionInfoDto.setId(getId());
    guardianExceptionInfoDto.setVehicleName(vehicleName);
    guardianExceptionInfoDto.setCityName(cityName);
    guardianExceptionInfoDto.setStationName(stationName);
    Optional.ofNullable(AlarmSourceEnum.of(alarmSource)).ifPresent(v-> guardianExceptionInfoDto.setAlarmSourceName(v.getSourceName()));
    guardianExceptionInfoDto.setAlarmEvent(alarmEvent);
    guardianExceptionInfoDto.setDescription(description);
    guardianExceptionInfoDto.setAlarmSource(alarmSource);
    guardianExceptionInfoDto.setIssueNumber(issueNumber);
    guardianExceptionInfoDto.setComponentId(componentId);
    guardianExceptionInfoDto.setComponentName(componentName);
    guardianExceptionInfoDto.setVehicleVersion(vehicleVersion);
    guardianExceptionInfoDto.setReportUserName(reportUserName);
    guardianExceptionInfoDto.setReportTimestamp(reportTimestamp);
    guardianExceptionInfoDto.setOperateTimestamp(operateTimestamp);
    guardianExceptionInfoDto.setEndTimestamp(endTimestamp);
    guardianExceptionInfoDto.setExceptionInfoId(exceptionInfoId);
    guardianExceptionInfoDto.setBootUuid(bootUuid);
    guardianExceptionInfoDto.setBootId(bootId);
    return guardianExceptionInfoDto;
  }
}
