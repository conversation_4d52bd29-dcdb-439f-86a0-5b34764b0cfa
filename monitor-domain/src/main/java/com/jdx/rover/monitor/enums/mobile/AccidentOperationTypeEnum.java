package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentOperationTypeEnum {

    INITIATIVE_CRASH("INITIATIVE_CRASH","主动碰撞"),
    PASSIVE_CRASH("PASSIVE_CRASH","被动碰撞"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentOperationTypeEnum accidentOperationTypeEnum : AccidentOperationTypeEnum.values()) {
            if (accidentOperationTypeEnum.getValue().equals(value)) {
                return accidentOperationTypeEnum.getName();
            }
        }
        return null;
    }
}
