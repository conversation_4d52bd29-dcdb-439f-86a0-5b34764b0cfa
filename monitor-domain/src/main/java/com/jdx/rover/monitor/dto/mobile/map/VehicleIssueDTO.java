package com.jdx.rover.monitor.dto.mobile.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/17 14:12
 * @description 车辆运维工单
 */
@Data
public class VehicleIssueDTO {

    /**
     * 工单编号
     */
    private String issueNumber;

    /**
     * 工单状态
     * @see com.jdx.rover.ticket.api.enums.IssueStatusEnum
     */
    private String issueStatus;

    /**
     * 事件列表
     */
    private List<String> alarmEventList;

    /**
     * 工单创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 工单受理人
     */
    private String acceptUsername;

    /**
     * 工单受理座席
     */
    private String acceptCockpitNumber;
}
