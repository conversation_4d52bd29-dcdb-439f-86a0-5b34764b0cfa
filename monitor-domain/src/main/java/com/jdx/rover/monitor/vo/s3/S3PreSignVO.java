package com.jdx.rover.monitor.vo.s3;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 预签名请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/26
 */
@Data
public class S3PreSignVO {
    /**
     * 车辆名称
     */
    private String vehicleName;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 文件名称
     */
    @NotBlank(message = "上传文件名不能为空!")
    private String fileName;
}
