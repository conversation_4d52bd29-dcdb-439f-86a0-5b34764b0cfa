package com.jdx.rover.monitor.dto.mobile.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 小程序异常车辆调度停靠点信息
 */
@Data
public class ErrorVehicleScheduleStopDTO {

    /**
     * 停靠点ID
     */
    private Integer id;

    /**
     * 停靠点目标ID
     */
    private Integer goalId;

    /**
     * 停靠点名称
     */
    private String name;

    /**
     * 停靠点类型
     */
    private String type;

    /**
     * 停靠点动作类型
     */
    private String stopAction;

    /**
     * 停靠点状态
     */
    private String travelStatus;

    /**
     * 出发时间(用于计算已经行驶多长时间时间)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 到达时间(用于计算倒计时)
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arrivedTime;

    /**
     * 预计离开时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date estDepartTime;

    /**
     * 等待时间
     */
    private Integer waitingTime;

    /**
     * 当前停靠点规划总里程
     */
    private Double globalMileage;
}
