/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 采图车辆分页查询返回对象
 */
@Data
public class MapVehiclePageDTO {

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 红绿灯颜色
     */
    private String trafficLightColor;

    /**
     * 接管人
     */
    private String takeOverUserName;

    /**
     * 接管来源
     */
    private String takeOverSource;

    /**
     * 接管状态
     */
    private String takeOverStatus;

    /**
     * 速度
     */
    private Float speed;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 规划总里程
     */
    private Double globalMileage;

    /**
     * 已走里程
     */
    private Double arrivedMileage;

    /**
     * 任务绑定状态
     */
    private Boolean bindingState;

    /**
     * 电量
     */
    private Double power;

    /**
     * 系统状态
     */
    private String systemState;

    /**
     * 告警事件列表
     */
    private List<AlarmEvent> alarmEventList;

    /**
     * 告警事件
     */
    @Data
    public static class AlarmEvent implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 告警事件
         */
        private String alarmEvent;

        /**
         * 上报时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
        private Date reportTime;
    }
}
