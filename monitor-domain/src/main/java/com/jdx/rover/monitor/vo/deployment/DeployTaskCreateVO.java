/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.deployment;

import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskCreateVO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:12
 * @description 创建勘查任务线路
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeployTaskCreateVO extends TaskCreateVO {

    /**
     * 线路方案
     * @see com.jdx.rover.monitor.enums.mapcollection.RoutePlanTypeEnum
     */
    @NotBlank(message = "线路方案不能为空")
    private String routePlanType;

    /**
     * 精度方案
     * @see com.jdx.rover.monitor.enums.mapcollection.PreciseTypeEnum
     */
    @NotBlank(message = "精度方案不能为空")
    private String preciseType;

    /**
     * 勘查线路总里程
     */
    @NotNull(message = "线路总里程不能为空")
    private Double totalMileage;

    /**
     * 线路颜色
     * @see com.jdx.rover.monitor.enums.mapcollection.RouteColorEnum
     */
    private String taskRouteColor;

    /**
     * 点位列表
     */
    @NotEmpty(message = "点位列表不能为空")
    private List<PositionVO> taskRouteList;

    /**
     * 路线名称列表
     */
    private List<String> roadNameList;

    /**
     * 线路类型
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskRouteTypeEnum
     */
    private String taskRouteType;
}
