/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import com.jd.fastjson.annotation.JSONField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:20
 * @description 获取勘查任务
 */
@Data
public class TaskSearchDTO implements Serializable {

    /**
     * 勘查任务列表
     */
    private List<TaskDTO> taskList;

    @Data
    public static class TaskDTO implements Serializable {

        /**
         * 勘查任务ID
         */
        private Integer taskId;

        /**
         * 线路名称
         */
        private String taskName;

        /**
         * 任务创建人
         */
        private String taskCreator;

        /**
         * 勘查任务创建时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        /**
         * 勘查任务状态名称
         */
        private String taskStatus;

        /**
         * 所属城市ID
         */
        private Integer cityId;

        /**
         * 所属站点ID
         */
        private Integer stationId;

        /**
         * 车辆名称
         */
        private String vehicleName;
    }
}
