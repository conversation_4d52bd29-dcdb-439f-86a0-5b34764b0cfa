/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.issue;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * The enumerate issue state.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum IssueStateEnum {
  /**
   * <p>
   * The enumerate issue state.
   * </p>
   */
  WAITING("WAITING", "待受理"), PROCESS("PROCESS", "处理中"), FINISH("FINISH", "待提报"), UNREPORTED("UNREPORTED", "未提报"), REPORTED("REPORTED", "已提报");

  /**
   * <p>
   * The state of issue.
   * </p>
   */
  private final String issueState;

  /**
   * <p>
   * The state name of issue.
   * </p>
   */
  private final String issueStateName;

  /**
   * <p>
   * 依据issue state获取enum
   * </p>
   */
  public static IssueStateEnum of(final String issueState) {
    for (IssueStateEnum itemEnum : IssueStateEnum.values()) {
      if (itemEnum.issueState.equals(issueState)) {
        return itemEnum;
      }
    }
    return null;
  }
}
