package com.jdx.rover.monitor.vo.data;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @description: GetCockpitTeamDataListVO
 * @author: wanggu<PERSON>i
 * @create: 2024-06-20 10:48
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GetCockpitTeamDataListVO extends PageVO {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 坐席团队
     */
    private List<String> cockpitTeamNumberList;
}