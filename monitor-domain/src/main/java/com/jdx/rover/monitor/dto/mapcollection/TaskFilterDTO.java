/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:15
 * @description 获取站点及任务创建人列表
 */
@Data
public class TaskFilterDTO implements Serializable {

    /**
     * 城市列表
     */
    private List<CityFilterDTO> cityList;

    /**
     * 创建人用户名列表
     */
    private List<String> creatorList;

    @Data
    public static class CityFilterDTO implements Serializable {

        /**
         * 城市ID
         */
        private Integer cityId;

        /**
         * 站点ID列表
         */
        private List<Integer> stationList;
    }
}
