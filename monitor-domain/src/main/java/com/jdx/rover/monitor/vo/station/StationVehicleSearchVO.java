/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.vo.station;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 站点或车辆查询接口
 *
 * <AUTHOR>
 */
@Data
public class StationVehicleSearchVO {

  /**
   * 车辆业务类型(配送/售卖)
   *
   * @see com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum
   */
  @NotBlank(message = "车辆业务类型不能为空")
  private String vehicleBusinessType;

  /**
   * 查询名称
   */
  @NotBlank(message = "搜索内容不能为空")
  private String name;
}
