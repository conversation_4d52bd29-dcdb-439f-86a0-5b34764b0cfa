/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a stop map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleMapInfoDTO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the lat of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the scheduleState of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the task type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * Represents the system state of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String systemState;


}
