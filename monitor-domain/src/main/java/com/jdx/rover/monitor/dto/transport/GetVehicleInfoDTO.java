package com.jdx.rover.monitor.dto.transport;

import lombok.Data;

/**
 * @description: 车辆遥控-获取车辆选择列表-响应对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2025-02-06 17:40
 **/
@Data
public class GetVehicleInfoDTO {

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 车辆系统状态
     */
    private String systemStatus;

    /**
     * 车辆模式状态
     */
    private String vehicleStatus;

    /**
     * 接管人名称
     */
    private String takeoverUserName;

    /**
     * 接管来源
     */
    private String takeoverSource;

    /**
     * 接管状态（临时停车还是接管）
     */
    private String takeoverStatus;
}