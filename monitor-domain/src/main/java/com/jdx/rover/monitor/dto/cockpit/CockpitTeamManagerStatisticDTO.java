/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.cockpit;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 团队管理员统计信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/30
 */
@Data
public class CockpitTeamManagerStatisticDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 驾驶团队编号
     */
    private String cockpitTeamNumber;

    /**
     * 驾驶团队名称
     */
    private String cockpitTeamName;

    /**
     * 处理工单数
     */
    private Integer completeIssueCount;

    /**
     * 待处理工单数
     */
    private Integer waitAcceptIssueCount;

    /**
     * 座舱信息列表
     */
    private CockpitTeamManagerInfo managerInfo;

    @Data
    public static class CockpitTeamManagerInfo {

        /**
         * 处理工单数
         */
        private Integer completeIssueCount;

        /**
         * 工作时长
         */
        private Integer workTimeTotal;

    }
}