/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a assist jira debug time entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AssistJiraDebugTime extends BaseDomain {

  /**
   * <p>
   * Represents the assist jira record of assist jira debug time. The default value is null It's
   * changeable.
   * </p>
   */
  private Integer jiraRecordId;

  /**
   * <p>
   * Represents the debug time of assist jira debug time. The default value is null It's changeable.
   * </p>
   */
  private Date debugTime;
}
