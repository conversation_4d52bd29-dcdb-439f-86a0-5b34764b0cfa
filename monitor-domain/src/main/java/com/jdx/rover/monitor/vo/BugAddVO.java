package com.jdx.rover.monitor.vo;

import com.jdx.rover.infrastructure.api.domain.enums.xingyun.ModuleEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 创建缺陷对象
 */
@Data
public class BugAddVO {

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String topic;

    /**
     * 详细描述
     */
    @NotBlank(message = "详细描述不能为空")
    private String description;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 严重程度
     */
    private String severity;

    /**
     * 问题模块
     */
    private String module;

    /**
     * Rover版本号
     */
    private String version;

    /**
     * 事故等级
     */
    private String level;

    /**
     * 发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<Date> debugTime;

    /**
     * 报警事件
     */
    private String selectedAlarm;

    /**
     * 工单号&事故号
     */
    private String issueNo;

    /**
     * 来源
     */
    private String jiraSource;

    /**
     * 附件
     */
    private List<String> attachmentList;
    /**
     * 问题跟进人ERP
     */
    private String follower;

    /**
     * 事故等级
     */
    private String accidentLevel;

    /**
     * 事故类型
     */
    private String accidentType;
}
