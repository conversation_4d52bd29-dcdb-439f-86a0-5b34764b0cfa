/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a view object for semi jira issue.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class SemiJiraAddVO {

   /**
   * <p>
   * Represents the id of assist jira debug time. The default value is null It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Integer id;

  /**
   * <p>
   * Represents the assist jira record of assist jira debug time. The default value is null It's
   * changeable.
   * </p>
   */
  private Integer exceptionId;
  
  /**
   * <p>
   * Represents the reporter of assist jira. The default value is null It's
   * changeable.
   * </p>
   */
  private String reportUserName;

}
