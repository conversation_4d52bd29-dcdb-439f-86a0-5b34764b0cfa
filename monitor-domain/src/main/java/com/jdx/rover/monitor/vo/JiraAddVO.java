/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a view object for jira issue.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraAddVO {

  /**
   * <p>
   * Represents the id of issue type. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long issueTypeId;

  /**
   * <p>
   * Represents the topic. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String topic;

  /**
   * <p>
   * Represents the description. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String description;

  /**
   * <p>
   * Represents the id of priority. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long priorityId;

  /**
   * <p>
   * Represents the id of defect type. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long defectTypeId;

  /**
   * <p>
   * Represents the value of defect type. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String defectTypeValue;

  /**
   * <p>
   * Represents the id of severity. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long severityId;

  /**
   * <p>
   * Represents the value of severity. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String severityValue;

  /**
   * <p>
   * Represents the id of discovery phase. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long discoveryPhaseId;

  /**
   * <p>
   * Represents the value of discovery phase. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String discoveryPhaseValue;

  /**
   * <p>
   * Represents the id of discover situation. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long discoverSituationId;

  /**
   * <p>
   * Represents the value of discover situation. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String discoverSituationValue;

  /**
   * <p>
   * Represents the id of component. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long componentId;

  /**
   * <p>
   * Represents the name of reporter. The default value is null. It's changeable.
   * </p>
   */
  private String reporterName;

  /**
   * <p>
   * Represents the date list. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private List<Date> debugTime;

  /**
   * <p>
   * Represents the id of version. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(0)
  private Long versionId;

  /**
   * <p>
   * Represents the value of version. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String versionValue;

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * The issue no. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * The jira source (issue、accident).
   * </p>
   */
  private String jiraSource;

  /**
   * <p>
   * The issue alarm. The default value is null. It's changeable.
   * </p>
   */
  private String selectedAlarm;

  /**
   * <p>
   * Represents the attachment name list. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<String> attachmentName;

}
