/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * This is a view object for monitor user info identity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserDataInfoRequestVO {

  /**
   * <p>
   * Reprensents the request user. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String user;

  /**
   * <p>
   * Reprensents the type of the user. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String userType;

}
