package com.jdx.rover.monitor.dto.mobile.map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.monitor.dto.vehicle.AlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeDrivableInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/17 17:22
 * @description 地图模式车辆实时信息
 */
@Data
public class MapVehicleRealtimeDTO {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 告警事件列表
     */
    private List<AlarmEventDTO> alarmEventList;

    /**
     * 系统状态
     * @see com.jdx.rover.monitor.enums.mobile.SystemStatusEnum
     */
    private String systemStatus;

    /**
     * 车辆系统状态
     * @see com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum
     */
    private String systemState;

    /**
     * 驾驶模式
     * @see com.jdx.rover.server.api.domain.enums.guardian.DriveModeEnum
     */
    private String driveMode;

    /**
     * 电量
     */
    private Double power;

    /**
     * GPS信号
     */
    private String gpsSignal;

    /**
     * 定位置信度
     */
    private String sceneSignal;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 四路使能
     */
    private VehicleRealtimeDrivableInfoDTO drivableDirection;

    /**
     * 调度编号
     */
    private String scheduleName;

    /**
     * 调度状态
     * @see com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState
     */
    private String scheduleStatus;

    /**
     * 已行驶里程数
     */
    private Double finishMileage;

    /**
     * 调度单里程数
     */
    private Double totalMileage;

    /**
     * 调度停靠点列表
     */
    private List<ScheduleStop> scheduleStopList;

    /**
     * 接管/临时停车信息
     */
    private TakeOverInfo takeOverInfo;

    /**
     * 停靠点信息
     */
    @Data
    public static class ScheduleStop {

        /**
         * 停靠点ID
         */
        private Integer id;

        /**
         * 停靠点名称
         */
        private String name;

        /**
         * 停靠点动作类型
         * @see com.jdx.rover.schedule.api.domain.enums.StopAction
         */
        private String stopAction;

        /**
         * 停留状态(INIT,START,ARRIVED,DEPART,SKIP,STAY)
         * @see com.jdx.rover.schedule.api.domain.enums.StopTravelStatus
         */
        private String travelStatus;

        /**
         * 到达时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date arrivedTime;

        /**
         * 开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date startTime;

        /**
         * 离开时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date departTime;

        /**
         * 预计离开时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date estDepartTime;

        /**
         * 等待时间（分钟）
         */
        private Integer waitingTime;
    }

    /**
     * 接管/临时停车信息
     */
    @Data
    public static class TakeOverInfo {

        /**
         * 接管人名称
         */
        private String takeOverUserName;

        /**
         * 接管来源
         * @see com.jdx.rover.monitor.enums.RemoteCommandSourceEnum
         */
        private String takeOverSource;

        /**
         * 接管状态（临时停车还是接管）
         * @see com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum
         */
        private String takeOverStatus;
    }
}
