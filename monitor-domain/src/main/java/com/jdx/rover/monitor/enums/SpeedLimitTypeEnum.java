/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/8/11 23:08
 * @description 临时限速类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SpeedLimitTypeEnum {
    LIGHT_RAIN("LIGHT_RAIN", "小雨", 10.0),
    MODERATE_RAIN("MODERATE_RAIN", "中雨", 15.0),
    HEAVY_RAIN_OR_SNOW("HEAVY_RAIN_OR_SNOW", "大雨/雪", 20.0),
    OTHER("OTHER", "自定义", 0.0),
    UNKNOWN("UNKNOWN", "未知", 0.0)
    ;
    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 限速值
     */
    private final Double speed;

    /**
     * 根据speedLimitType获取枚举
     *
     * @param speedLimitType speedLimitType
     * @return MarkTypeEnum
     */
    public static SpeedLimitTypeEnum getByName(String speedLimitType) {
        for (SpeedLimitTypeEnum em : SpeedLimitTypeEnum.values()) {
            if (em.getCode().equals(speedLimitType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
