package com.jdx.rover.monitor.vo.cockpit;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @description: 切换模式VO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-06-11 19:56
 **/
@Data
public class SwitchModeVO {

    /**
     * 座席模式
     */
    @NotBlank(message = "座席模式不能为空")
    private String cockpitMode;

    /**
     * 座席编号
     */
    @NotBlank(message = "座席编号不能为空")
    private String cockpitNumber;
}