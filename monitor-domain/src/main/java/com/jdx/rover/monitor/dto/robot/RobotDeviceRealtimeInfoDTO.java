/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

/**
 * <p>
 * 机器人设备实时心跳信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotDeviceRealtimeInfoDTO extends RobotDeviceBasicDTO{

    /**
     * 定位状态
     */
    private Integer locateState;

    /**
     * 急停状态
     */
    private Integer emergencyState;

    /**
     * 电量
     */
    private Integer battery;

    /**
     * 是否充电
     */
    private Integer charging;

    /**
     * 自检状态
     */
    private Integer checkState;

    /**
     * 当前位置X坐标
     */
    private Double x;

    /**
     * 当前位置Y坐标
     */
    private Double y;

    /**
     * 当前位置yaw
     */
    private Double yaw;

    /**
     * 楼宇编号
     */
    private String buildId;

    /**
     * 区域编号
     */
    private String regionId;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 地图编号
     */
    private String mapId;

    /**
     * 地图版本
     */
    private String mapVersion;

    /**
     * 信号强度
     */
    private String esimSignal;

    /**
     * 电机状态
     */
    private Integer motorEnabled;

    /**
     * 错误代码：1-中断 2-异常 3-告警 4-正常
     */
    private String errorCode;

    /**
     * 当前任务：0-无任务；1-导航；2-充电;3-泊车
     */
    private String taskType;

    /**
     * 当前时间戳
     */
    private Long timestamp;

    /**
     * 机器人版本号
     */
    private String version;

    /**
     * 定位置信度
     */
    private Double locateConfidence;

    /**
     * 线速度
     */
    private Double linear;

    /**
     * 角速度
     */
    private Double angular;

    /**
     * 接管状态 false 未接管 true 已接管
     */
    private Boolean takeOverState;

}
