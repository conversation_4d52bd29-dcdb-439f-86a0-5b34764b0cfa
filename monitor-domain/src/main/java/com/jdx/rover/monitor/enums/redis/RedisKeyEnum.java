package com.jdx.rover.monitor.enums.redis;

import com.jdx.rover.monitor.constant.LocalCacheConstant;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum RedisKeyEnum {
  BASIC_VEHICLE_PREFIX("basic:vehicle:", "prefix", "车辆基本信息"),
  REALTIME_VEHICLE_PREFIX("realtime:vehicle:", "prefix", "车辆实时信息"),
  SCHEDULE_VEHICLE_PREFIX("schedule:vehicle:", "prefix", "车辆调度信息"),
  SCHEDULE_UPDATE_LOCK_PREFIX("schedule:update:lock:", "prefix", "车辆调度更新锁"),
  SCHEDULE_VEHICLE_STOP_PREFIX("schedule:vehicle:stop:", "prefix", "车辆调度停靠点信息"),
  SCHEDULE_PNC_ROUTE_PREFIX("schedule:pnc:routes:", "prefix", "调度规划路线信息"),
  SCHEDULE_REAL_ROUTE_PREFIX("schedule:real:route:", "prefix", "调度实际路线信息"),
  ALARM_EVENT_VEHICLE_PREFIX("alarmEvent:vehicle:", "prefix", "车辆告警信息"),
  ALARM_VEHICLE_PREFIX("alarm:vehicle:", "prefix", "车辆告警信息"),
  ALL_VEHICLE_NAME_SORT_LIST("all:vehicleNameSortList", "all", "所有车辆名称排序LIST,用于生成车辆名称分数"),
  ALL_STATION_SORT_LIST("all:stationSortList", "all", "所有站点排序LIST,用于生成车辆名称分数"),
  USER_VEHICLE_NAME_SET_PREFIX("user:vehicleNameSet:", "prefix", "用户车辆名称SET"),
  COCKPIT_VEHICLE_NAME_SET_PREFIX("cockpit:vehicleNameSet:", "prefix", "驾舱车辆名称SET"),
  USER_ATTENTION_VEHICLE_NAME_SET_PREFIX("user:attention:vehicleNameSet:", "prefix", "用户关注车辆名称SET"),
  USER_ATTENTION_STATION_SET_PREFIX("user:attention:stationSet:", "prefix", "用户关注站点SET"),
  USER_ATTENTION_EVENT_PREFIX("user:attention:event:", "prefix", "用户关注事件"),
  USER_STOP_LIST_PREFIX("user:stopList:", "prefix", "用户停靠点SET"),
  VEHICLE_NAME_SET_SELECT_TYPE_PREFIX("vehicleNameSet:selectType:", "prefix", "车辆排序(业务类型:车辆用途)"),
  SORTED_SET_VEHICLE_NAME(LocalCacheConstant.SORTED_SET_VEHICLE_NAME, "all", "车辆名称排序"),
  SORTED_SET_BUSINESS_TYPE(LocalCacheConstant.SORTED_SET_BUSINESS_TYPE, "all", "业务类型排序"),
  SORTED_SET_POWER(LocalCacheConstant.SORTED_SET_POWER, "all", "电量排序"),
  SORTED_SET_STATION(LocalCacheConstant.SORTED_SET_STATION, "all", "站点排序"),
  SORTED_SET_PUSH_SINGLE_VEHICLE("sortedSet:pushSingleVehicle", "all", "推送单车页"),
  TRAFFIC_LIGHT_SET_VEHICLE("trafficLightSet:vehicle:", "prefix", "车辆红绿灯列表"),
  VEHICLE_CLOUD_VERSION_PREFIX("vehicle:cloud:version:", "prefix", "车辆云端版本信息"),
  WEB_TERMINAL_SESSION_PREFIX("vehicle:webterminal:session:", "prefix", "远程工具会话状态"),
  REPORT_BOOT_PREFIX("monitor:report:boot:", "prefix", "report链路车辆启动信息"),
  VEHICLE_BOOT_MODULE("vehicle:boot:module", "all", "车辆启动模块配置"),
  ERROR_CODE_TRANSLATE_LIST("error:code:translate:list", "all", "错误码映射列表"),
  VEHICLE_STEER_ZERO_STATUS("vehicle:steer:zero:status:", "prefix", "车辆零偏状态"),
  USER_VEHICLE_SEARCH_RECORD_PREFIX("user:vehicleSearch:", "prefix", "用户车辆搜索记录"),
  BUG_USER_SET("bug:user:set", "all", "bug人员集合"),
  BUG_USER_MAPPING("bug:user:mapping:", "prefix", "bug人员映射"),
  COCKPIT_STATUS_UPDATE_LOCK_PREFIX("cockpit:status:update:lock:", "prefix", "座席状态更新锁"),
  COCKPIT_REST_AFTER_ISSUE_PREFIX("cockpit:rest:after:issue:", "prefix", "座席结单后休息标识"),
  COCKPIT_TIME_CHANGE_TASK_LOCK("cockpit:time:change:task:lock", "all", "定时修改未离线座席时长锁"),
  DRAWER_PUSH_SET("drawer:push:set", "all", "抽屉推送集合"),
  ACCIDENT_OPERATION_LOCK("mobile:accident:operation:", "prefix", "操作事故单"),
  GUARDIAN_CONNECT_VEHICLE_PREFIX("gurdian:connect:vehicle:", "prefix", "车辆失联信息"),
  JDME_ACCESS_TOKEN("jdme:accesstoken:", "prefix", "京ME访问密钥"),
  ACCIDENT_BUG_SYNC_LOCK("accident:bug:sync:lock", "all", "定时同步事故缺陷锁"),
  ACCIDENT_DAILY_REPORT_LOCK("accident:daily:report:lock", "all", "定时发送事故日报"),
  ACCIDENT_WEEKLY_REPORT_LOCK("accident:weekly:report:lock", "all", "定时发送事故周报"),
  VIDEO_CAPTURE_STATUS("accident:video:", "prefix", "事故视频快照截取状态"),
  ACCIDENT_BOARD_FOLLOW_UP("accident:board:follow:up", "all", "事故看板事故跟进"),
  ACCIDENT_BOARD_TODAY_MILEAGE("accident:board:today:mileage", "all", "事故看板今天里程数"),
  ALARM_ROBOT_PREFIX("alarm:robot:map:", "prefix", "机器人告警信息"),
  ROBOT_STOP_PREFIX("robot:stop", "prefix", "机器人停车信息"),
  ROBOT_GROUP_INFO("robot:group:info:", "prefix", "机器人分组信息"),
  ROBOT_ISSUE_PREFIX("robot:issue:", "prefix", "机器人工单信息"),
  ROBOT_SCHEDULE_PREFIX("robot:schedule:", "prefix", "机器人调度信息"),
  ROBOT_CONGESTION_PREFIX("robot:congestion:", "prefix", "机器人调度信息"),
  ROBOT_TASK_ROUTE_PREFIX("robot:task:route:", "prefix", "机器人规划路径信息"),
  ROBOT_TASK_GOAL("robot:task:goal", "all", "机器人停靠信息"),
  VEHICLE_STORAGE_SPACE_INFO("vehicle:storage:space:info", "all", "车辆储存空间"),
  COLLECTION_REAL_ROUTE ("collection:real:route:%s", "prefix", "采图车辆行驶点位"),
  COLLECTION_REAL_DISTANCE ("collection:real:distance:", "prefix", "采图车辆行驶距离"),
  COLLECTION_VEHICLE_STATUS("collection:vehicle:status:%s", "prefix", "采图车辆切换状态"),
  ASSOCIATE_TASK_LOCK("associate:task:lock:", "prefix", "关联任务锁"),
  DISABLE_DEVICE_NAME_SET_PREFIX("disable:robot:deviceNameSet:", "prefix", "停用设备名称SET"),
  DEVICE_NAME_SET_WORK_MODE_PREFIX("deviceNameSet:workMode:", "prefix", "设备工作模式(产品类型:工作模式)"),

  ;

  /**
   * 值
   */
  private final String value;
  /**
   * prefix前缀,suffix后缀,all全部
   */
  private final String type;
  /**
   * 标题描述
   */
  private final String title;
}
