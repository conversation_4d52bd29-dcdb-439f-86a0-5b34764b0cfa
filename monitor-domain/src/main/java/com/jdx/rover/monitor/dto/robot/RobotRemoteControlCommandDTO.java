/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

/**
 * 机器人远遥控制指令
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/10
 */
@Data
public class RobotRemoteControlCommandDTO extends RobotChassisCommandDTO {

  /**
   * 接管指令
   */
  private RobotVehicleControlCommand vehicleControl;


  @Data
  public static class RobotVehicleControlCommand {
    /**
     * 目标速度
     */
    private double targetVelocity;

    /**
     * 目标角度
     */
    private double targetAngle;
  }
}
