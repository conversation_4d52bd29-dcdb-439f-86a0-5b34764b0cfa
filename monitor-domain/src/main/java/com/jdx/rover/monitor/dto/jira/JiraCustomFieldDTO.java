/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.jira;

import lombok.Data;

/**
 * <p>
 * This is a jira custom field data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraCustomFieldDTO {

  /**
   * <p>
   * Represents the id of jira custom field. The default value is null. It's
   * changeable.
   * </p>
   */
  private Long id;

  /**
   * <p>
   * Represents the id of jira custom field. The default value is null. It's
   * changeable.
   * </p>
   */
  private String name;

}
