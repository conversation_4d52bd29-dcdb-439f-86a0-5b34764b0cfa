/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * This is a view objects for impassable area.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorImpassableAreaUpdateVO {

  /**
   * <p>
   * Represents the id of impassable area. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  private Integer id;

  /**
   * <p>
   * Represents the userName of the operation. The default value is null. It's
   * changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * 生效类别（永久性、周期性）
   * </p>
   */
  @NotNull
  private Integer effectType;

  /**
   * <p>
   * 周期时长
   * </p>
   */
  private List<String> timeLimit;

  /**
   * <p>
   * 备注
   * </p>
   */
  private String remark;

  /**
   * <p>
   * 启用/停用
   * </p>
   */
  private Integer effectState;

}
