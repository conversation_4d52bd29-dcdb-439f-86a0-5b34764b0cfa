/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.dto.issue;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a monitor issue change data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueChangeDTO {

  /**
   * <p>
   * Represents the number of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the vehicle name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the state of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueState;

  /**
   * <p>
   * Represents the alarm of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the start time of the issue. The default value is null. It's changeable.
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * Represents the operate result name of the issue. The default value is null. It's changeable.
   * </p>
   */
  private String operateResultName;
}
