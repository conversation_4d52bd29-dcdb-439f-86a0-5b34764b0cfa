/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:49
 * @description 新增标记
 */
@Data
public class MarkCreateVO implements Serializable {

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private Double latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private Double longitude;

    /**
     * 地址名称
     */
    @NotBlank(message = "地址名称不能为空")
    private String addressName;

    /**
     * 标记类型
     */
    @NotBlank(message = "标记类型不能为空")
    private String markType;
}
