package com.jdx.rover.monitor.enums.user;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

import java.util.Map;

/**
 * 视频清晰状态
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
@Slf4j
public enum UserConfigVideoQualityEnum {
    /**
     * 视频清晰状态
     */
    SUPER_QUALITY(1, "超清"),
    HIGH_QUALITY(2, "高清"),
    MEDIUM_QUALITY(3, "流畅"),
    LOW_QUALITY(4, "极速"),
    ;

    private static final Map<Integer, UserConfigVideoQualityEnum> CODE_MAP = EnumUtils.getEnumMap(UserConfigVideoQualityEnum.class, UserConfigVideoQualityEnum::getCode);

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 通过code获取枚举
     */
    public static UserConfigVideoQualityEnum getByCode(final Integer code) {
        return CODE_MAP.get(code);
    }
}
