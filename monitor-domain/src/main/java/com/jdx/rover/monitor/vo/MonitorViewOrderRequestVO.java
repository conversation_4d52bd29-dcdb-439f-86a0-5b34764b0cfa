/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

/**
 * <p>
 * This is a view object for verify code request.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorViewOrderRequestVO {

    /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the phone.
   * </p>
   */
  private String phone;

  /**
   * <p>
   * Represents the user.
   * </p>
   */
  private String user;

  /**
   * <p>
   * Represents the order no.
   * </p>
   */
  private String orderNo;

  /**
   * <p>
   * Represents the stop name.
   * </p>
   */
  private String stopName;

  /**
   * <p>
   * Represents the verify code.
   * </p>
   */
  private String verifyCode;

}
