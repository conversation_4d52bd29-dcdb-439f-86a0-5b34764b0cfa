/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * This is a view object for user control command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserControlCommandVO {

  /**
   * <p>
   * Represents the data. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  private String data;

  /**
   * <p>
   * Represents operate type. It's changeable.
   * </p>
   */
  @NotNull
  private String eventType;

}
