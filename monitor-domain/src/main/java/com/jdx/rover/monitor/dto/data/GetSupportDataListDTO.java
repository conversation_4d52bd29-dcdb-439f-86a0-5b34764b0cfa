package com.jdx.rover.monitor.dto.data;

import lombok.Data;

/**
 * 技术支持列表返回对象
 */
@Data
public class GetSupportDataListDTO {

    /**
     * 技术支持名称
     */
    private String supportUsername;

    /**
     * 驾舱团队名称
     */
    private String cockpitTeamName;

    /**
     * 在线时间
     */
    private Integer onlineDuration;

    /**
     * 休息时间
     */
    private Integer restDuration;

    /**
     * 听单总用时
     */
    private Integer listeningModeCost;

    /**
     * 受理工单次数
     */
    private Integer acceptCount;

    /**
     * 完单次数
     */
    private Integer finishCount;

    /**
     * 转单次数
     */
    private Integer transferCount;

    /**
     * 承接转单待受理次数
     */
    private Integer acceptTransferCount;

    /**
     * 抛单次数
     */
    private Integer discardCount;

    /**
     * 巡查提报工单次数
     */
    private Integer patrolReportCount;
}
