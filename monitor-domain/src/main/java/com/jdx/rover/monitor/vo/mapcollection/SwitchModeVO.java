/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * @description: 开启/关闭 地图采集模式请求对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-12-18 17:48
 **/
@Data
public class SwitchModeVO {

    /**
     * 驾舱编号
     */
    @NotBlank(message = "cockpitNumber不能为空")
    private String cockpitNumber;

    /**
     * 车号
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 动作（0:关闭 1:开启）
     */
    @NotNull(message = "action不能为空")
    private Integer action;

    /**
     * 操作时间
     */
    @NotNull(message = "operateTime不能为空")
    private Date operateTime;
}