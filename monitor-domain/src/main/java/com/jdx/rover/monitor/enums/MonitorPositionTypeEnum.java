/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 位置坐标系
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MonitorPositionTypeEnum {
  /**
   * <p>
   * The enumerate type and  name.
   * </p>
   */
  GCJ02("GCJ02", "国测局坐标系"),
  WGS84("WGS84", "大地坐标系");

  /**
   * <p>
   * 值
   * </p>
   */
  private String value;

  /**
   * <p>
   * 名称
   * </p>
   */
  private String name;

}
