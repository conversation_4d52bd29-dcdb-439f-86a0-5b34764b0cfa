/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums;

import com.google.common.collect.Lists;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.LevelEnum;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.ModuleEnum;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.SeverityEnum;
import com.jdx.rover.infrastructure.api.domain.enums.xingyun.StatusEnum;
import com.jdx.rover.metadata.api.domain.enums.CockpitTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.StopTypeEnum;
import com.jdx.rover.metadata.api.domain.enums.SupplierEnum;
import com.jdx.rover.metadata.api.domain.enums.TagEnum;
import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import com.jdx.rover.monitor.api.domain.enums.AlarmSourceEnum;
import com.jdx.rover.monitor.api.domain.enums.CockpitModeEnum;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.enums.accident.AccidentSourceEnum;
import com.jdx.rover.monitor.enums.accident.AccidentStatusEnum;
import com.jdx.rover.monitor.enums.accident.AccidentTypeEnum;
import com.jdx.rover.monitor.enums.issue.IssueOperateResultEnum;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentOperationStatusEnum;
import com.jdx.rover.monitor.enums.mobile.AccidentResolutionStatusEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentTypeEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentLevelEnum;
import com.jdx.rover.monitor.enums.mobile.SafetyGroupAccidentTypeEnum;
import com.jdx.rover.monitor.enums.vehicle.online.VehicleOnlineEnum;
import com.jdx.rover.schedule.api.domain.enums.*;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import com.jdx.rover.ticket.api.enums.IssueStatusEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 字段映射枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum FieldDisplayEnum {
  DEFAULT("DEFAULT", "默认值", null, Lists.newArrayList("value", "title"), Lists.newArrayList("code", "name")),
  VEHICLE_STATE_ENUM("VEHICLE_STATE_ENUM", "车辆状态", VehicleStateEnum.class, Lists.newArrayList("vehicleState", "vehicleStateName"), DEFAULT.displayNameList),
  ALARM_EVENT_ENUM("ALARM_EVENT_ENUM", "报警事件", AlarmEventSolutionEnum.class, Lists.newArrayList("alarmEventType", "alarmEventTypeName"), DEFAULT.displayNameList),
  ALARM_SOLUTION_ENUM("ALARM_SOLUTION_ENUM", "报警解决方案", AlarmEventSolutionEnum.class, Lists.newArrayList("alarmEventType", "solutionTypeName"), DEFAULT.displayNameList),
  SYSTEM_STATE_ENUM("SYSTEM_STATE_ENUM", "系统状态", SystemStateEnum.class, Lists.newArrayList("systemState", "systemStateName"), DEFAULT.displayNameList),
  SCHEDULE_STATE_ENUM("SCHEDULE_STATE_ENUM", "调度状态", VehicleScheduleState.class, Lists.newArrayList("vehicleScheduleState", "vehicleScheduleStateName"), DEFAULT.displayNameList),
  SCHEDULE_TASK_ENUM("SCHEDULE_TASK_ENUM", "调度任务", TaskType.class, Lists.newArrayList("taskType"), DEFAULT.displayNameList),
  DELIVERY_STATUS_ENUM("DELIVERY_STATUS_ENUM", "配送状态", DeliveryStatus.class, Lists.newArrayList("deliveryStatus", "deliveryStatusName"), DEFAULT.displayNameList),
  ISSUE_STATE_ENUM("ISSUE_STATE_ENUM", "工单状态", IssueStateEnum.class, Lists.newArrayList("issueState", "issueStateName"), DEFAULT.displayNameList),
  VEHICLE_ONLINE_ENUM("VEHICLE_ONLINE_ENUM", "车辆在线状态", VehicleOnlineEnum.class, Lists.newArrayList("online", "onlineName"), DEFAULT.displayNameList),
  VEHICLE_BUSINESS_TYPE_ENUM("VEHICLE_BUSINESS_TYPE_ENUM", "车辆业务类型", VehicleBusinessTypeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ISSUE_OPERATE_RESULT_ENUM("ISSUE_OPERATE_RESULT_ENUM", "工单操作结果", IssueOperateResultEnum.class, Lists.newArrayList("value", "title"), DEFAULT.displayNameList),
  VEHICLE_REALTIME_STATE_ENUM("VEHICLE_REALTIME_STATE_ENUM", "车辆实时状态", VehicleRealtimeStateEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ORDER_SOURCE_TYPE_ENUM("ORDER_SOURCE_TYPE_ENUM", "订单来源", OrderSourceType.class, Lists.newArrayList("code", "name"), DEFAULT.displayNameList),
  STOP_TYPE_ENUM("STOP_TYPE_ENUM", "停靠点类型", StopTypeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  STOP_ACTION_ENUM("STOP_ACTION_ENUM", "停靠点动作类型", StopAction.class, Lists.newArrayList("stopAction", "stopActionName"), DEFAULT.displayNameList),
  DELIVERY_MODE_ENUM("DELIVERY_MODE_ENUM", "配送方式", DeliveryMode.class, Lists.newArrayList("deliveryMode", "deliveryModeName"), DEFAULT.displayNameList),
  VIDEO_PUSH_TYPE_ENUM("VIDEO_PUSH_TYPE_ENUM", "视频推送方式", VideoPushTypeEnum.class, Lists.newArrayList("type", "name"), DEFAULT.displayNameList),
  MINI_MONITOR_EVENT_ENUM("MINI_MONITOR_EVENT_ENUM","运营端影子事件" , MiniMonitorEventEnum.class, Lists.newArrayList("eventType", "eventNo", "eventName", "description"), DEFAULT.displayNameList),
  ACCIDENT_TYPE_ENUM("ACCIDENT_TYPE_ENUM", "事故类型", AccidentTypeEnum.class, DEFAULT.fieldNameList, DEFAULT.displayNameList),
  ACCIDENT_SOURCE_ENUM("ACCIDENT_SOURCE_ENUM", "事故创建来源", AccidentSourceEnum.class, DEFAULT.fieldNameList, DEFAULT.displayNameList),
  ACCIDENT_STATUS_ENUM("ACCIDENT_STATUS_ENUM", "事故排查状态", AccidentStatusEnum.class, DEFAULT.fieldNameList, DEFAULT.displayNameList),
  BOOT_STATUS_ENUM("BOOT_STATUS_ENUM", "启动状态", ModuleBootStatusEnum.class, Lists.newArrayList("bootStatus", "bootStatusName"), DEFAULT.displayNameList),
  TAG_ENUM("TAG_ENUM", "车辆标签", TagEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  MINI_MONITOR_REALTIME_STATE_ENUM("MINI_MONITOR_REALTIME_STATE_ENUM", "运营端车辆实时状态",MiniMonitorVehicleRealtimeStateEnum.class,Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ACCIDENT_LEVEL_ENUM("ACCIDENT_LEVEL_ENUM", "事故等级枚举", LevelEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  BUG_MODULE_ENUM("BUG_MODULE_ENUM", "问题模块枚举", ModuleEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  BUG_SEVERITY_ENUM("BUG_SEVERITY_ENUM", "严重程度枚举", SeverityEnum.class, Lists.newArrayList("value", "alias"), DEFAULT.displayNameList),
  ALARM_SOURCE_ENUM("ALARM_SOURCE_ENUM", "事件类型枚举", AlarmSourceEnum.class, Lists.newArrayList("source", "sourceName"), DEFAULT.displayNameList),
  COCKPIT_TYPE_ENUM("COCKPIT_TYPE_ENUM", "工单受理端枚举", CockpitTypeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ISSUE_STATUS_ENUM("ISSUE_STATUS_ENUM", "工单状态枚举", IssueStatusEnum.class, Lists.newArrayList("code", "name"), DEFAULT.displayNameList),
  MANUAL_ALARM_SOURCE_ENUM("MANUAL_ALARM_SOURCE_ENUM", "人工告警创建来源", ManualAlarmSourceEnum.class, Lists.newArrayList("source", "sourceName"), DEFAULT.displayNameList),
  ACCIDENT_OPERATION_STATUS_ENUM("ACCIDENT_OPERATION_STATUS_ENUM", "事故一线处理状态", AccidentOperationStatusEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  @Deprecated
  SAFETY_GROUP_ACCIDENT_LEVEL_ENUM("SAFETY_GROUP_ACCIDENT_LEVEL_ENUM", "定责事故等级", SafetyGroupAccidentLevelEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  @Deprecated
  SAFETY_GROUP_ACCIDENT_TYPE_ENUM("SAFETY_GROUP_ACCIDENT_TYPE_ENUM", "定责事故分类", SafetyGroupAccidentTypeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ACCIDENT_RESOLUTION_STATUS_ENUM("ACCIDENT_RESOLUTION_STATUS_ENUM", "事故解决情况", AccidentResolutionStatusEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  PRELIMINARY_ACCIDENT_LEVEL_ENUM("PRELIMINARY_ACCIDENT_LEVEL_ENUM", "初判事故等级",PreliminaryAccidentLevelEnum .class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  PRELIMINARY_ACCIDENT_TYPE_ENUM("PRELIMINARY_ACCIDENT_TYPE_ENUM", "初判事故分类",PreliminaryAccidentTypeEnum .class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  BUG_STATUS_ENUM("BUG_STATUS_ENUM", "缺陷状态枚举", StatusEnum.class, Lists.newArrayList("value", "alias"), DEFAULT.displayNameList),
  VIDEO_MODE_ENUM("VIDEO_MODE_ENUM", "视频模式枚举", VideoModeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  COCKPIT_MODE_ENUM("COCKPIT_MODE_ENUM", "座席模式枚举", CockpitModeEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  VEHICLE_SUPPLIER_ENUM("VEHICLE_SUPPLIER_ENUM", "车辆供应商", SupplierEnum.class, Lists.newArrayList("value", "name"), DEFAULT.displayNameList),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 名称
   */
  private final String title;
  /**
   * 类型
   */
  private final Class clazz;
  /**
   * 字段列表
   */
  private final List<String> fieldNameList;
  /**
   * 展示名称
   */
  private final List<String> displayNameList;
}