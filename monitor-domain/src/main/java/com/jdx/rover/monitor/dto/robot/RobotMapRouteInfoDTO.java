/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;
import org.locationtech.jts.geom.Geometry;

/**
 * <p>
 * 机器人地图规划路径信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/06/10
 */
@Data
public class RobotMapRouteInfoDTO {

    /**
     * 机器人停靠点ID
     */
    private String stopId;

    /**
     * 机器人停靠点名称
     */
    private String stopName;

    /**
     * 机器人停靠点位置
     */
    private Double x;

    /**
     * 机器人停靠点位置
     */
    private Double y;

    /**
     * 机器人规划路径
     */
    private String planRoute;

}
