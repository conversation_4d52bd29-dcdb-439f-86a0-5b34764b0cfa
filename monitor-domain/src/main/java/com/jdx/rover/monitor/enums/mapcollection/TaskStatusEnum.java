/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:36
 * @description 勘查任务状态枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum TaskStatusEnum {
    UNDER_EXPLORATION("UNDER_EXPLORATION", "勘查中"),
    PENDING_CLAIM("PENDING_CLAIM", "待认领"),
    COLLECTING("COLLECTING", "采集中"),
    PENDING_COPY("PENDING_COPY", "待拷贝"),
    COPYING("COPYING", "拷贝中"),
    COPY_COMPLETED("COPY_COMPLETED", "拷贝完成"),
    MAP_IN_PROGRESS("MAP_IN_PROGRESS", "地图制作中"),
    MAP_RELEASE("MAP_RELEASE", "地图上线"),
    TASK_CLOSED("TASK_CLOSED", "已完成"),
    TASK_DELETED("TASK_DELETED", "任务已删除"),
    UNKNOWN("UNKNOWN", "未知")
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 根据taskStatus获取枚举
     *
     * @param taskStatus taskStatus
     * @return TaskStatusEnum
     */
    public static TaskStatusEnum of(String taskStatus) {
        for (TaskStatusEnum em : TaskStatusEnum.values()) {
            if (em.getCode().equals(taskStatus)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
