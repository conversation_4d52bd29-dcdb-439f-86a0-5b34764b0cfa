/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * <p>
 * 定位初始化车辆位姿数据
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleLocationPoseVO {

  /**
   * <p>
   * x轴
   * </p>
   */
  @NotNull
  private Double x;

  /**
   * <p>
   * y轴
   * </p>
   */
  @NotNull
  private Double y;

  /**
   * <p>
   * z轴
   * </p>
   */
  @NotNull
  private Double z;

  /**
   * <p>
   * row角
   * </p>
   */
  @NotNull
  private Double roll;

  /**
   * <p>
   * yaw轴
   * </p>
   */
  @NotNull
  private Double yaw;

  /**
   * <p>
   * pitch角
   * </p>
   */
  @NotNull
  private Double pitch;

}
