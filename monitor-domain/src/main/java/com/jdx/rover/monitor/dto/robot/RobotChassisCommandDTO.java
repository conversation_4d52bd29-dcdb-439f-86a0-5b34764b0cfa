/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.robot;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 机器人远遥指令
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/10
 */
@Data
public class RobotChassisCommandDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 当前机器人指令顺序
   */
  private Integer sequenceNum;

  /**
   * 指令耗时统计
   */
  private List<ModuleDelayInfo> moduleDelayInfo;

  /**
   * 模块耗时
   */
  @Data
  public static class ModuleDelayInfo {

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 接收时间
     */
    private Long receiveTime;

    /**
     * 传送时间
     */
    private Long transitTime;

  }


}

