/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.Date;
import lombok.Data;

/**
 * 工单明细缓存.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmCacheAddVO {
  /**
   * <p>
   * 报警编号.
   * </p>
   */
  private Integer alarmId;

  /**
   * <p>
   * 报警事件类型.
   * </p>
   */
  private String alarmEventType;

  /**
   * <p>
   * 标题/错误码(运营报警为标题,其它为错误码).
   * </p>
   */
  private String title;

  /**
   * <p>
   * 描述/ErrorCode(运营报警为描述,其它为ErrorCode).
   * </p>
   */
  private String description;

  /**
   * <p>
   * 上报时间/首次报警时间.
   * </p>
   */
  private Date startTimestamp;
}
