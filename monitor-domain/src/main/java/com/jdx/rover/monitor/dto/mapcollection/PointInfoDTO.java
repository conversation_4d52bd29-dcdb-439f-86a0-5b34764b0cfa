/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import com.jdx.rover.metadata.domain.dto.stop.StopBasicDTO;
import com.jdx.rover.monitor.enums.mapcollection.PointTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:08
 * @description 通用点位信息
 */
@Data
public class PointInfoDTO {

    /**
     * 初始点位ID
     */
    private Integer pointId;

    /**
     * 初始点位编号
     */
    private String pointNumber;

    /**
     * 初始点位名称
     */
    private String pointName;

    /**
     * 初始点位类型
     * @see com.jdx.rover.monitor.enums.mapcollection.PointTypeEnum
     */
    private String pointType;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 是否启用
     */
    private Integer enabled;

    /**
     * 根据站点列表生成点位
     *
     * @param stationBasicDTOList stationBasicDTOList
     * @return List<PointInfoDTO>
     */
    public static List<PointInfoDTO> createByStationList(List<StationBasicDTO> stationBasicDTOList) {
        if (CollUtil.isEmpty(stationBasicDTOList)) {
            return Lists.newArrayList();
        }

        List<PointInfoDTO> result = Lists.newArrayListWithCapacity(stationBasicDTOList.size());
        stationBasicDTOList.forEach(stationBasicDTO -> {
            PointInfoDTO pointInfoDTO = new PointInfoDTO();
            pointInfoDTO.setPointId(stationBasicDTO.getStationId());
            pointInfoDTO.setPointNumber(stationBasicDTO.getStationNumber());
            pointInfoDTO.setPointName(stationBasicDTO.getStationName());
            pointInfoDTO.setPointType(PointTypeEnum.STATION.getCode());
            pointInfoDTO.setLatitude(stationBasicDTO.getLatitude());
            pointInfoDTO.setLongitude(stationBasicDTO.getLongitude());
            pointInfoDTO.setEnabled(stationBasicDTO.getEnable());
            result.add(pointInfoDTO);
        });
        return result;
    }

    /**
     * 根据停靠点列表生成点位
     *
     * @param stopBasicDTOList stopBasicDTOList
     * @return List<PointInfoDTO>
     */
    public static List<PointInfoDTO> createByStopList(List<StopBasicDTO> stopBasicDTOList) {
        if (CollUtil.isEmpty(stopBasicDTOList)) {
            return Lists.newArrayList();
        }

        List<PointInfoDTO> result = Lists.newArrayListWithCapacity(stopBasicDTOList.size());
        stopBasicDTOList.forEach(stopBasicDTO -> {
            PointInfoDTO pointInfoDTO = new PointInfoDTO();
            pointInfoDTO.setPointId(stopBasicDTO.getStopId());
            pointInfoDTO.setPointNumber(stopBasicDTO.getStopNumber());
            pointInfoDTO.setPointName(stopBasicDTO.getStopName());
            pointInfoDTO.setPointType(PointTypeEnum.STOP.getCode());
            pointInfoDTO.setLatitude(stopBasicDTO.getLatitude());
            pointInfoDTO.setLongitude(stopBasicDTO.getLongitude());
            pointInfoDTO.setEnabled(stopBasicDTO.getEnable());
            result.add(pointInfoDTO);
        });
        return result;
    }
}
