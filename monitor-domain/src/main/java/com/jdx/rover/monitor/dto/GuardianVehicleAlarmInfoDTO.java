/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a guardian alarm info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class GuardianVehicleAlarmInfoDTO {

  /**
   * <p>
   * Represents the id of guardian alarm info. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the vehicle entity of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * 事件来源名称
   */
  private String alarmSourceName;

  /**
   * <p>
   * Represents the alarmEvent of guardian alarm info. It's changeable.
   * </p>
   */
  private String alarmEvent;

  /**
   * <p>
   * 告警编号
   * </p>
   */
  private String alarmNumber;

  /**
   * <p>
   * 告警编号
   * </p>
   */
  private String alarmSource;

  /**
   * <p>
   * 所属工单
   * </p>
   */
  private String issueNumber;

  /**
   * <p>
   * Represents the description of guardian alarm info. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the component id of guardian alarm info. It's changeable.
   * </p>
   */
  private String componentId;

  /**
   * <p>
   * Represents the component name of guardian alarm info. It's changeable.
   * </p>
   */
  private String componentName;

  /**
   * <p>
   * Represents the occurrence timestamp of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date operateTimestamp;

  /**
   * <p>
   * Represents the end timestamp of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date endTimestamp;

  /**
   * <p>
   * Represents the city entity of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station entity of guardian alarm info. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the report timestamp of guardian alarm info. It's changeable.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date reportTimestamp;

  /**
   * <p>
   * Represents the report user name of guardian alarm info. It's changeable.
   * </p>
   */
  private String reportUserName;

  /**
   * <p>
   * Represents the version of vehicle. It's changeable.
   * </p>
   */
  private String vehicleVersion;

  /**
   * <p>
   * Represents the exception info id of guardian alarm info. It's changeable.
   * </p>
   */
  private Integer exceptionInfoId;

  /**
   * <p>
   * Represents the boot uuid id of guardian alarm info. It's changeable.
   * </p>
   */
  private String bootUuid;

  /**
   * <p>
   * Represents the boot id of guardian alarm info. It's changeable.
   * </p>
   */
  private Integer bootId;
}