/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * <p>
 * This is a view object for jira attachment.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class JiraAttachmentAddVO {
  /**
   * <p>
   * Represents the name of attachment. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String name;

  /**
   * <p>
   * Represents the data of attachment. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotBlank
  private String data;

}
