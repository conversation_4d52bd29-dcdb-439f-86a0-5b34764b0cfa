/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a station map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorStationMapInfoDTO {

  /**
   * <p>
   * Represents the id of station. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the name of station. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the lat of station. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of station. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

}
