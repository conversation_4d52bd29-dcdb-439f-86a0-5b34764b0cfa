/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.issue.IssueAlarmDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a model class of vehicle alarm record.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmRecord extends BaseDomain {

  /**
   * <p>
   * The vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * The error code. The default value is null. It's changeable.
   * </p>
   */
  private String alarmCode;

  /**
   * <p>
   * The alarm name. The default value is null. It's changeable.
   * </p>
   */
  private String alarmNo;

  /**
   * <p>
   * The alarm type. The default value is null. It's changeable.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * The source of the alarm. The default value is null. It's changeable.
   * </p>
   */
  private String source;

  /**
   * <p>
   * The timestamp of exception. The default value is null. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * <p>
   * The issue no. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the selected by issue. The default value is 0. It's changeable.
   * </p>
   */
  private boolean selected;

  /**
   * <p>
   * This method helps to convert model of VehicleAlarmRecord to VehicleAlarmRecordDto.
   * </p>
   * 
   * @return The data transfer object for vehicle alarm record.
   */
  public IssueAlarmDTO toIssAlarmDto() {
    IssueAlarmDTO alarmRecordDto = new IssueAlarmDTO();
    alarmRecordDto.setId(getId());
    alarmRecordDto.setType(alarmType);
    alarmRecordDto.setStartTimestamp(timestamp);
    alarmRecordDto.setDescription(alarmCode);
    alarmRecordDto.setTitle(alarmNo);
    alarmRecordDto.setIsSelected(selected);
    alarmRecordDto.setVehicleName(vehicleName);
    return alarmRecordDto;
  }
}