/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import java.util.List;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * This is a view object for mini monitor station request.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorIssueVO {
  /**
   * <p>
   * 车辆名称
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 工单号
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * 操作人姓名
   * </p>
   */
  @NotBlank
  private String operateUserName;

  /**
   * <p>
   * 操作结果
   * </p>
   */
  private List<Integer> alarmIdList;


  /**
   * <p>
   * 技术支持生成工单告警来源
   * </p>
   */
  private List<MonitorIssueAlarmVO> alarmList;

  /**
   * <p>
   * 操作结果
   * </p>
   */
  private String operateResult;

  /**
   * <p>
   * 操作记录
   * </p>
   */
  private List<MonitorIssueOperateHistoryVO> issueHistoryList;

}
