/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

/**
 * <p>
 * 监控地图车辆位置请求VO
 * </p>
 *
 * <AUTHOR>
 * @date 2025/02/20
 */

@Data
public class MonitorMapVehiclePositionRequestVO {

  /**
   * 元数据类型，用于区分不同类型的监控地图位置请求。
   */
  private String metaType;

  /**
   * 用于标识特定监控地图位置请求的唯一键。
   */
  private String key;

  /**
   * 表示位置的坐标系类型，默认为WGS84。
   */
  private String positionType = "WGS84";

}
