/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

/**
 * <p>
 * This is a monitor order info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorOrderInfoRequestVO {

  /**
   * 订单主键
   */
  private Integer id;

  /**
   * 订单号
   */
  private Integer orderId;

  /**
   * 收货人联系方式
   */
  private String contact;

  /**
   * <p>
   * 配送模式
   * </p>
   */
  private String deliveryModel;

}
