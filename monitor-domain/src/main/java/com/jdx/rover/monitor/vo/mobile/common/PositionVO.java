package com.jdx.rover.monitor.vo.mobile.common;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @description: PositionVO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-07-15 14:23
 **/
@Data
public class PositionVO {

    /**
     * 经度（cj02坐标系）
     */
    @NotNull(message = "经度不能为空")
    private Double longitude;

    /**
     * 纬度（cj02坐标系）
     */
    @NotNull(message = "纬度不能为空")
    private Double latitude;
}