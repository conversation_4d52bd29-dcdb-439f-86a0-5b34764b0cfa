/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * This is a user city basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserCityBasicInfoDTO {

    /**
   * <p>
   * Represents the vehicle's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the list of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<MonitorUserStationBasicInfoDTO> station = new ArrayList<>();

  /**
   * <p>
   * Represents the total count of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Integer total = 0;

  /**
   * <p>
   * Represents the abnormal count of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Integer abnormalNum;

}
