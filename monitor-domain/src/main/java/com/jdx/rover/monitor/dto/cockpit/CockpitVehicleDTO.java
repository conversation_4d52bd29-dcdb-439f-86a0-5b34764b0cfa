/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 座席下车辆列表
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@NoArgsConstructor
public class CockpitVehicleDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 车辆模式
     */
    private String vehicleState;

    /**
     * 调度状态
     */
    private String scheduleState;

    /**
     * 系统状态
     */
    private String systemState;

    /**
     * 速度
     */
    private Float speed;

    /**
     * 接管人名称
     */
    private String takeOverUserName;

    /**
     * 接管来源
     */
    private String takeOverSource;

    /**
     * 接管状态(临时停车还是接管)
     */
    private String takeOverStatus;

    /**
     * 规划总里程
     */
    private Double globalMileage;

    /**
     * 已走里程
     */
    private Double arrivedMileage;

    /**
     * 记录时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 场景
     */
    private List<String> sceneList;

    /**
     * 告警事件列表
     */
    private List<AlarmEvent> alarmEventList;

    /**
     * 告警事件
     */
    @Data
    public static class AlarmEvent implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 告警事件
         */
        private String alarmEvent;

        /**
         * 上报时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
        private Date reportTime;
    }
}
