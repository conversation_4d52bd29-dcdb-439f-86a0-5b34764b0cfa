/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a monitor order info data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorOrderInfoDTO {

  /**
   * 订单主键
   */
  private Integer id;

  /**
   * 订单号
   */
  private String orderId;

  /**
   * 总包裹数
   */
  private Integer packageTotal;

  /**
   * 订单状态
   */
  private String deliveryState;

  /**
   * 收货人联系方式
   */
  private String contact;

  /**
   * 订单收货人名
   */
  private String name;

  /**
   * 配送停靠点名称
   */
  private String stopName;

  /**
   * 配送停靠点
   */
  private Integer stopId;

  /**
   * 装载停靠点
   */
  private Integer loadStopId;

  /**
   * 配送模式
   */
  private String deliveryModel;

  /**
   * 订单来源
   */
  private String orderSourceCode;

  /**
   * 装货方式类型（始发装载、中途补货）
   */
  private String loadMethodType;

  /**
   * 装货方式（始发装载、中途补货）
   */
  private String loadMethodName;

  /**
   * 隔口号
   */
  private List<Integer> gridNoList;

  /**
   * 验证码
   */
  private String verifyCode;
}
