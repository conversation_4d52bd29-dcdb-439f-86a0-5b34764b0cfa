/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.tnta;

import lombok.Data;

/**
 * <p>
 * This is a impassable area update data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ImpassableAreaUpdateDTO {

  /**
   * <p>
   * 响应码
   * </p>
   */
  private String stateCode;

  /**
   * <p>
   * 响应时间
   * </p>
   */
  private String responseDate;

  /**
   * <p>
   * 异常信息
   * </p>
   */
  private String msg;

}
