/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.List;
import lombok.Data;

/**
 * <p>
 * This is a vehicle basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleBasicMapInfoDTO {

  /**
   * <p>
   * The latitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * The longitude for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * The heading for the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double heading;

  /**
   * <p>
   * Represents the finished routing point list. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointDTO> finishedRoutingPoint;

  /**
   * <p>
   * Represents the planning routing point dto. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointDTO> planningRoutingDto;

  /**
   * <p>
   * Represents the planning stop state dto. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorVehiceStopBasicMapInfoDTO> stop;
}
