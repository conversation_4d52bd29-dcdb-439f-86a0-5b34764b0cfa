package com.jdx.rover.monitor.vo.transport;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * @description: 指令操作-请求对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-18 13:25
 **/
@Data
public class CommandVO {

    /**
     * 车号
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 指令类型
     */
    @NotBlank(message = "commandType不能为空")
    private String commandType;

    /**
     * 操作时间
     */
    @NotNull(message = "timeStamp不能为空")
    private Date timeStamp;
}