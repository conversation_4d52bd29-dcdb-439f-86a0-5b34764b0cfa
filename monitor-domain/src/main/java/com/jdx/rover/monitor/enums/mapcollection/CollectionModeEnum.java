package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 车辆采图模式-枚举
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-12-19 11:03
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum CollectionModeEnum {

    COLLECTION("COLLECTION", "采图模式"),
    NO_COLLECTION("NO_COLLECTION", "非采图模式"),
    ;

    /**
     * 采图模式
     */
    private final String collectionMode;

    /**
     * 采图模式名称
     */
    private final String collectionModeName;

    /**
     * 获取枚举
     *
     * @param collectionMode collectionMode
     * @return CollectionModeEnum
     */
    public static CollectionModeEnum of(String collectionMode) {
        for (CollectionModeEnum collectionModeEnum : CollectionModeEnum.values()) {
            if (collectionModeEnum.getCollectionMode().equals(collectionMode)) {
                return collectionModeEnum;
            }
        }
        return null;
    }
}