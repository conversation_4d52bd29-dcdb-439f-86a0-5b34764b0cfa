/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.tnta;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a impassable area sort type enum.
 * </p>
 *
 * <p>
 * <strong>impassableArea sort type: </strong> enumeration of the class impassableArea sort type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@AllArgsConstructor
@ToString
public enum ImpassableAreaSortType {

  /**
   * <p>
   * The enumerate impassableArea sort type.
   * </p>
   */
  ASC(1), DESC(2);

  /**
   * <p>
   * The status corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private Integer sortType;
}
