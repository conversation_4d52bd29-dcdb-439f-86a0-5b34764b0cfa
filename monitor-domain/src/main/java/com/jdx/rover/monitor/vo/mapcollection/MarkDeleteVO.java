/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:55
 * @description 删除标记
 */
@Data
public class MarkDeleteVO implements Serializable {

    /**
     * 标记ID
     */
    @NotNull(message = "标记ID不能为空")
    private Integer markId;

    /**
     * 标记类型
     */
    @NotBlank(message = "标记类型不能为空")
    private String markType;
}
