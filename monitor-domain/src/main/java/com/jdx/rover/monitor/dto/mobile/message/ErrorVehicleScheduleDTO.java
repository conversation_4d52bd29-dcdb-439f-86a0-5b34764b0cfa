package com.jdx.rover.monitor.dto.mobile.message;

import lombok.Data;

import java.util.List;

/**
 * 小程序异常车辆调度信息
 */
@Data
public class ErrorVehicleScheduleDTO {

    /**
     * 总订单数
     */
    private Integer totalOrderNum;

    /**
     * 完成订单数
     */
    private Integer finishedOrderNum;

    /**
     * 当前规划总里程
     */
    private Double globalMileage;

    /**
     * 当前规划已走里程
     */
    private Double arrivedMileage;

    /**
     * 当前站点完成里程
     */
    private Double currentStopFinishedMileage;

    /**
     * 停靠点列表
     */
    private List<ErrorVehicleScheduleStopDTO> stopList;
}
