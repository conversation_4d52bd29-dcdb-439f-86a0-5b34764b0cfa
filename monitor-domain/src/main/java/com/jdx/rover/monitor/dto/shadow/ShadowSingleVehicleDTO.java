/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.shadow;

import com.jdx.rover.monitor.dto.vehicle.VehicleDrivableDirectionDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 影子系统单车页实时模型
 *
 * <AUTHOR>
 */
@Data
public class ShadowSingleVehicleDTO {

  /**
   * 车号
   */
  private String vehicleName;

  /**
   * 时间
   */
  private Date recordTime;

  /**
   * 配送状态
   */
  private String scheduleState;

  /**
   * 配送任务号
   */
  private String scheduleNo;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 剩余电量
   */
  private Double power;

  /**
   * 剩余电量
   */
  private Double speed;

  /**
   * 当前站点完成里程
   */
  private Double currentStopFinishedMileage;

  /**
   * 当前调度完成里程
   */
  private Double currentScheduleFinishedMileage;

  /**
   * 调度信息
   */
  private VehicleDrivableDirectionDTO vehicleDrivableDirection;

  /**
   * 告警事件
   */
  private List<String> alarmEventList;
}
