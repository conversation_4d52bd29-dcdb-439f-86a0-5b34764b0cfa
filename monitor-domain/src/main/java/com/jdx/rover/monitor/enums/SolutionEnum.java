/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 解决方案
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum SolutionEnum {
  UNKNOWN("UNKNOWN", ""),
  ON_SITE_REPAIR("ON_SITE_REPAIR", "现场处理"),
  BACK_TO_CHARGE("BACK_TO_CHARGE", "返回充电"),
  REMOTE_CONTROL("REMOTE_CONTROL", "远程遥控"),
  WAIT_FOR_RESCUE("WAIT_FOR_RESCUE", "等待处理"),
  ISSUE_HANDLE("ISSUE_HANDLE", "工单处理"),
  AS_ARRIVED("AS_ARRIVED", "视同到达"),
  PASS_INTERSECTION("PASS_INTERSECTION", "通过路口"),
  POWER_RESTART("POWER_RESTART", "断电重启"),
  EMERGENCY_TREATMENT("EMERGENCY_TREATMENT", "紧急处理"),

  RESTART_MCU("RESTART_MCU","重启MCU"),
  LOCALIZATION_RESET("LOCALIZATION_RESET","定位重置"),
  CALL_OPERATION("CALL_OPERATION","呼叫一线"),
  ;

  /**
   * 解决方案类型
   */
  private String solutionType;

  /**
   * 解决方案类型名称
   */
  private String solutionTypeName;
}
