/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.entity;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/1 11:47
 * @description
 */
@Data
public class MapTaskDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 采集任务ID
     */
    private Integer taskId;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 座席编号
     */
    private String cockpitNumber;
}
