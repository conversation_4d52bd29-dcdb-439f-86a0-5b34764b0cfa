/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 监控多车页
 *
 * <AUTHOR>
 */
@Data
public class MultiVehicleDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 车辆业务类型(配送/售卖)
   */
  private String vehicleBusinessType;

  /**
   * 车辆供应商(京东/新石器/白犀牛)
   */
  private String supplier;

  /**
   * 系统状态
   */
  private String systemState;

  /**
   * 车辆模式
   */
  private String vehicleState;

  /**
   * 剩余电量
   */
  private Double power;

  /**
   * 速度
   */
  private Double speed;

  /**
   * 调度状态
   */
  private String scheduleState;

  /**
   * 调度任务
   */
  private String taskType;

  /**
   * 调度信息
   */
  private MultiVehicleScheduleDTO schedule;

  /**
   * 告警事件
   */
  private List<AlarmEventDTO> alarmEventList;

  /**
   * 记录时间
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date recordTime;
}
