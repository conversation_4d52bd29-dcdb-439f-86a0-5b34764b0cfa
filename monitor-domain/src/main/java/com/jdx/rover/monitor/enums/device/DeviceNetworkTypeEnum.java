/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 设备网络类型
 * </p>
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DeviceNetworkTypeEnum {

    /**
     * 枚举
     */
    WIFI(1,"Wi-Fi在线"),
    MOBILE(2,"蜂窝在线"),
    ;
    /**
     * 值.
     */
    private final Integer value;

    /**
     * 标题.
     */
    private final String title;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (DeviceNetworkTypeEnum networkTypeEnum : DeviceNetworkTypeEnum.values()) {
            if (networkTypeEnum.getValue().equals(value)) {
                return networkTypeEnum.getTitle();
            }
        }
        return null;
    }
}
