/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.deployment;

import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/18 22:07
 * @description 勘查任务信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeployTaskInfoDTO extends TaskRouteDTO {

    /**
     * 勘查任务ID
     */
    private Integer taskId;

    /**
     * 勘查任务创建人
     */
    private String taskCreator;

    /**
     * 线路类型
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskRouteTypeEnum
     */
    private String taskRouteType;

    /**
     * 创建人电话，脱敏
     */
    private String phoneNumber;

    /**
     * 勘查任务状态
     * @see com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum
     */
    private String taskStatus;

    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 任务创建时间
     */
    private String createTime;

    /**
     * 采集车
     */
    private String vehicleName;

    /**
     * 线路方案
     * @see com.jdx.rover.monitor.enums.mapcollection.RoutePlanTypeEnum
     */
    private String routePlanType;

    /**
     * 精度方案
     * @see com.jdx.rover.monitor.enums.mapcollection.PreciseTypeEnum
     */
    private String preciseType;

    /**
     * 子任务列表
     */
    private List<SubTaskDTO> subTaskList;
}
