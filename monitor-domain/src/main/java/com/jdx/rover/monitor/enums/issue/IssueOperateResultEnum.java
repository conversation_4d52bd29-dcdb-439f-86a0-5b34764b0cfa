/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.issue;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * The enumerate issue operate result.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum IssueOperateResultEnum {
  /**
   * <p>
   * The enumerate issue operate result.
   * </p>
   */
  OPERATION_ALARM("OPERATION_ALARM", "运营报警", "运营", IssueStateEnum.WAITING.getIssueState()),
  EXCEPTION_REMIND("EXCEPTION_REMIND", "异常提醒", "运营", IssueStateEnum.WAITING.getIssueState()),
  VEHICLE_ALARM("VEHICLE_ALARM", "车辆报警", "技术支持", IssueStateEnum.WAITING.getIssueState()),
  SUPPORT_PROCESS("SUPPORT_PROCESS", "技术支持处理中", "技术支持", IssueStateEnum.PROCESS.getIssueState()),
  DEVELOPER_PROCESS("DEVELOPER_PROCESS", "研发处理中", "技术支持", IssueStateEnum.PROCESS.getIssueState()),
  OPERATOR_PROCESS("OPERATOR_PROCESS", "运营处理中", "技术支持", IssueStateEnum.PROCESS.getIssueState()),
  OPERATION_RESUME("OPERATION_RESUME", "运营恢复", "技术支持", IssueStateEnum.FINISH.getIssueState()),
  OPERATION_ABORT("OPERATION_ABORT", "运营终止", "技术支持", IssueStateEnum.FINISH.getIssueState()),
  ;

  /**
   * <p>
   * The operate result of issue.
   * </p>
   */
  private final String value;

  /**
   * <p>
   * The operate result name of issue.
   * </p>
   */
  private final String title;

  /**
   * <p>
   * The operate role of issue.
   * </p>
   */
  private final String role;

  /**
   * <p>
   * The state of issue.
   * </p>
   */
  private final String issueState;

  /**
   * <p>
   * 依据value获取enum
   * </p>
   */
  public static IssueOperateResultEnum of(final String value) {
    for (IssueOperateResultEnum itemEnum : IssueOperateResultEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
