package com.jdx.rover.monitor.dto.mobile.common;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/26 16:28
 * @description 车辆基本信息（经纬度、系统状态、接管信息）
 */
@Data
public class CommonVehicleInfoDTO {

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 维度
     */
    private Double latitude;

    /**
     * 是否权限下车辆
     */
    private boolean coverPermission;

    /**
     * 车辆系统状态
     * @see com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum
     */
    private String systemStatus;

    /**
     * 接管人名称
     */
    private String takeoverUserName;

    /**
     * 接管来源
     * @see com.jdx.rover.monitor.enums.RemoteCommandSourceEnum
     */
    private String takeoverSource;

    /**
     * 接管状态（临时停车还是接管）
     * @see com.jdx.rover.monitor.entity.VehicleRemoteOperationStatusEnum
     */
    private String takeoverStatus;
}
