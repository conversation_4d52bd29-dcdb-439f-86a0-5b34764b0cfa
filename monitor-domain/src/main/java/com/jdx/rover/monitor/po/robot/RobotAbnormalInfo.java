/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.po.robot;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 机器人(多合一/巡检) 异常错误信息表
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotAbnormalInfo extends BaseDomain {

  /**
   * <p>
   * 产品标识
   * </p>
   */
  private String productKey;

  /**
   * <p>
   * 车牌号
   * </p>
   */
  private String remarkName;

  /**
   * <p>
   * 设备编号
   * </p>
   */
  private String deviceName;

  /**
   * <p>
   * 启动标识
   * </p>
   */
  private Long bootId;

  /**
   * <p>
   * 性能模块
   * </p>
   */
  private String performanceModule;

  /**
   * <p>
   * 异常模块
   * </p>
   */
  private String abnormalModule;

  /**
   * <p>
   * 错误码
   * </p>
   */
  private String errorCode;

  /**
   * <p>
   * 错误编号
   * </p>
   */
  private Integer errorNumber;

  /**
   * <p>
   * 错误级别
   * </p>
   */
  private String errorLevel;

  /**
   * <p>
   * 错误消息
   * </p>
   */
  private String errorMsg;

  /**
   * <p>
   * 触发时间
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * 结束时间
   * </p>
   */
  private Date endTime;

  /**
   * <p>
   * 工作模式
   * </p>
   */
  private String workMode;

  /**
   * 分组编号
   */
  private String groupNo;

  /**
   * 站点名称
   */
  private String stationName;

  /**
   * 任务号
   */
  private String taskNo;

  /**
   * 位置
   */
  private String point;

  /**
   * 处理方式
   */
  private String processMode;

  /**
   * 跟进用户
   */
  private String followUser;


}
