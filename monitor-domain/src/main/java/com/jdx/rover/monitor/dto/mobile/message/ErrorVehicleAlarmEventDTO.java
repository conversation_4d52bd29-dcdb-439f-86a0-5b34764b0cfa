package com.jdx.rover.monitor.dto.mobile.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 异常车辆告警信息
 */
@Data
public class ErrorVehicleAlarmEventDTO {

    /**
     * 告警事件
     */
    private String alarmEvent;

    /**
     * 上报时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
}
