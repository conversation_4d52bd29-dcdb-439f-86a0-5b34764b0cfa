/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 报警类型和错误异常关联.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DeviceAlarmCodeEnum {

  /**
   * 表示设备电量过低的报警代码。
   */
  LOW_BATTERY("LOW_BATTERY", "低电量", DeviceAlarmCategoryEnum.NORMAL.getValue(),"无需人工介入"),
  /**
   * 表示设备长时间停留的报警代码。
   */
  VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT",  "长时间停留", DeviceAlarmCategoryEnum.SEVERITY.getValue(),"技术支持远程介入，远程遥控脱困"),
  /**
   * 表示设备发生碰撞的报警代码。
   */
  VEHICLE_CRASH("VEHICLE_CRASH", "碰撞", DeviceAlarmCategoryEnum.CRITICAL.getValue(),"技术支持远程介入，远程遥控脱困"),
  /**
   * 表示设备发生任务超时的报警代码。
   */
  VEHICLE_TASK_TIMEOUT("VEHICLE_TASK_TIMEOUT", "任务超时", DeviceAlarmCategoryEnum.CRITICAL.getValue(),"技术支持远程介入，远程遥控脱困"),
  /**
   * 表示设备发生遇阻的报警代码。
   */
  VEHICLE_CONGESTED("VEHICLE_CONGESTED", "车辆遇阻", DeviceAlarmCategoryEnum.SEVERITY.getValue(),"技术支持远程介入，远程遥控脱困"),
  ;

  /**
   * <p>
   * 报警类型.
   * </p>
   */
  private String alarmCode;

  /**
   * <p>
   * 错误数字编码.
   * </p>
   */
  private String alarmMsg;

  /**
   * <p>
   * 报警层级.
   * </p>
   */
  private String category;

  /**
   * <p>
   * 解决方案.
   * </p>
   */
  private String solutionName;


  public static DeviceAlarmCodeEnum of(String alarmCode) {
    for (DeviceAlarmCodeEnum em : DeviceAlarmCodeEnum.values()) {
      if (Objects.equals(alarmCode, em.getAlarmCode())) {
        return em;
      }
    }
    return null;
  }
}
