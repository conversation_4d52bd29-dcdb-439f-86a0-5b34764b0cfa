/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.vehicle.sort;

import com.jdx.rover.metadata.api.domain.enums.VehicleBusinessTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆业务类型分数,配送为10,售卖为20
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleBusinessTypeScoreEnum {
  DISPATCH(10, VehicleBusinessTypeEnum.DISPATCH.getName()),
  VENDING(20, VehicleBusinessTypeEnum.VENDING.getName()),
  ;

  private final Integer value;

  private final String title;
}

