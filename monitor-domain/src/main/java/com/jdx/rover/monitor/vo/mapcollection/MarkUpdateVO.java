/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:52
 * @description 修改标记
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MarkUpdateVO extends MarkCreateVO implements Serializable {

    /**
     * 标记ID
     */
    @NotNull(message = "标记ID不能为空")
    private Integer markId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件列表
     */
    private List<AttachmentVO> attachmentList;

    @Data
    public static class AttachmentVO implements Serializable {

        /**
         * 文件类型
         */
        private String type;

        /**
         * 文件key
         */
        private String fileKey;
    }
}
