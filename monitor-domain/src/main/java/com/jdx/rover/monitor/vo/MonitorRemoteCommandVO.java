/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a view object for remote control abstract calss.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorRemoteCommandVO {

  /**
   * <p>
   * Represents the product key. It's changeable.
   * </p>
   */
  private String productKey;

  /**
   * <p>
   * Represents the vehicle's name. The default value is 0. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the name of user. The default value is 0. It's changeable.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the source of command. The default value is 0. It's changeable.
   * </p>
   */
  private String commandSource;

  /**
   * <p>
   * Represents the timeStamp of user operate recovery request. It's changeable.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date timeStamp;

}
