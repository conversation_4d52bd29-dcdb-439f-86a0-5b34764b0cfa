/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums;

import com.google.common.collect.Lists;
import com.jdx.rover.monitor.enums.device.DeviceNetworkTypeEnum;
import com.jdx.rover.monitor.enums.device.DeviceRealtimeStateEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * PDA产品字段映射枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum PdaFieldDisplayEnum {
  DEFAULT("DEFAULT", "默认值", null, Lists.newArrayList("value", "title"), Lists.newArrayList("code", "name")),
  PDA_NETWORK_TYPE_ENUM("PDA_NETWORK_TYPE_ENUM", "网络类型", DeviceNetworkTypeEnum.class, Lists.newArrayList("value", "title"), DEFAULT.displayNameList),
  PDA_REALTIME_STATE_ENUM("PDA_REALTIME_STATE_ENUM", "设备状态", DeviceRealtimeStateEnum.class, Lists.newArrayList("value", "property", "title"), DEFAULT.displayNameList),

  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 名称
   */
  private final String title;
  /**
   * 类型
   */
  private final Class clazz;
  /**
   * 字段列表
   */
  private final List<String> fieldNameList;
  /**
   * 展示名称
   */
  private final List<String> displayNameList;
}