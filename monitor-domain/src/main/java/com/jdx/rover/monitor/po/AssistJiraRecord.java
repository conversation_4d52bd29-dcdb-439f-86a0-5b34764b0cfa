/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is an assist jira record entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AssistJiraRecord extends BaseDomain {

  /**
   * <p>
   * Represents the vehicle name of assist jira record.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the reported flag of assist jira record.
   */
  private Boolean isReported;

  /**
   * <p>
   * Represents the reported flag of assist jira record.
   * </p>
   */
  private Integer issueTypeId;

  /**
   * <p>
   * Represents the topic of assist jira record.
   * </p>
   */
  private String topic;

  /**
   * <p>
   * Represents the description of assist jira record.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the vehicle alarm event type.
   * </p>
   */
  private String alarmEvent;

  /**
   * <p>
   * Represents the vehicle system state.
   * </p>
   */
  private String systemState;

  /**
   * <p>
   * Represents the priority id of assist jira record.
   * </p>
   */
  private Integer priorityId;

  /**
   * <p>
   * Represents the defect type id of assist jira record.
   * </p>
   */
  private Integer defectTypeId;

  /**
   * <p>
   * Represents the defect type value of assist jira record.
   * </p>
   */
  private String defectTypeValue;

  /**
   * <p>
   * Represents the severityId of assist jira record.
   * </p>
   */
  private String severityId;

  /**
   * <p>
   * Represents the severity value of assist jira record.
   * </p>
   */
  private String severityValue;

  /**
   * <p>
   * Represents the discovery phase id of assist jira record.
   * </p>
   */
  private Integer discoveryPhaseId;

  /**
   * <p>
   * Represents the discovery phase value of assist jira record.
   * </p>
   */
  private String discoveryPhaseValue;

  /**
   * <p>
   * Represents the discover situation id of assist jira record.
   * </p>
   */
  private Integer discoverSituationId;

  /**
   * <p>
   * Represents the discover situation value of assist jira record.
   * </p>
   */
  private String discoverSituationValue;

  /**
   * <p>
   * Represents the component id of assist jira record.
   * </p>
   */
  private String componentId;

  /**
   * <p>
   * Represents the component name of assist jira record.
   * </p>
   */
  private String componentName;

  /**
   * <p>
   * Represents the use case of assist jira record.
   * </p>
   */
  private String useCase;

  /**
   * <p>
   * Represents the version id of assist jira record.
   * </p>
   */
  private String versionId;

  /**
   * <p>
   * Represents the version value of assist jira record.
   * </p>
   */
  private String versionValue;

  /**
   * <p>
   * Represents the operate time of assist jira record.
   * </p>
   */
  private Date operateTime;

  /**
   * <p>
   * Represents the version value of assist jira record.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the name of station.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the name of city.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the exception id.
   * </p>
   */
  private Integer exceptionId;

}
