package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故信息来源枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentFlowEnum {

    VEHICLE_REPORT("VEHICLE_REPORT", "车端上报事故"),
    MANUALLY_CREATED("MANUALLY_CREATED","手动创建事故"),
    TECHNICAL_SUPPORT_EDIT("TECHNICAL_SUPPORT_EDIT", "远程安全员编辑"),
    OPERATION_ACCEPT("OPERATION_ACCEPT", "区域安全员操作生成事故单"),
    OPERATION_REJECT("OPERATION_REJECT", "区域安全员操作无需处理"),
    OPERATION_SUBMIT("OPERATION_SUBMIT", "区域安全员提报事故"),
    BUG_REPORT("BUG_REPORT", "提报缺陷"),
    SAFETY_GROUP_EDIT("SAFETY_GROUP_EDIT", "安全组编辑")
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentFlowEnum accidentFlowEnum : AccidentFlowEnum.values()) {
            if (accidentFlowEnum.getValue().equals(value)) {
                return accidentFlowEnum.getName();
            }
        }
        return null;
    }

    /**
     * <p>
     * 获取枚举对象
     * </p>
     */
    public static AccidentFlowEnum getByValue(String value) {
        for (AccidentFlowEnum accidentFlowEnum : AccidentFlowEnum.values()) {
            if (accidentFlowEnum.getValue().equals(value)) {
                return accidentFlowEnum;
            }
        }
        return null;
    }
}
