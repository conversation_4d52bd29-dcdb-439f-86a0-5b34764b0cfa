/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 设备属性配置
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DevicePropertyCodeEnum {

    ALL_STATUS("ALL_STATUS", "设备全量状态", newArrayList()),
    /**
     * 监控PDA状态
     */
    MONITOR_PDA_STATUS("MONITOR_PDA_STATUS", "监控PDA状态", newArrayList("onlineStatus","positionStatus","networkType","burialStatus","deviceRestElectric")),
    /**
     * 监控机器人设备状态
     */
    MONITOR_ROBOT_STATUS("MONITOR_ROBOT_STATUS", "监控机器人状态", newArrayList("onlineStatus", "charging", "battery", "power", "taskType", "sceneSignal", "x", "y","yaw","emergencyState", "motorEnabled", "linear")),
    /**
     * 监控无人车机器人设备状态
     */
    MONITOR_ROVER_STATUS("MONITOR_ROVER_STATUS", "新石器机器人状态", newArrayList("onlineStatus","vehicleState", "systemState", "latitude", "longitude","heading", "power", "speed", "currentStopId", "mileageToNextStop", "currentStopFinishedMileage", "reportTime")),
    /**
     * 机器人设备版本信息
     */
    MONITOR_ROBOT_VERSION("MONITOR_ROBOT_VERSION", "监控机器人版本", newArrayList("mapId", "mapVersion","version","videoVersion","androidVersion"));

    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 属性列表
     */
    private final List<String> propertyList;

    /**
     * 创建列表
     */
    private static <E> ArrayList<E> newArrayList(E... elements) {
        ArrayList<E> list = new ArrayList<>(elements.length);
        Collections.addAll(list, elements);
        return list;
    }
}
