/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

/**
 * <p>
 * This is a entity for getting monitor issue detail.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it is an entity
 * so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorIssueDetailRequestVO {

  /**
   * <p>
   * Reprensents the name of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Reprensents the issue number of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Reprensents the issue status of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String issueStatus;
}
