package com.jdx.rover.monitor.vo.cockpit;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @description: 入座座席VO
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-06-11 10:10
 **/
@Data
public class EnterCockpitVO {

    /**
     * 座席模式
     */
    @NotBlank(message = "座席模式不能为空")
    private String cockpitMode;

    /**
     * 座席编号
     */
    @NotBlank(message = "座席编号不能为空")
    private String cockpitNumber;
}