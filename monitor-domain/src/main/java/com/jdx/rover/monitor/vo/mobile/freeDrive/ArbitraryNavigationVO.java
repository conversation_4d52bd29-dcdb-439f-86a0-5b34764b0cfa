package com.jdx.rover.monitor.vo.mobile.freeDrive;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @description: ArbitraryNavigationVO
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-16 18:25
 **/
@Data
public class ArbitraryNavigationVO {

    /**
     * 任意点名称
     */
    @NotBlank(message = "任意点名称不能为空")
    private String stopName;

    /**
     * 任意点经度
     */
    @NotNull(message = "任意点经度不能为空")
    private Double longitude;

    /**
     * 任意点纬度
     */
    @NotNull(message = "任意点纬度不能为空")
    private Double latitude;

    /**
     * 任意点朝向
     */
    @NotNull(message = "任意点朝向不能为空")
    private Double heading;

    /**
     * 已选车辆列表
     */
    @NotEmpty(message = "已选车辆列表不能为空")
    private List<String> vehicleNameList;
}
