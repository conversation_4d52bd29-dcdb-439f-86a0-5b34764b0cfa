/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <p>
 * 设备分类枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DeviceEnum {
  /**
   * <p>
   * 分类枚举
   * </p>
   */
  PDA("PDA", "PDA设备");

  /**
   * <p>
   * 设备值
   * </p>
   */
  private String value;

  /**
   * <p>
   * 设备名
   * </p>
   */
  private String name;

}
