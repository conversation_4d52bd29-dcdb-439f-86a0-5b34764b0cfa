/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025/8/11 23:05
 * @description 下发最大限速
 */
@Data
public class MonitorSetSpeedLimitVO {

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String vehicleName;

    /**
     * 是否开启临时限速
     */
    @NotNull(message = "是否开启临时限速不能为空")
    private Boolean isTempSpeedLimitEnabled = false;

    /**
     * 临时限速类型
     * @see com.jdx.rover.monitor.enums.SpeedLimitTypeEnum
     */
    private String tempSpeedLimitType;

    /**
     * 临时限速值，KM/H
     */
    private Double tempSpeedLimit;
}
