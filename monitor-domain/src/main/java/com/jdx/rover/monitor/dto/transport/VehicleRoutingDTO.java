package com.jdx.rover.monitor.dto.transport;

import com.jdx.rover.monitor.dto.TransportStopMapInfoDTO;
import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;
import lombok.Data;

import java.util.List;

/**
 * 车辆routing信息
 */
@Data
public class VehicleRoutingDTO {

    private List<TransportStopMapInfoDTO> stop;

    private List<MonitorRoutingPointEntity> planningRoutingPoint;

    private List<MonitorRoutingPointEntity> finishedRoutingPoint;
}
