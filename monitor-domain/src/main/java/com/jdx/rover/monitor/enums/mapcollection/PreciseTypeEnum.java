/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/18 16:32
 * @description 精度方案
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum PreciseTypeEnum {
    STANDARD_PRECISION("STANDARD_PRECISION", "标精规划"),
    HIGH_PRECISION("HIGH_PRECISION", "高精规划"),
    UNKNOWN("UNKNOWN", "UNKNOWN");

    /**
     * code
     */
    private final String code;

    /**
     * name
     */
    private final String name;

    /**
     * 根据preciseType获取枚举
     *
     * @param preciseType preciseType
     * @return PreciseTypeEnum
     */
    public static PreciseTypeEnum of(String preciseType) {
        for (PreciseTypeEnum em : PreciseTypeEnum.values()) {
            if (em.getCode().equals(preciseType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
