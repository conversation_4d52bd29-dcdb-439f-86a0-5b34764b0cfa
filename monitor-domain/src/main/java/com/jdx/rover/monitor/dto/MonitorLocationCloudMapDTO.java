/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 车辆当前周边点云地图
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorLocationCloudMapDTO {

  /**
   * <p>
   * 指令id
   * </p>
   */
  private Long id;

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 点云地图列表
   * </p>
   */
  private List<String> cloudPathList = new ArrayList<>();

}
