/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * 地图页车辆实时状态
 * entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorMapVehicleRealtimeInfoDTO {

  /**
   * <p>
   * Represents the systemState of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String systemState;

  /**
   * <p>
   * Represents the schedule state of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String scheduleState;

  /**
   * <p>
   * Represents the schedule task type of the vehicle. The default value is false. It's changeable.
   * </p>
   */
  private String taskType;

}
