/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 停靠事件类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceTaskActionEnum {

    /**
     * 停靠点出发事件。
     */
    STATUS_START("STATUS_START", "前往"),
    /**
     * 停靠点到达事件。
     */
    STATUS_END("STATUS_END", "到达"),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceTaskActionEnum 枚举类型。
     */
    public static DeviceTaskActionEnum getByValue(String value) {
        for (DeviceTaskActionEnum em : DeviceTaskActionEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}
