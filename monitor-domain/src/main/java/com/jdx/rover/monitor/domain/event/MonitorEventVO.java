/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * This is an tracking event.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorEventVO {

  /**
   * <p>
   * Represents the name of user.
   * </p>
   */
  private String userName;

  /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the occur time of event.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date time;

  /**
   * <p>
   * Represents the event action name.
   * </p>
   */
  private String action;

  /**
   * 扩展map
   */
  private Map<String, Object> extendMap;

}
