package com.jdx.rover.monitor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: EnumDTO
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-06-15 09:59
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class EnumDTO {

    /**
     * 值
     */
    private String code;

    /**
     * 名称
     */
    private String name;
}