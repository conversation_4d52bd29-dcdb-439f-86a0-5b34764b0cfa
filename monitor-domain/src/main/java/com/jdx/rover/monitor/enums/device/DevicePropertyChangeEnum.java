/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 设备参数变化枚举
 * </p>
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DevicePropertyChangeEnum {

    /**
     * 车端上报
     */
    ONLINE_STATUS("onlinestatus","在离线状态"),
    ;
    /**
     * 值.
     */
    private final String value;

    /**
     * 标题.
     */
    private final String name;

    /**
     * 根据给定的值获取对应的 DevicePropertyChangeEnum 枚举类型。
     */
    public static DevicePropertyChangeEnum getByValue(String value) {
        for (DevicePropertyChangeEnum em : DevicePropertyChangeEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }
}
