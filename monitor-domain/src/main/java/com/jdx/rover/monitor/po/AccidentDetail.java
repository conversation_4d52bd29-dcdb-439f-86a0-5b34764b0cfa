package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;

/**
 * 事故详情表
 */
@Data
public class AccidentDetail extends BaseModel {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 运营提报人
     */
    private String operationUser;

    /**
     * 处理方式
     */
    private String operationHandleMethod;

    /**
     * 是否需要赔偿
     */
    private Integer operationCompensated;

    /**
     * 赔偿金额
     */
    private String operationAmount;

    /**
     * 运营事故分类
     */
    private String operationAccidentType;

    /**
     * 运营-事故责任判定
     */
    private String operationAccidentJudge;

    /**
     * 运营-事故原因
     */
    private String operationAccidentReason;

    /**
     * 运营-是否上报车网
     */
    private Integer operationIsReportVehicleNet;

    /**
     * 技术支持-事故分类
     */
    private String technicalSupportAccidentType;

    /**
     * 技术支持-事故等级
     */
    private String technicalSupportAccidentLevel;

    /**
     * 技术支持-事故描述
     */
    private String technicalSupportDescription;

    /**
     * 安全组-事故分类
     */
    private String safetyGroupAccidentType;

    /**
     * 安全组-事故等级
     */
    private String safetyGroupAccidentLevel;

    /**
     * 安全组-描述
     */
    private String safetyGroupDescription;

    /**
     * 安全组-事故模块
     */
    private Integer safetyGroupAccidentModule;

    /**
     * 安全组-事故标签
     */
    private Long safetyGroupAccidentTag;

    /**
     * 安全组-解决方案
     */
    private String safetyGroupAccidentSolution;

    /**
     * 安全组-解决情况
     */
    private String safetyGroupAccidentResolutionStatus;

    /**
     * 挂起原因
     */
    private String safetyAccidentSuspendReason;
}
