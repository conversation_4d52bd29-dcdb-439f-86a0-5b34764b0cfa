/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * This is a basic infomation entity which contains user id and station.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe.
 * But it is an entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class MonitorStationVehicleMapInfoRequestVO {

  /**
   * <p>
   * The station id. The default value is null. It's changeable.
   * </p>
   */
  private Integer stationId;

  /**
   * <p>
   * The vehicle list. The default value is null. It's changeable.
   * </p>
   */
  @NotEmpty
  private List<String> vehicleName;

}
