/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机器人调度任务缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Data
public class RobotScheduleDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String deviceName;

  /**
   * 开始时间
   */
  private Date recordTime;

  /**
   * 业务编号
   */
  private String businessNo;

  /**
   * 任务号
   */
  private String taskNo;

  /**
   * 任务Id号
   */
  private String taskId;

  /**
   * 任务类型
   * CALL_DEVICE("CALL_DEVICE", "入库呼车"),
   * PUTAWAY("PUTAWAY", "入库上架"),
   * BIND("BIND", "出库绑箱"),
   * PICK("PICK", "出库拣选"),
   * RECHECK("RECHECK", "出库复核"),
   * PARK("PARK", "泊车"),
   * CHARGE("CHARGE", "充电"),
   * NAVIGATION("NAVIGATION", "导航")
   */
  private String taskType;

  /**
   * 跟进用户
   */
  private String taskTypeName;

  /**
   * 最晚生产时间
   */
  private Date latestOutTime;

  /**
   * 跑马灯倒计时时间
   */
  private Integer reminderTime;

  /**
   * 任务状态
   */
  private String taskStatus;

  /**
   * 上装类型
   */
  private String shelfTypeName;

  /**
   * 上装架子编号
   */
  private String shelfNo;

  /**
   * 推送时间
   */
  private Date timestamp;

  /**
   * 机器人任务停靠点
   */
  private ParkStop parkStop;

  @Data
  @NoArgsConstructor
  public static class ParkStop {

    /**
     * 停靠点
     */
    private String stopId;

    /**
     * 停靠点名称
     */
    private String stopName;

    /**
     * 停靠点的X坐标
     */
    private Double x;

    /**
     * 停靠点的Y坐标
     */
    private Double y;

  }
}
