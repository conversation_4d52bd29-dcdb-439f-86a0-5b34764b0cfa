/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 工作记录 数据库表名为work_record
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkRecord extends BaseDomain implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 座席编号
     */
    private String cockpitNumber;

    /**
     * 座席状态
     */
    private String cockpitStatus;

    /**
     * 座席模式
     */
    private String cockpitMode;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 座席类型
     */
    private String cockpitType;

    /**
     * 座席团队编号
     */
    private String cockpitTeamNumber;
}