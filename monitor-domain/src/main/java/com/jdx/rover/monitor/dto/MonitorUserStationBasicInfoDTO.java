/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.List;

import lombok.Data;

/**
 * <p>
 * This is a user station basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorUserStationBasicInfoDTO {

  /**
   * <p>
   * Represents the station's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;
  
  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the station's lat. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the station's lon. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the list of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<MonitorUserVehiceBasicInfoDTO> vehicle;

  /**
   * <p>
   * Represents the total count of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Integer total;

  /**
   * <p>
   * Represents the abnormal count of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private Integer abnormalNum;

}
