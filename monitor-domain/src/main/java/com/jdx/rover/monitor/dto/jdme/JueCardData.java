package com.jdx.rover.monitor.dto.jdme;

import lombok.Data;

import java.util.List;

@Data
public class JueCardData {
    //卡片数据要发给哪个群
    private String groupId;
    //卡片摘要
    private String summary;
    //卡片头
    private JueCardDataHeader header;
    //卡片元素
    private List<JueCardDataElementText> elements;
    //事故快照
    private List<String> snapshotUrlList;
    //卡片底部的按钮
    private JueCardDataButtons buttons;
}
