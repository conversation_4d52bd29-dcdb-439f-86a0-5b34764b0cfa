/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.vo;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * This is the entity for issue alarm item.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueAlarmAddVO {

  /**
   * <p>
   * Represents the alarm item's id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the alarm item's event type. The default value is null. It's changeable.
   * </p>
   */
  private String type;

  /**
   * <p>
   * Represents the alarm item's event type name. The default value is null. It's changeable.
   * </p>
   */
  private String typeName;

  /**
   * <p>
   * Represents the alarm item's start date. The default value is null. It's changeable.
   * </p>
   */
  private Date startTimestamp;

  /**
   * <p>
   * Represents the alarm item's title. The default value is null. It's changeable.
   * </p>
   */
  private String title;

  /**
   * <p>
   * Represents the alarm item's description. The default value is null. It's changeable.
   * </p>
   */
  private String description;

  /**
   * <p>
   * Represents the attachment list of the alarm item. The default value is null. It's changeable.
   * </p>
   */
  private List<String> attachmentList;
}
