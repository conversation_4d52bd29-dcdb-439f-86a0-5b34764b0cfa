package com.jdx.rover.monitor.po.mapcollection;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jdx.rover.monitor.base.BaseModel;
import com.jdx.rover.monitor.domain.jsonhandler.RealRouteListTypeHandler;
import com.jdx.rover.monitor.po.mapcollection.json.RealRoutePoint;
import lombok.Data;

import java.util.Date;
import java.util.List;
import lombok.EqualsAndHashCode;

/**
 * 采图时间记录表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "map_collection_task_time_record", autoResultMap = true)
public class MapCollectionTaskTimeRecord extends BaseModel {

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否暂停
     */
    private Boolean paused;

    /**
     * 实时路径
     */
    @TableField(typeHandler = RealRouteListTypeHandler.class)
    private List<RealRoutePoint> realRoute;
    /**
     * 行驶里程
     */
    private Double drivingMileage;

    /**
     * 座席编号
     */
    private String cockpitNumber;
}
