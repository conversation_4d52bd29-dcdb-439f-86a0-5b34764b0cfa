package com.jdx.rover.monitor.enums.user;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

import java.util.Map;

/**
 * 驾驶动力状态
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
@Slf4j
public enum UserConfigDriveTorqueEnum {
    /**
     * 驾驶动力状态
     */
    POWER(100, "动力"),
    STANDARD(80, "标准"),
    STABILITY(60, "稳定"),
    LOW_SPEED(40, "慢速"),
    ;

    private static final Map<Integer, UserConfigDriveTorqueEnum> CODE_MAP = EnumUtils.getEnumMap(UserConfigDriveTorqueEnum.class, UserConfigDriveTorqueEnum::getCode);

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 通过code获取枚举
     */
    public static UserConfigDriveTorqueEnum getByCode(final Integer code) {
        return CODE_MAP.get(code);
    }
}
