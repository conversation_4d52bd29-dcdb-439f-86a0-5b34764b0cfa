package com.jdx.rover.monitor.po.mobile;

import com.jdx.rover.monitor.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;

/**
 * @description: 车辆实时位置表
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-05-24 10:02
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class VehiclePosition extends BaseModel {

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 车辆位置
     */
    private Point point;
}