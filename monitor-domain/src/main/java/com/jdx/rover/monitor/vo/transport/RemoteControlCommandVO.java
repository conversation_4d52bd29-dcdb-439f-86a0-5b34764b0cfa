package com.jdx.rover.monitor.vo.transport;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * @description: 车辆遥控-指令-请求对象
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2025-02-06 17:55
 **/
@Data
public class RemoteControlCommandVO {

    /**
     * 车号
     */
    @NotBlank(message = "vehicleName不能为空")
    private String vehicleName;

    /**
     * 时间戳
     */
    @NotNull(message = "timeStamp不能为空")
    private Date timeStamp;
}