package com.jdx.rover.monitor.dto.xata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: xata数据拷贝任务明细对象
 * @author: wanggu<PERSON>i
 * @create: 2024-12-24 09:45
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DataCopyTaskItemDTO {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * topic列表
     */
    private List<String> topics;
}