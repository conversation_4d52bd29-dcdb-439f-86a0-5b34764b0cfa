/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * This is a view object for remote control command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorRemoteControlCommandVO extends MonitorRemoteCommandVO {

  /**
   * <p>
   * Represents the id of request. The default value is 0. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the command type. It's changeable.
   * </p>
   */
  private String commandType;

  /**
   * <p>
   * Represents the target velocity. The default value is 0.0. It's changeable.
   * </p>
   */
  @NotNull
  private Double targetVelocity;

  /**
   * <p>
   * Represents the target angle. The default value is 0.0. It's changeable.
   * </p>
   */
  @NotNull
  private Double targetAngle;

  /**
   * <p>
   * Represents the name of module. It's changeable.
   * </p>
   */
  private String moduleName;

}
