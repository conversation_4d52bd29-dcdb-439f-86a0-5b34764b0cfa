package com.jdx.rover.monitor.enums.message;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MessageLevelEnum {
  INFO("INFO", "信息"),
  WARN("WARN", "告警"),
  ERROR("ERROR", "错误"),
  FATAL("FATAL", "FATAL错误"),
  ;

  /**
   * 值
   */
  private final String value;
  /**
   * 标题描述
   */
  private final String title;
}
