package com.jdx.rover.monitor.jsf;

import com.jd.jsf.gd.util.RpcContext;
import com.jdx.rover.monitor.constant.BusinessConstants;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * @description: JSF工具类
 * @author: wangguotai
 * @create: 2024-11-04 17:41
 **/
public class JsfUtil {

    /**
     * 构造函数
     */
    private JsfUtil() {

    }

    /**
     * 获取上下文
     *
     * @return Map<String, Object>
     */
    public static Map<String, Object> getAttachment() {
        RpcContext context = RpcContext.getContext();
        if (Objects.isNull(context)) {
            return Collections.emptyMap();
        }
        return context.getAttachments();
    }

    /**
     * 获取上下文数据
     *
     * @param key key
     * @return Object
     */
    public static Object getAttachmentData(String key) {
        BusinessCheckUtil.checkNull(key, MonitorErrorEnum.ERROR_CALL_CHECK_PARAM);
        RpcContext context = RpcContext.getContext();
        if (Objects.isNull(context)) {
            return null;
        }
        return context.getAttachment(key);
    }

    /**
     * 获取h5 siteId
     *
     * @return Integer
     */
    public static Integer getH5SiteId() {
        Object data = getAttachmentData(BusinessConstants.H5_SITE_ID_KEY);
        BusinessCheckUtil.checkNull(data, MonitorErrorEnum.ERROR_H5_GET_SITE_ID);
        assert data != null;
        return Integer.valueOf((String) data);
    }

    /**
     * 获取h5 userAccount
     *
     * @return String
     */
    public static String getH5UserAccount() {
        Object data = getAttachmentData(BusinessConstants.H5_USER_ACCOUNT_KEY);
        BusinessCheckUtil.checkNull(data, MonitorErrorEnum.ERROR_H5_GET_USER_ACCOUNT);
        return (String) data;
    }
}