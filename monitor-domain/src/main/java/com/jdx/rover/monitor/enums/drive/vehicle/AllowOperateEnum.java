/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.drive.vehicle;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 允许接管状态
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AllowOperateEnum {
    /**
     * 允许接管状态
     */
    FORBID("禁止"),
    ALLOW("允许"),
    EXIT("退出"),
    ;
    /**
     * 标题描述
     */
    private final String title;
}
