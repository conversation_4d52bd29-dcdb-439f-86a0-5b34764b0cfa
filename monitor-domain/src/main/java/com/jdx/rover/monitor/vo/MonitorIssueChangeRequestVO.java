/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * This is a ws unsubscribe information entity which contains user and state.
 * </p>
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class MonitorIssueChangeRequestVO {

  /**
   * <p>
   * The subscribe issue state. The default value is null. It's changeable.
   * </p>
   */
  private String issueState;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;

}
