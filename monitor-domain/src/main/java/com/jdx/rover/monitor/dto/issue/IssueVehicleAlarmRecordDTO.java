/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.issue;

import java.util.Date;
import lombok.Data;

/**
 * <p>
 * This is a vehicle alarm record data transform object entity.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueVehicleAlarmRecordDTO {

  /**
   * <p>
   * Represents the vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the alarm code. The default value is null. It's changeable.
   * </p>
   */
  private String alarmCode;

  /**
   * <p>
   * Represents the alarm type of the vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the name of the vehicle alarm. The default value is null. It's changeable.
   * </p>
   */
  private String alarmNo;

  /**
   * <p>
   * The alarm source. The default value is null. It's changeable.
   * </p>
   */
  private String source;

  /**
   * <p>
   * Represents the timestamp of alarm. The default value is null. It's changeable.
   * </p>
   */
  private Date timestamp;

  /**
   * <p>
   * Represents the no of issue. The default value is null. It's changeable.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the selected by issue. The default value is 0. It's changeable.
   * </p>
   */
  private boolean isSelected;
}
