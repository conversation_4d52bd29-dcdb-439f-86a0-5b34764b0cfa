package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 消息类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum MessageTypeEnum {

    ACCIDENT("ACCIDENT", "事故"),
    REPAIR_CONFIRM("REPAIR_CONFIRM", "维修待确认"),
    REPAIR_REJECT("REPAIR_REJECT", "维修驳回"),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (MessageTypeEnum messageTypeEnum : MessageTypeEnum.values()) {
            if (messageTypeEnum.getValue().equals(value)) {
                return messageTypeEnum.getName();
            }
        }
        return null;
    }
}
