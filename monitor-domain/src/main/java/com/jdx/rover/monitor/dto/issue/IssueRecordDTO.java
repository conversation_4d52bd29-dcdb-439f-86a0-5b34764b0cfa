/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.issue;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a issue record data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueRecordDTO {

  /**
   * <p>
   * Represents the id of issue record.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the no of issue record.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the type of alarm.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the time of alarm.
   * </p>
   */
  private Date alarmTime;

  /**
   * <p>
   * Represents the bussiness type of vehicle.
   * </p>
   */
  private String vehicleBussinessType;

  /**
   * <p>
   * Represents the id of city.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the id of station.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the source of issue record.
   * </p>
   */
  private String source;

  /**
   * <p>
   * Represents the state of issue record.
   * </p>
   */
  private String state;

  /**
   * <p>
   * Represents the operate user.
   * </p>
   */
  private String operateUser;

  /**
   * <p>
   * Represents the report user.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the result of issue record.
   * </p>
   */
  private String result;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  private Date endTime;

  /**
   * <p>
   * Represents the report time of issue record.
   * </p>
   */
  private Date reportTime;

  /**
   * <p>
   * Represents the reported jira no of issue record.
   */
  private String jiraNo;

}
