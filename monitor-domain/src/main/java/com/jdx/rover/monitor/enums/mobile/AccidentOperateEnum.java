package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故卡片操作类型
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentOperateEnum {

    REPORT("REPORT", "生成事故单"),
    REJECT("REJECT", "无需处理"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentOperateEnum accidentOperateEnum : AccidentOperateEnum.values()) {
            if (accidentOperateEnum.getValue().equals(value)) {
                return accidentOperateEnum.getName();
            }
        }
        return null;
    }
}
