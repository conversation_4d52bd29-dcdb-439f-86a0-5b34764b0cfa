/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.util.List;

/**
 * 监控多车页
 *
 * <AUTHOR>
 */
@Data
public class MultiVehicleRealtimeDTO {
  /**
   * 车辆业务类型(配送/售卖)
   */
  private String vehicleBusinessType;

  /**
   * 车辆用途(TEST测试/OPERATION运营)
   */
  private List<String> vehicleUseCaseList;

  /**
   * 当前分类(失联/碰撞/传感器异常/运营报警/停车/电量/胎压/正常/离线/全部)
   */
  private String currentCategory;

  /**
   * 排序类型(VEHICLE_NAME,BUSINESS_TYPE,POWER)
   */
  private String sortType;

  /**
   * 标签类型(ERROR,NORMAL,OFFLINE,ALL)
   */
  private String tabType;

  /**
   * 失联及异常条数
   */
  private Integer errorSize;

  /**
   * 正常条数
   */
  private Integer normalSize;

  /**
   * 离线条数
   */
  private Integer offlineSize;

  /**
   * 全部总数
   */
  private Integer allSize;

  /**
   * 关注车辆总数
   */
  private Integer interestedSize;

  /**
   * 分组车辆列表
   */
  private List<MultiVehiclePageDTO> multiVehiclePageList;
}
