package com.jdx.rover.monitor.dto.xata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: xata数据拷贝任务对象
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-12-24 09:43
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DataCopyTaskDTO {

    /**
     * 问题来源
     */
    private String type;

    /**
     * 车号
     */
    private String carNum;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 场景
     */
    private String scene;

    /**
     * 环境
     */
    private String env;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 任务明细列表
     */
    private List<DataCopyTaskItemDTO> dataItems;
}