/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a basic infomation entity which contains user id and name.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe.
 * But it is an entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class MonitorUserVehicleLinkBasicInfoRequestVO {

  /**
   * <p>
   * The User id. The default value is null. It's changeable.
   * </p>
   */
  @NotNull
  @Min(1)
  private Integer userId;

  /**
   * <p>
   * The User name. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String userName;

  /**
   * <p>
   * The business type of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleBusinessType;

  /**
   * <p>
   * The special vehicle name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

}
