/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * This is a view object for mini monitor event add info identity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorEventAddVO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the event no. It's changeable.
   * </p>
   */
  private String eventNo;

  /**
   * <p>
   * Represents the event type. It's changeable.
   * </p>
   */
  private String eventType;

  /**
   * <p>
   * Represents the trace id. It's changeable.
   * </p>
   */
  private String traceId;

  /**
   * <p>
   * Represents the parent trace id. It's changeable.
   * </p>
   */
  private String parentTraceId;

  /**
   * <p>
   * Represents the event body. It's changeable.
   * </p>
   */
  private String eventBody;

  /**
   * <p>
   * Represents the timeStamp of operate request. It's changeable.
   * </p>
   */
  @NotNull
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportTime;

}
