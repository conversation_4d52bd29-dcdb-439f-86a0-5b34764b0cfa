/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.enums.scene;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 驾驶场景
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DriveSceneEnum {
    STOP_AT("停靠场景"),
    INTERSECTION("路口场景"),
    ;
    /**
     * 标题描述
     */
    private final String title;
}
