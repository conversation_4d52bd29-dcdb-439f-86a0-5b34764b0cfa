/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a view object for quit remote control command.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorQuitRemoteControlCommandVO extends MonitorRemoteCommandVO {

  /**
   * <p>
   * Represents the id of request. The default value is 0. It's changeable.
   * </p>
   */
  @NotNull
  private Integer id;

}
