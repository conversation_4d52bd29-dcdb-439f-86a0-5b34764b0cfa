/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.dto.cockpit;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 座席看板列表
 *
 * <AUTHOR>
 * @date 2023/06/07
 */
@Data
@NoArgsConstructor
public class MultiCockpitDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 驾驶舱编号
     */
    private String cockpitNumber;

    /**
     * 坐席类型(监控座席/驾舱座席)
     */
    private String cockpitType;

    /**
     * 座席模式
     */
    private String cockpitMode;

    /**
     * 在线用户
     */
    private String onlineUser;

    /**
     * 工作时长（单位秒）
     */
    private Integer workTimeTotal;

    /**
     * 完成工单数
     */
    private Integer completedIssueCount;

    /**
     * 座席工单
     */
    private List<CockpitIssue> issueList;

    public MultiCockpitDTO buildIssueStatistic(UserStatusDO userStatus) {
        int dateTime = (int) DateUtil.between(userStatus.getWorkStartTime(), new Date(), DateUnit.SECOND);
        this.setWorkTimeTotal(Objects.isNull(userStatus.getWorkTimeTotalHistory())?
                dateTime : userStatus.getWorkTimeTotalHistory() + dateTime);
        this.setCompletedIssueCount(userStatus.getCompleteIssueCount());
        return this;
    }

    /**
     * 座席工单
     */
    @Data
    public static class CockpitIssue implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 工单编号
         */
        private String issueNumber;

        /**
         * 车号
         */
        private String vehicleName;

        /**
         * 工单状态
         */
        private String issueState;

        /**
         * 工单类型
         */
        private String issueType;

        /**
         * 是否催单
         */
        private Boolean issueReminder;

        /**
         * 调度状态
         */
        private String scheduleState;

        /**
         * 车辆模式
         */
        private String vehicleState;

        /**
         * 系统状态
         */
        private String systemState;

        /**
         * 速度
         */
        private Float speed;

        /**
         * 工单开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
        private Date issueStartTime;

        /**
         * 处理工单时长
         */
        private Integer issueProcessTime;

        /**
         * 告警事件列表
         */
        private List<AlarmEvent> alarmEventList;
    }

        /**
         * 告警事件
         */
        @Data
        public static class AlarmEvent implements Serializable {
            @Serial
            private static final long serialVersionUID = 1L;

            /**
             * 告警事件
             */
            private String alarmEvent;

            /**
             * 上报时间
             */
            @JSONField(format = "yyyy-MM-dd HH:mm:ss")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
            private Date reportTime;
        }
    }
