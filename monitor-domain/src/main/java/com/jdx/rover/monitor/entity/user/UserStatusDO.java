package com.jdx.rover.monitor.entity.user;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户坐席模式DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Data
public class UserStatusDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 历史工作时长(单位秒)
     */
    private Integer workTimeTotalHistory;

    /**
     * 当次工作开始时间
     */
    private Date workStartTime;

    /**
     * 处理工单数
     */
    private Integer completeIssueCount;

    /**
     * 工单数据统计日期 格式yyyy-MM-dd
     */
    private String issueStatisticDate;

    /**
     * 工作模式(监控/座席)
     */
    private String workMode;

    /**
     * 座席模式(听单/盯车/巡查/自由)
     */
    private String cockpitMode;

    /**
     * 当前座席ID
     */
    private String cockpitNumber;

    /**
     * 上次坐席ID
     */
    private String lastCockpitNumber;

    /**
     * 记录时间
     */
    private Date recordTime;
}
