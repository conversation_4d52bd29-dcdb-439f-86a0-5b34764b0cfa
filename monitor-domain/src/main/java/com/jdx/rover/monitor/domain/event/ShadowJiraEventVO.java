/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.domain.event;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is an jira event.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ShadowJiraEventVO {

  /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the jira event time.
   * </p>
   */
  @NotNull
  private List<Date> debugTime;

  /**
   * <p>
   * Represents the report time of jira event.
   * </p>
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date reportTime;

  /**
   * <p>
   * Represents the report user of jira event.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the no of jira event.
   * </p>
   */
  @NotBlank
  private String jiraNo;

}
