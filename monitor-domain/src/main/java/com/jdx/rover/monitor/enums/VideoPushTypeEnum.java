/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a user meta data type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum VideoPushTypeEnum {
  /**
   * <p>
   * The enumerate push type.
   * </p>
   */
  JSMPEG("JSMPEG", "jsmpeg播放器"),
  SRS_RTC("SRS_RTC", "srs播放器");

  /**
   * <p>
   * The meta data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;

  /**
   * <p>
   * The meta data request name corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String name;
}
