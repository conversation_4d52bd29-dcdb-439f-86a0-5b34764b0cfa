/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jdx.rover.metadata.api.domain.enums.StationUseCaseEnum;
import com.jdx.rover.metadata.domain.dto.station.StationBasicDTO;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * This is a vehicle basic information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehiceBasicInfoDTO {

  /**
   * <p>
   * Represents the vehicle's name. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the bussiness type of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the version of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String vehicleVersion;

  /**
   * <p>
   * Represents the version of android. The default value is null. It's
   * changeable.
   * </p>
   */
  private String androidVersion;

  /**
   * <p>
   * Represents the version of video. The default value is null. It's
   * changeable.
   * </p>
   */
  private String videoVersion;

  /**
   * <p>
   * Represents the version of map. The default value is null. It's
   * changeable.
   * </p>
   */
  private String mapVersion;

  /**
   * <p>
   * Represents the cityName of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the name of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationName;

    /**
   * <p>
   * Represents the user of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUser;

  /**
   * <p>
   * Represents the phone of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUserPhone;

  /**
   * <p>
   * Represents the use case of station. The default value is null. It's
   * changeable.
   * </p>
   */
  private String stationUseCase;

  /**
   * 车辆类型
   */
  private String vehicleTypeName;

  /**
   * 车辆类型
   */
  private StationInfo stationInfo;

  /**
   * 车辆纬度
   */
  private Double vehicleLatitude;

  /**
   * 车辆经度
   */
  private Double vehicleLongitude;

  /**
   * 标签列表
   */
  private List<Integer> tagList;

  /**
   * 车辆关注状态
   */
  private Boolean vehicleAttentionState;

  /**
   * 供应商
   */
  private String supplier;

  public void setStation(StationBasicDTO station){
    this.stationName = station.getStationName();
    this.stationUseCase= StationUseCaseEnum.getNameByValue(station.getStationUseCase());
    this.stationUser =station.getPersonName();
    this.stationUserPhone =station.getContact();
    if (station.getAddressInfo() != null) {
      this.cityName =station.getAddressInfo().getCityName();
    }
    stationInfo = new StationInfo();
    stationInfo.stationName = station.getStationName();
    stationInfo.stationUseCase=StationUseCaseEnum.getNameByValue(station.getStationUseCase());
    stationInfo.stationUser =station.getPersonName();
    stationInfo.stationUserPhone =station.getContact();
    stationInfo.stationAddress = station.getStationAddress();
    stationInfo.latitude = station.getLatitude();
    stationInfo.longitude = station.getLongitude();
  }

  @Data
  public static class StationInfo{

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 站点管理员
     */
    private String stationUser;

    /**
     * 站长手机号
     */
    private String stationUserPhone;

    /**
     * 站点用途
     */
    private String stationUseCase;

    /**
     * 站点地址
     */
    private String stationAddress;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;
  }
}
