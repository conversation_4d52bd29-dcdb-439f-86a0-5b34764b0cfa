/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.domain.jsonhandler;

import com.alibaba.fastjson2.TypeReference;
import com.jdx.rover.monitor.po.mapcollection.json.FourLaneInfo;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:28
 * @description 四车道信息映射泛型
 */
public class FourLaneListTypeHandler extends ListTypeHandler<FourLaneInfo> {

    @Override
    protected TypeReference<List<FourLaneInfo>> specificType() {
        return new TypeReference<>() {};
    }
}
