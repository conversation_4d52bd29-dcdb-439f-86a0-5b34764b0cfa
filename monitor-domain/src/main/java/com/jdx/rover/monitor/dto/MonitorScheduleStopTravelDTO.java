/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * 调度停靠点行驶信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleStopTravelDTO {

  /**
   * <p>
   * 停靠点名
   * </p>
   */
  private String stopName;

  /**
   * <p>
   * 停靠点类型
   * </p>
   */
  private String stopType;

  /**
   * <p>
   * 停靠点状态
   * </p>
   */
  private String travelStatus;

  /**
   * <p>
   * 等待时间
   * </p>
   */
  private Long waitingTime = 0L;

  /**
   * <p>
   * 行驶时间
   * </p>
   */
  private Long travelTime = 0L;

  /**
   * <p>
   * 规划里程
   * </p>
   */
  private Double routingMileage;

  /**
   * <p>
   * 行驶里程
   * </p>
   */
  private Double finishedMileage;


}
