/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/
package com.jdx.rover.monitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is the add entity for issue monitor operate history.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueRecordListRequestVO {

  /**
   * <p>
   * Represents the vehicle name of issue record.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the state of issue record.
   * </p>
   */
  private List<String> stateList;

  /**
   * <p>
   * Represents the alarm type of issue record.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the business type of vehicle.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the issue reported.
   * </p>
   */
  private Boolean isReported;

  /**
   * <p>
   * Represents whether alarm.
   * </p>
   */
  private Boolean isNeedAlarm;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date endTime;
}
