/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 机器人调度停靠点缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/06/10
 */
@Data
public class RobotTaskGoalDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 车号
   */
  private String deviceName;

  /**
   * 开始时间
   */
  private Date recordTime;

  /**
   * 任务Id号
   */
  private String taskId;

  /**
   * 停靠点Id号
   */
  private Integer stopId;

  /**
   * 开始时间
   */
  private Date startTime;


}
