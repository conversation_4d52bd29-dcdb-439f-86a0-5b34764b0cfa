/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 设备指令事件类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum DeviceAlarmProcessModeEnum {

    /**
     * 远程处理
     */
    REMOTE_PROCESS("REMOTE_PROCESS", "远程处理"),
    /**
     * 现场处理。
     */
    LOCAL_PROCESS("LOCAL_PROCESS", "现场处理"),
    /**
     * 自行恢复。
     */
    AUTO("AUTO", "自行恢复"),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * 根据给定的值获取对应的 DeviceAlarmProcessModeEnum 枚举类型。
     */
    public static DeviceAlarmProcessModeEnum getByValue(String value) {
        for (DeviceAlarmProcessModeEnum em : DeviceAlarmProcessModeEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}
