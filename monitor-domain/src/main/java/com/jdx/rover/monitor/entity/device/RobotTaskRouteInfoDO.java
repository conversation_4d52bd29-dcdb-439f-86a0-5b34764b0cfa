/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 机器人路径规划缓存
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/06/10
 */
@Data
public class RobotTaskRouteInfoDO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 地图信息
   */
  private String mapId;

  /**
   * 目标点
   */
  private Point goal;

  /**
   * 存储当前规划路径信息
   */
  private List<Point> routePoint;

  /**
   * 点位
   */
  @Data
  public static class Point {
    /**
     * x坐标值，用于表示坐标点的位置。
     */
    private double x;
    /**
     * y坐标值，用于表示坐标点的位置。
     */
    private double y;
    /**
     * yaw角，用于表示坐标点的位置。
     */
    private double yaw;
  }
}
