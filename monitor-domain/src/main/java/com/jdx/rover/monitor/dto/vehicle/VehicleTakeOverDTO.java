/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.util.Date;

/**
 * 车辆接管状态
 *
 * <AUTHOR>
 */
@Data
public class VehicleTakeOverDTO {
  /**
   * 车辆名称
   */
  private String vehicleName;

  /**
   * 接管用户名
   */
  private String takeOverUserName;

  /**
   * 接管方
   */
  private String takeOverSource;

  /**
   * 接管方式
   */
  private String takeOverStatus;

  /**
   * <p>
   * 座席编号
   * </p>
   */
  private String cockpitNumber;

  /**
   * <p>
   * 执行命令
   * </p>
   */
  private String operateType;

  /**
   * <p>
   * 执行命令时间
   * </p>
   */
  private Date reportTime;
}
