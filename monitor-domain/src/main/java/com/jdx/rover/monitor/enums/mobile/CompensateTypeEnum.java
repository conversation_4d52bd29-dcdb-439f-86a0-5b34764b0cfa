package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 是否需要赔偿枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum CompensateTypeEnum {

    YES(1, "需赔偿"),
    NO(0, "无需赔偿"),
    ;

    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (CompensateTypeEnum compensateTypeEnum : CompensateTypeEnum.values()) {
            if (compensateTypeEnum.getValue().equals(value)) {
                return compensateTypeEnum.getName();
            }
        }
        return null;
    }
}
