/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.po;

import com.jdx.rover.monitor.base.BaseDomain;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is an issue record entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class IssueRecord extends BaseDomain {
  /**
   * <p>
   * Represents the no of issue record.
   * </p>
   */
  private String issueNo;

  /**
   * <p>
   * Represents the type of alarm.
   * </p>
   */
  private String alarmType;

  /**
   * <p>
   * Represents the time of alarm.
   * </p>
   */
  private Date alarmTime;

  /**
   * <p>
   * Represents the name of vehicle.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the business type of vehicle.
   * </p>
   */
  private String vehicleBusinessType;

  /**
   * <p>
   * Represents the id of city.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the id of station.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the source of issue record.
   * </p>
   */
  private String source;

  /**
   * <p>
   * Represents the vehicle alarm event type.
   * </p>
   */
  private String state;

  /**
   * <p>
   * Represents the operate user.
   * </p>
   */
  private String operateUser;

  /**
   * <p>
   * Represents the report user.
   * </p>
   */
  private String reportUser;

  /**
   * <p>
   * Represents the result of issue record.
   * </p>
   */
  private String result;

  /**
   * <p>
   * Represents the start time of issue record.
   * </p>
   */
  private Date startTime;

  /**
   * <p>
   * Represents the end time of issue record.
   * </p>
   */
  private Date endTime;

  /**
   * <p>
   * Represents the report time of issue record.
   * </p>
   */
  private Date reportTime;

  /**
   * <p>
   * Represents the reported jira no of issue record.
   */
  private String jiraNo;

  /**
   * <p>
   * Represents the owner usecase of vehicle.
   */
  private String ownerUseCase;

  public IssueRecordDTO toIssueRecordDto() {
    IssueRecordDTO issueRecordDto = new IssueRecordDTO();
    issueRecordDto.setId(getId());
    issueRecordDto.setIssueNo(issueNo);
    issueRecordDto.setJiraNo(jiraNo);
    issueRecordDto.setAlarmType(alarmType);
    issueRecordDto.setAlarmTime(alarmTime);
    issueRecordDto.setOperateUser(operateUser);
    issueRecordDto.setReportTime(reportTime);
    issueRecordDto.setReportUser(reportUser);
    issueRecordDto.setResult(result);
    issueRecordDto.setSource(source);
    issueRecordDto.setCityName(cityName);
    issueRecordDto.setStationName(stationName);
    issueRecordDto.setStartTime(startTime);
    issueRecordDto.setEndTime(endTime);
    issueRecordDto.setState(state);
    issueRecordDto.setVehicleBussinessType(vehicleBusinessType);
    issueRecordDto.setVehicleName(vehicleName);
    return issueRecordDto;
  }

}
