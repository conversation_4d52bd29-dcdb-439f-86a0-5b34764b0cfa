/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.jira;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a jira custom field type enum.
 * </p>
 *
 * <p>
 * <strong>customField type: </strong> enumeration of the class defectStage type.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@AllArgsConstructor
@ToString
public enum JiraCustomFieldEnum {

  /**
   * <p>
   * The enumerate jira customField types.
   * </p>
   */
  FUNCTION_MODULE("customfield_11214"), DEFECT("customfield_11400"), SEVERITY("customfield_11401"),DEFECT_INTRODUCTION_STAGE("customfield_11403"),
  DEFECTSTAGE("customfield_11404"), COVERAGETEST("customfield_11405"),DEFECT_CAUSE("customfield_11406"), VERSION("customfield_10412");

  /**
   * <p>
   * The customField type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String customField;
}
