/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a issue process data type enum.
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum IssueProcessDataEnum {
  /**
   * <p>
   * The enumerate issue process type.
   * </p>
   */
  ALARM("alarm", "车辆告警"),
  OPERATION("operation", "车辆操作");

  /**
   * <p>
   * The meta data request type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String type;

  /**
   * <p>
   * The meta data request name corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private String name;
}
