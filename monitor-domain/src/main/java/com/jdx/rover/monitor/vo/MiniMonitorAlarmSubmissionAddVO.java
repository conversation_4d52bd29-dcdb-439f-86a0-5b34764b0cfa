/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * This is a view object for mini monitor station info identity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorAlarmSubmissionAddVO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * Represents the title of alarm. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String title;

  /**
   * <p>
   * Represents the message of alarm. The default value is null. It's changeable.
   * </p>
   */
  @NotBlank
  private String message;

  /**
   * <p>
   * Represents the id list of linked mini monitor alarm. The default value is null. It's
   * changeable.
   * </p>
   */
  private List<MiniMonitorAlarmAttachmentAddVO> attachment;

}
