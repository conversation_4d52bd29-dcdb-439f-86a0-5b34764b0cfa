/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * 获取初始化点云地图帧数据
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorGetCloudMapVO {

  /**
   * <p>
   * 车号
   * </p>
   */
  @NotBlank
  private String vehicleName;

  /**
   * <p>
   * 请求范围
   * </p>
   */
  private Double distance;

}
