/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * This is a schedule data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorScheduleDTO {
  /**
   * <p>
   * Represents the schedule's no. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleNo;

  /**
   * <p>
   * Represents the vehicle's schedule state. The default value is null. It's changeable.
   * </p>
   */
  private String scheduleState = VehicleScheduleState.WAITING.getVehicleScheduleState();

  /**
   * <p>
   * Represents the schedule task type. The default value is null. It's changeable.
   * </p>
   */
  private String taskType;

  /**
   * <p>
   * The start time of the schedule.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date startDateTime;

  /**
   * <p>
   * Represent the global mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double globalMileage = 0.0;

  /**
   * <p>
   * Represent the finished mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double finishedMileage = 0.0;

  /**
   * <p>
   * Represent the total finished mileage of schedule. The default value is null. It's changeable.
   * </p>
   */
  private Double totalFinishedMileage = 0.0;

  /**
   * <p>
   * Represents the stop list of schedule. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorScheduleStopDTO> stop;

}
