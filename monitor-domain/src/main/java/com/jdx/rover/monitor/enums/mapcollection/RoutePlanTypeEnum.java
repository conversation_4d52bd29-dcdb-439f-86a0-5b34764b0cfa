/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/18 16:32
 * @description 线路方案
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum RoutePlanTypeEnum {
    MOTOR_VEHICLE("MOTOR_VEHICLE", "机动车"),
    NON_MOTOR_VEHICLE("NON_MOTOR_VEHICLE", "非机动车"),
    UNKNOWN("UNKNOWN", "未知");


    /**
     * code
     */
    private final String code;

    /**
     * name
     */
    private final String name;

    /**
     * 根据routePlanType获取枚举
     *
     * @param routePlanType routePlanType
     * @return RoutePlanTypeEnum
     */
    public static RoutePlanTypeEnum of(String routePlanType) {
        for (RoutePlanTypeEnum em : RoutePlanTypeEnum.values()) {
            if (em.getCode().equals(routePlanType)) {
                return em;
            }
        }
        return UNKNOWN;
    }
}
