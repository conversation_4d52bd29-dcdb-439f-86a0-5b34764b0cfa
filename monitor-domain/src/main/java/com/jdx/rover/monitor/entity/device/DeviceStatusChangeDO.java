/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.entity.device;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 待更新设备DO
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
public class DeviceStatusChangeDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备号
     */
    private String deviceName;

    /**
     * guardian在线
     */
    private String productKey;

}
