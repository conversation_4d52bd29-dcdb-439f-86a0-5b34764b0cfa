/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.minimonitor;

import lombok.Data;

/**
 * <p>
 * This is a station stop data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MiniMonitorStationStopDTO {

  /**
   * <p>
   * Represents the stop id. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the stop name. The default value is null. It's changeable.
   * </p>
   */
  private String name;


}
