/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto.tnta;

import com.jdx.rover.monitor.dto.MonitorImpassableAreaDTO;
import com.jdx.rover.monitor.enums.ImpassableAreaEffectStateEnum;
import com.jdx.rover.monitor.enums.ImpassableAreaEffectTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <p>
 * This is a impassable area item data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data

public class ImpassableAreaItemDTO {
  /**
   * <p>
   * ID
   * </p>
   */
  private Integer objid;

  /**
   * <p>
   * 时间
   * </p>
   */
  private Integer duration;

  /**
   * <p>
   * 有效时间
   * </p>
   */
  private String durationName;

  /**
   * <p>
   * 类型 1 2 3
   * </p>
   */
  private Integer type;

  /**
   * <p>
   * 状态
   * </p>
   */
  private Integer status;

  /**
   * <p>
   * 状态名
   * </p>
   */
  private String statusName;

  /**
   * <p>
   * 坐标
   * </p>
   */
  private String coords;

  /**
   * <p>
   * 坐标类型
   * </p>
   */
  private Integer coordType;

  /**
   * <p>
   * 坐标类型名
   * </p>
   */
  private String coordTypeName;

  /**
   * <p>
   * 返回数据
   * </p>
   */
  private String utm;

  /**
   * <p>
   * 操作用户
   * </p>
   */
  private String operator;

  /**
   * <p>
   * 生效类别（永久性、周期性）
   * </p>
   */
  private Integer effectDate;

  /**
   * <p>
   * 启停用状态
   * </p>
   */
  private Integer effectStatus;

  /**
   * <p>
   * 周期时长
   * </p>
   */
  private String timeLimit;

  /**
   * <p>
   * 备注
   * </p>
   */
  private String remark;

  /**
   * <p>
   * 操作时间
   * </p>
   */
  private String timestamp;

  public MonitorImpassableAreaDTO convertMonitorDto() {
    MonitorImpassableAreaDTO dto = new MonitorImpassableAreaDTO();
    dto.setDuration(this.duration);
    dto.setDurationName(this.durationName);
    dto.setId(this.objid);
    dto.setType(this.type);
    dto.setCoordStatus(this.status);
    dto.setCoordType(this.coordType);
    dto.setCoordStatusName(this.statusName);
    dto.setCoords(this.coords);
    dto.setCoordTypeName(this.coordTypeName);
    dto.setUtm(this.utm);
    dto.setUserName(this.operator);
    dto.setTimestamp(this.timestamp);
    dto.setRemark(this.remark);
    if (this.effectStatus != null) {
      dto.setEffectState(this.effectStatus);
    } else {
      dto.setEffectState(ImpassableAreaEffectStateEnum.ENABLE.getValue());
    }
    if (this.effectDate != null) {
      dto.setEffectType(this.effectDate);
    } else {
      dto.setEffectType(ImpassableAreaEffectTypeEnum.PERMANENT.getValue());
    }
    if (StringUtils.isNotBlank(this.timeLimit)) {
      dto.setTimeLimit(Arrays.asList(this.timeLimit.split("\\|")));
    }
    return dto;
  }

}
