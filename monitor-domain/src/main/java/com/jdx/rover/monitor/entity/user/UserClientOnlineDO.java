package com.jdx.rover.monitor.entity.user;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户坐席模式DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6
 */
@Data
public class UserClientOnlineDO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 客户端名称
     */
    private String clientName;

    /**
     * 在线开始时间
     */
    private Date startTime;

    /**
     * 记录时间
     */
    private Date recordTime;
}
