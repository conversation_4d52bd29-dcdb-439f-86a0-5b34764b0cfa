package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 运营事故责任枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentOperationJudgeEnum {

    FULL_LIABILITY("FULL_LIABILITY", "无人车全责"),
    NO_LIABILITY("NO_LIABILITY", "无人车无责"),
    SHARED_LIABILITY("SHARED_LIABILITY", "多方责任");

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (AccidentOperationJudgeEnum accidentOperationJudgeEnum : AccidentOperationJudgeEnum.values()) {
            if (accidentOperationJudgeEnum.getValue().equals(value)) {
                return accidentOperationJudgeEnum.getName();
            }
        }
        return null;
    }
}
