package com.jdx.rover.monitor.dto.mobile.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class TodoListDTO {

    /**
     * id
     */
    private Integer messageId;

    /**
     * 车牌号
     */
    private String vehicleName;

    /**
     * 模块
     */
    private String module;

    /**
     * 类型
     */
    private String type;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 推送时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pushTime;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 描述
     */
    private String description;

    /**
     * 额外属性
     */
    private Map<String, Object> attributes;
}
