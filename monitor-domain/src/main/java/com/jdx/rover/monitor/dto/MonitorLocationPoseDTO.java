/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 车辆上报定位位姿和点云信息
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorLocationPoseDTO {

  /**
   * <p>
   * 指令id
   * </p>
   */
  private Long id;

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 可选位姿列表
   * </p>
   */
  private List<MonitorVehicleLocationPoseDTO> poseList;

  /**
   * <p>
   * 车端激光点云地图
   * </p>
   */
  private String cloudPath;

  /**
   * <p>
   * 车端点云图片
   * </p>
   */
  private String photoPath;

  /**
   * <p>
   * 瓦片列表大小
   * </p>
   */
  private Integer tileSize;


}
