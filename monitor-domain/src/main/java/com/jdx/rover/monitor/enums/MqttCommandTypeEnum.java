/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * mqtt下发指令类型
 *
 * <AUTHOR>
 * @date 2024/12/12
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MqttCommandTypeEnum {
  /**
   * 表示Mqtt下发指令类型中的"切换视频质量"命令。
   */
  CHANGE_VIDEO_QUALITY("onImageQualityChange", "切换视频质量"),
  /**
   * 表示Mqtt下发指令类型中的"切换视频质量"命令。
   */
  CMD_REMOTE_COMMAND_REPLY("CMD_REMOTE_COMMAND_REPLY", "遥控指令响应"),
  ;

  /**
   * 值
   */
  private String value;

  /**
   * 名称
   */
  private String name;
}
