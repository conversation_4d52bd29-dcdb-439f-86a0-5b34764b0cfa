/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * This is a enumerate class of coordinate type.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum CoordinateZoneEnum {
  /**
   * <p>
   * The enumerate zone type.
   * </p>
   */
  XCS(0), WGS84(1), UTM(2);

  /**
   * <p>
   * The zone type corresponding to the enumeration.
   * </p>
   */
  @Getter
  @Setter
  private Integer coordinateZoneType;

}
