/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:33
 * @description 暂存勘查线路
 */
@Data
public class TaskRouteSaveVO implements Serializable {

    /**
     * 勘查任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;

    /**
     * 勘查线路总里程
     */
    @NotNull(message = "线路总里程不能为空")
    private Double totalMileage;

    /**
     * 线路颜色
     */
    private String taskRouteColor;

    /**
     * 点位列表
     */
    @NotNull(message = "点位列表不能为空")
    private List<PositionVO> taskRouteList;

    /**
     * 路线名称列表
     */
    private List<String> roadNameList;

    /**
     * 四车道列表
     */
    private List<FourLaneVO> fourLaneList;

    @Data
    public static class FourLaneVO implements Serializable {

        /**
         * 四车道起点名称
         */
        private String startAddress;

        /**
         * 四车道终点名称
         */
        private String endAddress;

        /**
         * 四车道点位对应taskRouteList下标
         */
        private List<Integer> routeIndexList;
    }
}
