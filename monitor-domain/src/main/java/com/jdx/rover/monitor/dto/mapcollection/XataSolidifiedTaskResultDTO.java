/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.dto.mapcollection;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/31 16:24
 * @description 查询Xata车辆固化任务结果
 */
@Data
public class XataSolidifiedTaskResultDTO {

    /**
     * Code
     */
    private Integer code;

    /**
     * Data
     */
    private List<VehicleSolidifiedTask> data;

    @Data
    public static class VehicleSolidifiedTask {

        /**
         * 任务ID
         */
        private Integer id;

        /**
         * 任务状态
         */
        private String status;

        /**
         * 任务描述
         */
        private String description;

        /**
         * 任务创建时间
         */
        private String createTime;
    }
}
