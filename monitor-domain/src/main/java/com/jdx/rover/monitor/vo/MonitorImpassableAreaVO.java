/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.vo;

import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * This is a view objects for impassable area.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorImpassableAreaVO {

  /**
   * <p>
   * Represents the id of impassable area. The default value is null. It's
   * changeable.
   * </p>
   */
  @NotNull
  private Integer id;

  /**
   * <p>
   * Represents the userName of the operation. The default value is null. It's
   * changeable.
   * </p>
   */
  private String userName;

}
