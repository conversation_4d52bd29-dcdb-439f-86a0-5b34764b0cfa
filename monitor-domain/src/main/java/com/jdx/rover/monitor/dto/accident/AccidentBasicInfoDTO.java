package com.jdx.rover.monitor.dto.accident;

import lombok.Data;

import java.util.Date;

@Data
public class AccidentBasicInfoDTO {

    /**
     * 事故编号
     */
    private String accidentNo;

    /**
     * 影子事件id
     */
    private Integer shadowEventId;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 创建人
     */
    private String reportUser;

    /**
     * 创建时间
     */
    private Date accidentReportTime;

    /**
     * 事故描述
     */
    private String accidentDesc;

    /**
     * rover版本
     */
    private String roverVersion;

    /**
     * bug编号
     */
    private String bugCode;

    /**
     * bug标题
     */
    private String bugTitle;

    /**
     * 事故类型
     */
    private String accidentType;

    /**
     * 事故等级
     */
    private String accidentLevel;
}
