/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 车辆当前周边点云地图
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorMapTileDTO {

  /**
   * <p>
   * 请求id
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * 车号
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * 地图id
   * </p>
   */
  private Integer mapId;

  /**
   * <p>
   * 地图版本
   * </p>
   */
  private Integer mapVersion;

  /**
   * <p>
   * 瓦片版本
   * </p>
   */
  private Integer tileVersion;

  /**
   * <p>
   * 瓦片列表
   * </p>
   */
  private List<TileInfoDTO> tileList;

  /**
   * 响应数据列表
   */
  @Data
  @NoArgsConstructor
  public static class TileInfoDTO {
    /**
     * 瓦片ID
     */
    private Long tileId;

    /**
     * 下载链接地址
     */
    private String url;

    /**
     * 瓦片类型
     */
    private String tileType;

    /**
     * 压缩类型
     */
    private String compressType;
  }

}
