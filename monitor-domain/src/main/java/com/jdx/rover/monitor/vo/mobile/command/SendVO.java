package com.jdx.rover.monitor.vo.mobile.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * @description: SendVO
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-07-18 13:25
 **/
@Data
public class SendVO {

    /**
     * 车号
     */
    @NotBlank(message = "车号不能为空")
    private String vehicleName;

    /**
     * 指令类型
     */
    @NotBlank(message = "指令类型不能为空")
    private String commandType;

    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空")
    private Date operateTime;
}