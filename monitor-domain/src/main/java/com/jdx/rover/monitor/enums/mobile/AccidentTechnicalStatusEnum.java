package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 事故标签枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentTechnicalStatusEnum {


    YES(1, "已处理"),
    NO(0, "未处理"),
    ;


    /**
     * <p>
     * 值
     * </p>
     */
    private final Integer value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;


    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(Integer value) {
        for (AccidentTechnicalStatusEnum accidentTechnicalStatusEnum : AccidentTechnicalStatusEnum.values()) {
            if (accidentTechnicalStatusEnum.getValue().equals(value)) {
                return accidentTechnicalStatusEnum.getName();
            }
        }
        return null;
    }
}
