package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum AccidentLogEnum {

    VEHICLE_REPORT("上报碰撞", "", "VEHICLE_REPORT"),
    MANUAL_REPORT("人工创建事故", "%s于%s人工核实发生事故-%s-%s", "MANUAL_REPORT"),
    OPERATION_ACCEPT("生成事故单", "运营端操作\"生成事故单\"-%s-%s", "OPERATION_ACCEPT"),
    OPERATION_REJECT("无需处理", "运营端操作\"无需处理\"-%s-%s", "OPERATION_REJECT"),
    OPERATION_SAVE("生成事故单并暂存", "运营端操作\"生成事故单并暂存\"-%s-%s", "OPERATION_SAVE"),
    OPERATION_SUBMIT("生成事故单并提交", "运营端操作\"生成事故单并提交\"-%s-%s", "OPERATION_SUBMIT"),
    TECHNICAL_SUPPORT_EDIT("远程安全员编辑", "远程安全员操作\"保存\"-%s-%s", "TECHNICAL_SUPPORT_EDIT"),
    SAFETY_GROUP_EDIT("安全组编辑", "安全组操作\"保存\"-%s-%s", "SAFETY_GROUP_EDIT"),
    ;

    private final String title;

    private final String content;

    private final String value;
}
