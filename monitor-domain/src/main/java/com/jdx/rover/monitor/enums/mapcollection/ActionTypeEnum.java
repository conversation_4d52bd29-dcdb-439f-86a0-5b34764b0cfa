package com.jdx.rover.monitor.enums.mapcollection;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 操作类型枚举
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-12-18 18:04
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum ActionTypeEnum {

    OPEN(1, "开启/开始"),
    CLOSE(0, "关闭/暂停"),
    ;

    /**
     * 操作类型
     */
    private final Integer actionType;

    /**
     * 操作类型名称
     */
    private final String actionTypeName;

    /**
     * 获取操作类型枚举
     *
     * @param actionType actionType
     * @return ActionTypeEnum
     */
    public static ActionTypeEnum of(Integer actionType) {
        for (ActionTypeEnum actionTypeEnum : ActionTypeEnum.values()) {
            if (actionTypeEnum.getActionType().equals(actionType)) {
                return actionTypeEnum;
            }
        }
        return null;
    }
}