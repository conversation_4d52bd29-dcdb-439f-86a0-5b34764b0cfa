/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * 车辆实时四路使能
 * entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorVehicleEnvEnableDTO {

  /**
   * <p>
   * Represents whether the front of the vehicle is able to go. The default value
   * is false. It's changeable.
   * </p>
   */
  private Boolean front;

  /**
   * <p>
   * Represents whether the back of the vehicle is able to go. The default value
   * is false. It's changeable.
   * </p>
   */
  private Boolean back;

  /**
   * <p>
   * Represents whether the left of the vehicle is able to go. The default value
   * is false. It's changeable.
   * </p>
   */
  private Boolean left;

  /**
   * <p>
   * Represents whether the front of the vehicle is able to go. The default value
   * is false. It's right.
   * </p>
   */
  private Boolean right;

}
