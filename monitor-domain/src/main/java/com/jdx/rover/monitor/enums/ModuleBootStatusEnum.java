/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 模块启动状态
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum ModuleBootStatusEnum {
  /**
   * <p>
   * The enumerate alarm event type and solution type of vehicle.
   * </p>
   */
  WAIT_START("WAIT_START", "等待启动"),
  STARTING("STARTING", "启动中"),
  START_FAILED("START_FAILED","启动失败"),
  START_SUCCESS("START_SUCCESS","启动成功"),
  START_TIMEOUT("START_TIMEOUT","启动超时"),
  SHUTTING_DOWN("SHUTTING_DOWN","关机中"),
  SHUT_DOWN_SUCCESS("SHUT_DOWN_SUCCESS","关机成功");

  /**
   * <p>
   * 启动状态.
   * </p>
   */
  private String bootStatus;

  /**
   * <p>
   * 启动状态名.
   * </p>
   */
  private String bootStatusName;

  public static ModuleBootStatusEnum of(String bootStatus) {
    for (ModuleBootStatusEnum em : ModuleBootStatusEnum.values()) {
      if (Objects.equals(bootStatus, em.getBootStatus())) {
        return em;
      }
    }
    return null;
  }
}
