/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.po.mapcollection.json;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/10 15:27
 * @description 四车道信息
 */
@Data
public class FourLaneInfo {

    /**
     * 四车道起点名称
     */
    private String startAddress;

    /**
     * 四车道终点名称
     */
    private String endAddress;

    /**
     * 四车道点位对应taskRoute下标
     */
    private List<Integer> routeIndexList;
}
