/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.vo.mapcollection;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/12 20:24
 * @description 创建勘查任务
 */
@Data
public class TaskCreateVO implements Serializable {

    /**
     * 勘查任务名称
     */
    @NotBlank(message = "勘查任务名称不能为空")
    @Size(min = 1, max = 20, message = "字数限制20字以内")
    private String taskName;

    /**
     * 所属城市ID
     */
    private Integer cityId;

    /**
     * 所属站点ID
     */
    @NotNull(message = "站点ID不能为空")
    private Integer stationId;
}
