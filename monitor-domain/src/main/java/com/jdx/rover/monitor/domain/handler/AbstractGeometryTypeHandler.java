package com.jdx.rover.monitor.domain.handler;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.PGgeometry;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * @description: Geometry字段和MyBatis类型转换器
 * @author: wangguotai
 * @create: 2024-05-24 09:25
 **/
@Slf4j
public abstract class AbstractGeometryTypeHandler<T extends Geometry> extends BaseTypeHandler<T> {
    /**
     * WKTReader非线程安全
     */
    private static final ThreadLocal<WKTReader> READER_POOL = ThreadLocal.withInitial(WKTReader::new);
    /**
     * WKTWriter非线程安全
     */
    private static final ThreadLocal<WKTWriter> WRITER_POOL = ThreadLocal.withInitial(WKTWriter::new);
    /**
     * 与数据库中几何列的空间坐标系保持一致，要不然写入会报错
     */
    private static final int SRID_IN_DB = 4326;

    /**
     * 将几何类型转换为PG geometry，并设置空间坐标系。
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        PGgeometry pGgeometry = new PGgeometry(WRITER_POOL.get().write(parameter));
        net.postgis.jdbc.geometry.Geometry geometry = pGgeometry.getGeometry();
        geometry.setSrid(SRID_IN_DB);
        ps.setObject(i, pGgeometry);
    }

    /**
     * 获取结果集中的几何列，与getNullableResult(CallableStatement cs, int columnIndex)方法类似。
     */
    @SneakyThrows
    @Override
    public T getNullableResult(ResultSet rs, String columnName) {
        Object object = rs.getObject(columnName);
        if (Objects.isNull(object)) {
            return null;
        }
        if (object instanceof PGgeometry) {
            return getResult((PGgeometry) object);
        }
        log.error("获取结果集ResultSet中的几何列失败，columnName：{}", columnName);
        return null;
    }

    /**
     * 获取结果集中的几何列，与getNullableResult(ResultSet rs, String columnName)方法类似。
     */
    @SneakyThrows
    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) {
        Object object = rs.getObject(columnIndex);
        if (object instanceof PGgeometry) {
            return getResult((PGgeometry) object);
        }
        log.error("获取结果集ResultSet中的几何列失败，columnIndex：{}", columnIndex);
        return null;
    }

    /**
     * 获取存储过程的返回值，与getNullableResult(ResultSet rs, int columnIndex)方法类似。
     */
    @SneakyThrows
    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) {
        Object object = cs.getObject(columnIndex);
        if (object instanceof PGgeometry) {
            return getResult((PGgeometry) object);
        }
        log.error("获取结果集CallableStatement中的几何列失败，columnIndex：{}", columnIndex);
        return null;
    }

    /**
     * 将PG geometry转换为具体的Geometry类型
     */
    private T getResult(PGgeometry pGgeometry) {
        if (pGgeometry == null) {
            return null;
        }
        String pgWkt = pGgeometry.toString();
        String target = String.format("SRID=%s;", SRID_IN_DB);
        String wkt = pgWkt.replace(target, "");
        try {
            return (T) READER_POOL.get().read(wkt);
        } catch (Exception e) {
            throw new RuntimeException("解析wkt失败：" + wkt, e);
        }
    }
}