/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums.device;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <p>
 * 设备工作模式
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DeviceWorkModeEnum {

  /**
   * 表示系统模式
   */
  SYS("sys", "系统模式"),
  /**
   * 表示单机模式
   */
  SA("sa", "单机模式"),
  /**
   * 表示设备停用
   */
  DISABLE("disable", "停用"),
  ;

  /**
   * <p>
   * 模式类型.
   * </p>
   */
  private String workModeType;

  /**
   * <p>
   * 模式名称.
   * </p>
   */
  private String workModeName;

  public static DeviceWorkModeEnum of(String workModeType) {
    for (DeviceWorkModeEnum em : DeviceWorkModeEnum.values()) {
      if (Objects.equals(workModeType, em.getWorkModeType())) {
        return em;
      }
    }
    return null;
  }
}
