/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import com.jd.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * This is a monitor operation record data transform object entity.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorOperationRecordDTO {

  /**
   * <p>
   * Represents the id of record data.The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the operate time of record data. The default value is null. It's changeable.
   * </p>
   */
  @JSONField(format = "yyyy-MM-dd HH:mm:ss")
  private Date operateTime;

  /**
   * <p>
   * Represents the city name of record data. The default value is null. It's changeable.
   * </p>
   */
  private String cityName;

  /**
   * <p>
   * Represents the station name of record data. The default value is null. It's changeable.
   * </p>
   */
  private String stationName;

  /**
   * <p>
   * Represents the vehicle name of record data. The default value is null. It's changeable.
   * </p>
   */
  private String vehicleName;

  /**
   * <p>
   * Represents the operate user name of record data. The default value is null. It's changeable.
   * </p>
   */
  private String operateUserName;

  /**
   * <p>
   * Represents the operate info of record data. The default value is null. It's changeable.
   * </p>
   */
  private String operateInfo;

  /**
   * 操作状态(INIT,RUNNING,SUCCESS,FAIL)
   */
  private String state;

  /**
   * 操作失败消息
   */
  private String failMessage;
}
