package com.jdx.rover.monitor.enums.mobile;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 定责事故等级枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
@Deprecated
public enum SafetyGroupAccidentLevelEnum {

    NO_FAULT("NO_FAULT", "无责"),
    LOW_RISK("LOW_RISK", "低风险"),
    MEDIUM_RISK("MEDIUM_RISK", "中风险"),
    HIGH_RISK("HIGH_RISK", "高风险");

    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String name;

    /**
     * <p>
     * 获取名称
     * </p>
     */
    public static String getNameByValue(String value) {
        for (SafetyGroupAccidentLevelEnum safetyGroupAccidentLevelEnum : SafetyGroupAccidentLevelEnum.values()) {
            if (safetyGroupAccidentLevelEnum.getValue().equals(value)) {
                return safetyGroupAccidentLevelEnum.getName();
            }
        }
        return null;
    }
}
