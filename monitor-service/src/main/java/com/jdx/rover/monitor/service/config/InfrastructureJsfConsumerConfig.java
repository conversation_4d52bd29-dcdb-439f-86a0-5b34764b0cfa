/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.config;

import com.jdx.rover.infrastructure.jsf.service.HttpProxyService;
import com.jdx.rover.infrastructure.jsf.service.InfraNoticeJsfService;
import com.jdx.rover.infrastructure.jsf.service.InfraXingYunJsfService;
import com.jdx.rover.jsf.consumer.JsfConsumerRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * 公共服务jsf配置
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
@Slf4j
@Configuration
@Component
public class InfrastructureJsfConsumerConfig {

    @Autowired
    private JsfConsumerRegister jsfConsumerRegister;

    /**
     * 注册 InfraXingYunJsfService
     */
    @Bean
    public InfraXingYunJsfService infraXingYunJsfService() {
        return jsfConsumerRegister.createConsumerConfig(InfraXingYunJsfService.class).refer();
    }

    /**
     * 注册 InfraNoticeJsfService
     */
    @Bean
    public InfraNoticeJsfService infraNoticeJsfService() {
        return jsfConsumerRegister.createConsumerConfig(InfraNoticeJsfService.class).refer();
    }

    /**
     * 注册 HttpProxyService
     */
    @Bean
    public HttpProxyService httpProxyService() {
        return jsfConsumerRegister.createConsumerConfig(HttpProxyService.class).refer();
    }

}