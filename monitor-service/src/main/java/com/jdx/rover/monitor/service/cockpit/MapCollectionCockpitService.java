package com.jdx.rover.monitor.service.cockpit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CoordinateUtil;
import cn.hutool.core.util.CoordinateUtil.Coordinate;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.exception.BusinessException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.metadata.domain.dto.station.CityStationInfoDTO;
import com.jdx.rover.monitor.common.utils.param.ParamMap;
import com.jdx.rover.monitor.constant.MapCollectConstant;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.mapcollection.FuzzySearchVehicleDTO;
import com.jdx.rover.monitor.dto.mapcollection.GetRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.GetRouteDTO.RoutePointDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapVehiclePageDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MultiVideoUrlDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.VehicleSolidifiedTaskDTO;
import com.jdx.rover.monitor.entity.MapTaskDO;
import com.jdx.rover.monitor.manager.delay.DelayJob;
import com.jdx.rover.monitor.manager.delay.DelayJobProducer;
import com.jdx.rover.monitor.manager.station.MetadataStationApiManager;
import com.jdx.rover.monitor.vo.mapcollection.FinishCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.AssociateTaskVO;
import com.jdx.rover.monitor.vo.mapcollection.FuzzySearchVehicleVO;
import com.jdx.rover.monitor.vo.mapcollection.GetRouteVO;
import com.jdx.rover.monitor.vo.mapcollection.MapVehiclePageVO;
import com.jdx.rover.monitor.vo.mapcollection.MultiVideoUrlVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskOperateVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchModeVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.monitor.bo.video.MultiVideoBO;
import com.jdx.rover.monitor.dataobject.mapcollection.MonitorMapCollectionTakeoverDTO;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleDistanceDO;
import com.jdx.rover.monitor.dataobject.mapcollection.VehiclePointDO;
import com.jdx.rover.monitor.dto.vehicle.VehicleBasicDTO;
import com.jdx.rover.monitor.dto.xata.DataCopyTaskDTO;
import com.jdx.rover.monitor.dto.xata.DataCopyTaskItemDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.VehicleAlarmDO;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mapcollection.ActionTypeEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionStatusEnum;
import com.jdx.rover.monitor.enums.mapcollection.TaskStatusEnum;
import com.jdx.rover.monitor.enums.mobile.SystemStatusEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.manager.config.DuccMonitorProperties;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskManager;
import com.jdx.rover.monitor.manager.mapcollection.MapCollectionTaskTimeRecordManager;
import com.jdx.rover.monitor.manager.video.VideoManager;
import com.jdx.rover.monitor.manager.xata.XataManager;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTask;
import com.jdx.rover.monitor.po.mapcollection.json.RealRoutePoint;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTaskTimeRecord;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.manager.vehicle.VehicleStatusManager;
import com.jdx.rover.monitor.repository.redis.VehicleAlarmRepository;
import com.jdx.rover.monitor.repository.redis.VehicleBasicRepository;
import com.jdx.rover.monitor.repository.redis.VehicleMapDistanceRepository;
import com.jdx.rover.monitor.repository.redis.VehicleMapRealRouteRepository;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.mapcollection.MapMarkService;
import com.jdx.rover.monitor.service.mapcollection.MapTaskService;
import com.jdx.rover.server.api.domain.enums.RemoteCommandTypeEnum;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import com.jdx.rover.monitor.repository.redis.VehicleRealtimeRepository;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.schedule.api.domain.enums.VehicleScheduleState;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeInfoDTO;
import com.jdx.rover.server.api.domain.enums.guardian.SystemStateEnum;

import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 采图模式相关service
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MapCollectionCockpitService {

    private final VehicleBasicRepository vehicleBasicRepository;

    private final DuccMonitorProperties duccMonitorProperties;

    private final VehicleAlarmRepository vehicleAlarmRepository;

    private final VehicleRealtimeRepository vehicleRealtimeRepository;

    private final VehicleStatusRepository vehicleStatusRepository;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final RemoteCommandService remoteCommandService;

    private final MapCollectionTaskTimeRecordManager mapCollectionTaskTimeRecordManager;

    private final MapCollectionTaskManager mapCollectionTaskManager;

    private final VehicleMapRealRouteRepository vehicleMapRealRouteRepository;

    private final VehicleMapDistanceRepository vehicleMapDistanceRepository;

    private final VehicleScheduleRepository vehicleScheduleRepository;

    private final MapTaskService mapTaskService;

    private final MapMarkService mapMarkService;

    private final XataManager xataManager;

    private final VideoManager videoManager;

    private final JmqProducerService jmqProducerService;

    private final MetadataStationApiManager metadataStationApiManager;

    /**
     * 采图多车页分页查询接口
     *
     * @param mapVehiclePageVO mapVehiclePageVO
     * @return PageDTO<MapVehiclePageDTO>
     */
    public PageDTO<MapVehiclePageDTO> getVehiclePage(MapVehiclePageVO mapVehiclePageVO) {
        List<String> vehicleNameList = duccMonitorProperties.getVehicleNameList();
        PageDTO<MapVehiclePageDTO> result = new PageDTO<>();
        result.setPageNum(mapVehiclePageVO.getPageNum());
        result.setPageSize(mapVehiclePageVO.getPageSize());
        setPagesAndTotal(result, vehicleNameList);
        List<String> vehicleNamePageList = getVehicleNamePageList(result, vehicleNameList);
        List<MapVehiclePageDTO> list = getMultiVehicleList(vehicleNamePageList);
        result.setList(list);
        return result;
    }

    /**
     * 设置分页的总数,总页数
     */
    private void setPagesAndTotal(PageDTO<MapVehiclePageDTO> pageDTO, List<String> vehicleNameList) {
        Long pageTotal = (long) vehicleNameList.size();
        pageDTO.setTotal(pageTotal);
        double pagesDouble = Math.ceil(pageDTO.getTotal().doubleValue() / pageDTO.getPageSize());
        Integer pages = (int) pagesDouble;
        pageDTO.setPages(pages);
    }

    /**
     * 设置分页数据
     */
    private List<String> getVehicleNamePageList(PageDTO<MapVehiclePageDTO> pageDTO, List<String> vehicleNameList) {
        int startIndex = (pageDTO.getPageNum() - 1) * pageDTO.getPageSize();
        if (startIndex < 0) {
            startIndex = 0;
        }
        int endIndex = startIndex + pageDTO.getPageSize();
        if (endIndex > vehicleNameList.size()) {
            endIndex = vehicleNameList.size();
        }
        List<String> vehicleNamePageList = new ArrayList<>();
        if (endIndex > startIndex) {
            vehicleNamePageList.addAll(vehicleNameList.subList(startIndex, endIndex));
        }
        return vehicleNamePageList;
    }

    /**
     * 设置分页数据
     */
    private List<MapVehiclePageDTO> getMultiVehicleList(List<String> vehicleNameList) {
        List<MapVehiclePageDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return list;
        }
        Map<String, VehicleRealtimeInfoDTO> realtimeMap = vehicleRealtimeRepository.listMap(vehicleNameList);
        Map<String, VehicleAlarmDO> alarmMap = vehicleAlarmRepository.listMap(vehicleNameList);
        Map<String, VehicleBasicDTO> vehicleMonitorBasicDtoMap = vehicleBasicRepository.listMap(vehicleNameList);
        Map<String, VehicleStatusDO> vehicleStatusMap = vehicleStatusRepository.listMap(vehicleNameList);
        Map<String, VehicleTakeOverEntity> vehicleTakeOverMap = vehicleTakeOverRepository.listMap(vehicleNameList);
        Map<String, VehicleDistanceDO> vehicleDistanceDOMap = vehicleMapDistanceRepository.listMap(vehicleNameList);
        Map<String, MonitorScheduleEntity> monitorScheduleEntityMap = vehicleScheduleRepository.listMap(vehicleNameList);
        for (String vehicleName : vehicleNameList) {
            VehicleStatusDO vehicleStatus = vehicleStatusMap.get(vehicleName);
            MapVehiclePageDTO vehicle = new MapVehiclePageDTO();
            vehicle.setVehicleName(vehicleName);
            vehicle.setSystemState(SystemStateEnum.OFFLINE.getSystemState());
            VehicleBasicDTO vehicleMonitorBasicDto = vehicleMonitorBasicDtoMap.get(vehicleName);
            if (!Objects.isNull(vehicleMonitorBasicDto)) {
                vehicle.setStationName(vehicleMonitorBasicDto.getStationName());
            }
            VehicleRealtimeInfoDTO realtime = realtimeMap.get(vehicleName);
            if (!Objects.isNull(realtime)) {
                vehicle.setPower(Optional.ofNullable(realtime.getPower()).orElse(0.0));
                String systemState = realtime.getSystemState();
                vehicle.setSystemState(systemState);
                if (VehicleStatusManager.isGuardianOnline(vehicleStatus)) {
                    vehicle.setVehicleState(realtime.getVehicleState());
                    vehicle.setSpeed(Optional.ofNullable(realtime.getSpeed()).orElse(0.0).floatValue());
                    VehicleAlarmDO alarm = alarmMap.get(vehicleName);
                    if (alarm != null && CollectionUtils.isNotEmpty(alarm.getAlarmEventList())) {
                        List<MapVehiclePageDTO.AlarmEvent> alarmEventList = alarm.getAlarmEventList().stream().map(tmp -> {
                            MapVehiclePageDTO.AlarmEvent alarmEvent = new MapVehiclePageDTO.AlarmEvent();
                            alarmEvent.setAlarmEvent(tmp.getType());
                            alarmEvent.setReportTime(tmp.getReportTime());
                            return alarmEvent;
                        }).collect(Collectors.toList());
                        vehicle.setAlarmEventList(alarmEventList);
                    }
                }
            }
            setTakeOverInfo(vehicleTakeOverMap, vehicleName, vehicle);
            //设置任务绑定状态
            vehicle.setBindingState(vehicleStatus != null && vehicleStatus.getTaskId() != null);
            //设置里程信息
            if (!Objects.isNull(vehicleStatus) && Objects.equals(vehicleStatus.getCollectionMode(), CollectionModeEnum.COLLECTION.getCollectionMode())) {
                //设置采集模式里程
                VehicleDistanceDO vehicleDistanceDO = vehicleDistanceDOMap.get(vehicleName);
                if (vehicleDistanceDO != null) {
                    vehicle.setArrivedMileage(vehicleDistanceDO.getTotalDistance());
                }
                if (vehicleStatus.getTaskTotalMileage() != null) {
                    vehicle.setGlobalMileage(vehicleStatus.getTaskTotalMileage() * 1000);
                }
            } else {
                MonitorScheduleEntity monitorScheduleEntity = monitorScheduleEntityMap.get(vehicleName);
                if (!Objects.isNull(monitorScheduleEntity)) {
                    vehicle.setGlobalMileage(monitorScheduleEntity.getGlobalMileage());
                    vehicle.setArrivedMileage(getArrivedMileage(monitorScheduleEntity, realtime));
                }
            }
            list.add(vehicle);
        }
        return list;
    }

    /**
     * 设置接管信息
     */
    private static void setTakeOverInfo(Map<String, VehicleTakeOverEntity> vehicleTakeOverMap, String vehicleName, MapVehiclePageDTO vehicle) {
        if (MapUtils.isEmpty(vehicleTakeOverMap)) {
            return;
        }
        VehicleTakeOverEntity takeOver = vehicleTakeOverMap.get(vehicleName);
        if (Objects.isNull(takeOver)) {
            return;
        }
        vehicle.setTakeOverUserName(takeOver.getUserName());
        vehicle.setTakeOverSource(takeOver.getCommandSource());
        vehicle.setTakeOverStatus(takeOver.getOperationStatus());
    }

    /**
     * 当前调度完成里程
     *
     * @param monitorScheduleEntity monitorScheduleEntity
     * @param realtime              realtime
     * @return Double
     */
    private Double getArrivedMileage(MonitorScheduleEntity monitorScheduleEntity, VehicleRealtimeInfoDTO realtime) {
        Double finishedMileage = Optional.ofNullable(monitorScheduleEntity.getFinishedMileage()).orElse(0.0);
        if (StringUtils.equalsAny(monitorScheduleEntity.getScheduleState(), VehicleScheduleState.SETOUT.getVehicleScheduleState(), VehicleScheduleState.TOLOAD.getVehicleScheduleState(), VehicleScheduleState.TOUNLOAD.getVehicleScheduleState(), VehicleScheduleState.RETURN.getVehicleScheduleState())) {
            Double currentStopFinishedMileage;
            if (realtime == null || realtime.getCurrentStopFinishedMileage() == null) {
                currentStopFinishedMileage = 0.0;
            } else {
                currentStopFinishedMileage = realtime.getCurrentStopFinishedMileage();
            }
            finishedMileage += currentStopFinishedMileage;
        }
        return finishedMileage;
    }

    /**
     * 采图多车页模糊搜索车牌号列表接口
     *
     * @param fuzzySearchVehicleVO fuzzySearchVehicleVO
     * @return List<FuzzySearchVehicleDTO>
     */
    public List<FuzzySearchVehicleDTO> fuzzySearchVehicle(FuzzySearchVehicleVO fuzzySearchVehicleVO) {
        List<String> vehicleNameList = duccMonitorProperties.getVehicleNameList();
        List<FuzzySearchVehicleDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(vehicleNameList)) {
            return result;
        }
        String searchName = fuzzySearchVehicleVO.getSearchName();
        List<VehicleBasicDTO> vehicleBasicDTOList = vehicleBasicRepository.list(vehicleNameList);
        for (VehicleBasicDTO vehicleBasicDTO : vehicleBasicDTOList) {
            if (StringUtils.indexOfIgnoreCase(vehicleBasicDTO.getName(), searchName) == -1 && StringUtils.indexOfIgnoreCase(vehicleBasicDTO.getStationName(), searchName) == -1) {
                continue;
            }
            FuzzySearchVehicleDTO dto = new FuzzySearchVehicleDTO();
            dto.setVehicleName(vehicleBasicDTO.getName());
            dto.setStationName(vehicleBasicDTO.getStationName());
            result.add(dto);
            // 最多返回50条
            if (result.size() >= 50) {
                break;
            }
        }
        return result;
    }

    /**
     * 车辆认领/取消认领任务
     *
     * @param associateTaskVO associateTaskVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void associateTask(AssociateTaskVO associateTaskVO) {
        ActionTypeEnum actionTypeEnum = ActionTypeEnum.of(associateTaskVO.getAction());
        if (Objects.isNull(actionTypeEnum)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_ACTION_TYPE.getCode(), MonitorErrorEnum.ERROR_ACTION_TYPE.getMessage());
        }
        String taskLock = RedisKeyEnum.ASSOCIATE_TASK_LOCK.getValue() + associateTaskVO.getTaskId();
        if (RedissonUtils.tryLock(taskLock)) {
            MapCollectionTask mapCollectionTask = mapCollectionTaskManager.getById(associateTaskVO.getTaskId());
            if (mapCollectionTask == null) {
                log.error("采集任务不存在,taskId:{}", associateTaskVO.getTaskId());
                throw new BusinessException(MonitorErrorEnum.ERROR_TASK.getCode(), MonitorErrorEnum.ERROR_TASK.getMessage());
            }
            try {
                switch (actionTypeEnum) {
                    case OPEN:
                        //认领任务
                        if (mapCollectionTask.getVehicleName() != null) {
                            log.error("任务已经被认领,taskId:{}, dbVehicleName:{}, requestVehicleName:{}", associateTaskVO.getTaskId(), mapCollectionTask.getVehicleName(), associateTaskVO.getVehicleName());
                            throw new BusinessException(MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_EXIST.getCode(), MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_EXIST.getMessage());
                        }
                        //判断该车是否有其它认领任务
                        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(associateTaskVO.getVehicleName());
                        if (vehicleStatusDO.getTaskId() != null) {
                            log.error("车辆已认领任务,vehicleName:{}, dbTaskId:{}, voTaskId:{}", associateTaskVO.getVehicleName(), associateTaskVO.getTaskId(), vehicleStatusDO.getTaskId());
                            throw new BusinessException(MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_HAVE_TASK.getCode(), MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_HAVE_TASK.getMessage());
                        }

                        //更改任务状态为采集中&&关联车牌号
                        mapCollectionTaskManager.lambdaUpdate().eq(MapCollectionTask::getId, associateTaskVO.getTaskId())
                                .set(MapCollectionTask::getTaskStatus, TaskStatusEnum.COLLECTING.getCode())
                                .set(MapCollectionTask::getVehicleName, associateTaskVO.getVehicleName()).set(MapCollectionTask::getModifyUser, JsfLoginUtil.getUsername()).update();

                        //写入缓存
                        vehicleStatusRepository.putMapValue(associateTaskVO.getVehicleName(), VehicleStatusDO::getTaskId, String.valueOf(mapCollectionTask.getId()));
                        vehicleStatusRepository.putMapValue(associateTaskVO.getVehicleName(), VehicleStatusDO::getTaskTotalMileage, String.valueOf(mapCollectionTask.getTotalMileage()));
                        vehicleStatusRepository.putMapValue(associateTaskVO.getVehicleName(), VehicleStatusDO::getCollectionStatus, CollectionStatusEnum.NOT_STARTED.getCollectionStatus());
                        break;
                    case CLOSE:
                        //取消任务
                        if (!Objects.equals(associateTaskVO.getVehicleName(), mapCollectionTask.getVehicleName())) {
                            log.error("取消认领任务失败,任务车辆与解绑车辆不一致,taskId:{},dbVehicleName:{}, voVehicleName:{}", associateTaskVO.getTaskId(), mapCollectionTask.getVehicleName(), associateTaskVO.getVehicleName());
                            throw new BusinessException(MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_NOT_RIGHT.getCode(), MonitorErrorEnum.ERROR_ASSOCIATE_VEHICLE_NOT_RIGHT.getMessage());
                        }

                        //更改任务状态为待认领 && 删除采集记录表数据
                        mapCollectionTaskManager.lambdaUpdate().eq(MapCollectionTask::getId, associateTaskVO.getTaskId())
                                .set(MapCollectionTask::getTaskStatus, TaskStatusEnum.PENDING_CLAIM.getCode())
                                .set(MapCollectionTask::getVehicleName, null).set(MapCollectionTask::getModifyUser, JsfLoginUtil.getUsername()).update();
                        mapCollectionTaskTimeRecordManager.lambdaUpdate().eq(MapCollectionTaskTimeRecord::getTaskId, associateTaskVO.getTaskId()).eq(MapCollectionTaskTimeRecord::getVehicleName, associateTaskVO.getVehicleName()).remove();

                        //清除路径和距离缓存 && 移除车辆实时信息缓存关联任务
                        vehicleMapRealRouteRepository.remove(associateTaskVO.getVehicleName());
                        vehicleMapDistanceRepository.remove(associateTaskVO.getVehicleName());
                        vehicleStatusRepository.fastRemoveMapKey(associateTaskVO.getVehicleName(), VehicleStatusDO::getCollectionStatus, VehicleStatusDO::getTaskId, VehicleStatusDO::getTaskTotalMileage);
                        break;
                }
            } finally {
                RedissonUtils.unLock(taskLock);
            }
        } else {
            log.error("获取关联任务锁失败:{}", associateTaskVO.getTaskId());
            throw new BusinessException(MonitorErrorEnum.ERROR_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_REPEAT_OPERATION.getMessage());
        }
    }

    /**
     * 开启/关闭地图采集模式
     *
     * @param switchModeVO switchModeVO
     */
    public void switchMode(SwitchModeVO switchModeVO) {
        // 校验action
        ActionTypeEnum actionTypeEnum = ActionTypeEnum.of(switchModeVO.getAction());
        if (Objects.isNull(actionTypeEnum)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_ACTION_TYPE.getCode(), MonitorErrorEnum.ERROR_ACTION_TYPE.getMessage());
        }

        // 处理action
        String username = JsfLoginUtil.getUsername();
        String vehicleName = switchModeVO.getVehicleName();
        String cockpitNumber = switchModeVO.getCockpitNumber();

        RemoteCommandVO commandVO = new RemoteCommandVO();
        commandVO.setVehicleName(vehicleName);
        commandVO.setReceiveTimeStamp(switchModeVO.getOperateTime());
        commandVO.setTransitTimeStamp(new Date());
        commandVO.setCommandType(RemoteCommandTypeEnum.NORMAL);

        String statusKey = String.format(RedisKeyEnum.COLLECTION_VEHICLE_STATUS.getValue(), vehicleName);
        switch (actionTypeEnum) {
            case OPEN:
                log.info("处理[开启地图采集模式].");
                // 校验
                checkOpenMapCollection(vehicleName, cockpitNumber);
                // 下发开启地图采集模式指令
                if (duccMonitorProperties.getSendMapCollectionCommand()) {
                    HttpResult<Void> openHttpResult = remoteCommandService.openMapCollection(commandVO);
                    if (!HttpResult.isSuccess(openHttpResult)) {
                        throw new BusinessException(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode(), MonitorErrorEnum.ERROR_SEND_COMMAND.getMessage());
                    }
                }
                // 写入采图车辆切换状态缓存
                RedissonUtils.setObject(statusKey, System.currentTimeMillis(), duccMonitorProperties.getSwitchModeTimeout());
                // 自动清除驾舱接管
                sendExitTakeOver(vehicleName, cockpitNumber, username);
                break;
            case CLOSE:
                log.info("处理[关闭地图采集模式].");
                // 校验
                checkCloseMapCollection(vehicleName, cockpitNumber);
                // 下发进入有图模式指令
                if (duccMonitorProperties.getSendMapCollectionCommand()) {
                    HttpResult<Void> closeHttpResult = remoteCommandService.runHaveMap(commandVO);
                    if (!HttpResult.isSuccess(closeHttpResult)) {
                        throw new BusinessException(MonitorErrorEnum.ERROR_SEND_COMMAND.getCode(), MonitorErrorEnum.ERROR_SEND_COMMAND.getMessage());
                    }
                }
                // 写入采图车辆切换状态缓存
                RedissonUtils.setObject(statusKey, System.currentTimeMillis(), duccMonitorProperties.getSwitchModeTimeout());
                // 自动清除驾舱接管
                sendExitTakeOver(vehicleName, cockpitNumber, username);
                break;
            default:
                break;
        }
    }

    /**
     * 自动清除驾舱接管
     */
    private void sendExitTakeOver(String vehicleName, String cockpitNumber, String cockpitUserName) {
        MonitorMapCollectionTakeoverDTO takeoverDTO = new MonitorMapCollectionTakeoverDTO();
        takeoverDTO.setVehicleName(vehicleName);
        takeoverDTO.setCockpitNumber(cockpitNumber);
        takeoverDTO.setCockpitUserName(cockpitUserName);

        jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_MAP_COLLECTION_TAKEOVER.getTopic(), takeoverDTO);
        log.info("自动清除驾舱接管成功, vehicleName: [{}], cockpitNumber: [{}].", vehicleName, cockpitNumber);
    }

    /**
     * 校验是否支持开启地图采集
     */
    private void checkOpenMapCollection(String vehicleName, String cockpitNumber) {
        VehicleRealtimeInfoDTO realtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
        log.info("获取车辆实时信息返回结果, vehicleName: [{}] realtimeInfoDTO: [{}].", vehicleName, realtimeInfoDTO);

        if (Objects.isNull(realtimeInfoDTO)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_REALTIME_INFO.getCode(), MonitorErrorEnum.ERROR_REALTIME_INFO.getMessage());
        }

        // 系统状态：非离线
        if (SystemStatusEnum.OFFLINE.getCode().equals(realtimeInfoDTO.getSystemState())) {
            throw new BusinessException(MonitorErrorEnum.ERROR_SYSTEM_STATE.getCode(), MonitorErrorEnum.ERROR_SYSTEM_STATE.getMessage());
        }

        // 车速为0
        if (Objects.isNull(realtimeInfoDTO.getSpeed()) || Double.compare(Math.abs(realtimeInfoDTO.getSpeed()), duccMonitorProperties.getMinDrivingSpeed()) > 0) {
            throw new BusinessException(MonitorErrorEnum.ERROR_ZERO_SPEED.getCode(), MonitorErrorEnum.ERROR_ZERO_SPEED.getMessage());
        }

        // 接管状态：未被他人、他端接管
        VehicleTakeOverEntity takeOverEntity = vehicleTakeOverRepository.get(vehicleName);
        log.info("获取车辆接管信息返回结果, vehicleName: [{}] takeOverEntity: [{}].", vehicleName, takeOverEntity);

        if (Objects.nonNull(takeOverEntity) && !cockpitNumber.equals(takeOverEntity.getCockpitNumber())) {
            throw new BusinessException(MonitorErrorEnum.ERROR_TAKEOVER_STATE.getCode(), MonitorErrorEnum.ERROR_TAKEOVER_STATE.getMessage());
        }

        // 无调度
        MonitorScheduleEntity scheduleEntity = vehicleScheduleRepository.get(vehicleName);
        log.info("获取车辆调度信息返回结果, vehicleName: [{}] scheduleEntity: [{}].", vehicleName, scheduleEntity);

        if (Objects.nonNull(scheduleEntity)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_SCHEDULE_STATE.getCode(), MonitorErrorEnum.ERROR_SCHEDULE_STATE.getMessage());
        }
    }

    /**
     * 校验是否支持关闭地图采集
     */
    private void checkCloseMapCollection(String vehicleName, String cockpitNumber) {
        VehicleRealtimeInfoDTO realtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);
        log.info("获取车辆实时信息返回结果, vehicleName: [{}] realtimeInfoDTO: [{}].", vehicleName, realtimeInfoDTO);

        if (Objects.isNull(realtimeInfoDTO)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_REALTIME_INFO.getCode(), MonitorErrorEnum.ERROR_REALTIME_INFO.getMessage());
        }

        // 系统状态：非离线
        if (SystemStatusEnum.OFFLINE.getCode().equals(realtimeInfoDTO.getSystemState())) {
            throw new BusinessException(MonitorErrorEnum.ERROR_SYSTEM_STATE.getCode(), MonitorErrorEnum.ERROR_SYSTEM_STATE.getMessage());
        }

        // 车速为0
        if (Objects.isNull(realtimeInfoDTO.getSpeed()) || Double.compare(Math.abs(realtimeInfoDTO.getSpeed()), duccMonitorProperties.getMinDrivingSpeed()) > 0) {
            throw new BusinessException(MonitorErrorEnum.ERROR_ZERO_SPEED.getCode(), MonitorErrorEnum.ERROR_ZERO_SPEED.getMessage());
        }

        // 接管状态：未被他人、他端接管
        VehicleTakeOverEntity takeOverEntity = vehicleTakeOverRepository.get(vehicleName);
        log.info("获取车辆接管信息返回结果, vehicleName: [{}] takeOverEntity: [{}].", vehicleName, takeOverEntity);

        if (Objects.nonNull(takeOverEntity) && !cockpitNumber.equals(takeOverEntity.getCockpitNumber())) {
            throw new BusinessException(MonitorErrorEnum.ERROR_TAKEOVER_STATE.getCode(), MonitorErrorEnum.ERROR_TAKEOVER_STATE.getMessage());
        }

        // 车辆采图模式
        VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
        log.info("获取车辆状态信息返回结果, vehicleName: [{}] vehicleStatusDO: [{}].", vehicleName, vehicleStatusDO);

        if (Objects.isNull(vehicleStatusDO) || !CollectionModeEnum.COLLECTION.getCollectionMode().equals(vehicleStatusDO.getCollectionMode())) {
            throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_MODE.getCode(), MonitorErrorEnum.ERROR_COLLECTION_MODE.getMessage());
        }
    }

    /**
     * 开始/暂停采集
     *
     * @param switchCollectionVO switchCollectionVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void switchCollection(SwitchCollectionVO switchCollectionVO) {
        // 获取当前用户
        String username = JsfLoginUtil.getUsername();

        // 校验action
        ActionTypeEnum actionTypeEnum = ActionTypeEnum.of(switchCollectionVO.getAction());
        if (Objects.isNull(actionTypeEnum)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_ACTION_TYPE.getCode(), MonitorErrorEnum.ERROR_ACTION_TYPE.getMessage());
        }

        // 任务加锁，防止延时任务造成并发
        String taskLock = MapCollectConstant.getTaskLock(switchCollectionVO.getTaskId());
        if (!RedissonUtils.tryLock(taskLock, 3, TimeUnit.SECONDS)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_REPEAT_OPERATION.getCode(), MonitorErrorEnum.ERROR_REPEAT_OPERATION.getMessage());
        }
        try {
            // 获取当前采集状态
            String vehicleName = switchCollectionVO.getVehicleName();
            String cockpitNumber = switchCollectionVO.getCockpitNumber();
            Integer taskId = switchCollectionVO.getTaskId();

            VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
            log.info("获取车辆状态信息返回结果, vehicleName: [{}] vehicleStatusDO: [{}].", vehicleName, vehicleStatusDO);

            if (Objects.isNull(vehicleStatusDO)) {
                throw new BusinessException(MonitorErrorEnum.ERROR_VEHICLE_STATUS_INFO.getCode(), MonitorErrorEnum.ERROR_VEHICLE_STATUS_INFO.getMessage());
            }
            String collectionStatus = vehicleStatusDO.getCollectionStatus();

            // 处理action
            switch (actionTypeEnum) {
                case OPEN:
                    log.info("处理[开始采集].");
                    // 校验采集状态
                    if (CollectionStatusEnum.PROCESSING.getCollectionStatus().equals(collectionStatus)) {
                        throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_STATUS.getCode(), MonitorErrorEnum.ERROR_COLLECTION_STATUS.getMessage());
                    }

                    // 新增任务记录
                    Date date = new Date();
                    mapCollectionTaskTimeRecordManager.createRecord(taskId, vehicleName, cockpitNumber, username, date);

                    // 新增延时任务
                    DelayJob delayJobStart = getDelayJob(taskId, vehicleName, cockpitNumber);
                    DelayJobProducer.submitJob(delayJobStart, Long.valueOf(duccMonitorProperties.getMapTaskAutoUploadTimeInterval()), TimeUnit.MINUTES);
                    log.info("地图采集新增延时任务：[interval:{}, job:{}]", duccMonitorProperties.getMapTaskAutoUploadTimeInterval(), delayJobStart);

                    // 更新车辆实时状态缓存
                    vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDO::getCollectionStatus, CollectionStatusEnum.PROCESSING.getCollectionStatus());
                    vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDO::getOpenCollectionTime, String.valueOf(date.getTime()));
                    break;
                case CLOSE:
                    log.info("处理[暂停采集].");
                    // 校验采集状态
                    if (!CollectionStatusEnum.PROCESSING.getCollectionStatus().equals(collectionStatus)) {
                        throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_STATUS.getCode(), MonitorErrorEnum.ERROR_COLLECTION_STATUS.getMessage());
                    }

                    // 取消延时任务，数据上传，若延时任务被消费，则在延时处理线程执行记录更新及任务上传
                    DelayJob delayJobCancel = getDelayJob(taskId, vehicleName, cockpitNumber);
                    if (DelayJobProducer.cancelJob(delayJobCancel)) {
                        log.info("地图采集暂停，取消延时任务：[{}]", delayJobCancel);

                        // 更新任务记录，推送任务
                        persistentRecord(vehicleName, username, taskId, duccMonitorProperties.getOpenXataDataCopy(), true);
                    }

                    // 创建数据合并任务
                    createDataMergeTask(vehicleName, new Date(Long.parseLong(vehicleStatusDO.getOpenCollectionTime())), new Date(), duccMonitorProperties.getOpenXataDataCopy());

                    // 更新车辆实时状态缓存
                    vehicleStatusRepository.putMapValue(vehicleName, VehicleStatusDO::getCollectionStatus, CollectionStatusEnum.PAUSED.getCollectionStatus());

                    // 清除车辆实时轨迹
                    vehicleMapRealRouteRepository.remove(vehicleName);

                    // 清除采图车辆行驶距离
                    vehicleMapDistanceRepository.remove(vehicleName);
                    break;
                default:
                    break;
            }
        } finally {
            RedissonUtils.unLock(taskLock);
        }
    }

    /**
     * 创建数据合并任务
     */
    private void createDataMergeTask(String vehicleName, Date startTime, Date endTime, Boolean openXataDataCopy) {
        if (!BooleanUtil.isTrue(openXataDataCopy)) {
            return;
        }
        // xata创建
        List<DataCopyTaskItemDTO> dataItems = new ArrayList<>();
        DataCopyTaskItemDTO dataItem = DataCopyTaskItemDTO.builder()
                .startTime(DateUtil.formatDateTime(startTime))
                .endTime(DateUtil.formatDateTime(endTime))
                .topics(new ArrayList<>())
                .build();
        dataItems.add(dataItem);
        DataCopyTaskDTO taskDTO = DataCopyTaskDTO.builder()
                .type("dataplateform")
                .carNum(vehicleName)
                .priority("normal")
                .scene("collect_map")
                .env("instant_task_solid")
                .description("地图采集数据")
                .creator("system")
                .dataItems(dataItems)
                .build();
        boolean success = xataManager.createDataMergeTask(taskDTO);
        log.info("创建数据合并任务, 输入参数: [{}] 处理结果: [{}].", taskDTO, success);
    }

    /**
     * 更新任务记录
     */
    public Date persistentRecord(String vehicleName, String username, Integer taskId, Boolean openXataDataCopy, boolean paused) {
        List<VehiclePointDO> pointDOList = vehicleMapRealRouteRepository.get(vehicleName);
        if (CollUtil.isEmpty(pointDOList)) {
            pointDOList = Collections.emptyList();
        }
        log.info("获取采图车辆行驶点位缓存, vehicleName: [{}] pointList大小: [{}].", vehicleName, pointDOList.size());

        VehicleDistanceDO distanceDO = vehicleMapDistanceRepository.get(vehicleName);
        log.info("获取采图车辆行驶距离缓存, vehicleName: [{}] distanceDO: [{}].", vehicleName, distanceDO);

        Double drivingMileage = Optional.ofNullable(distanceDO).map(VehicleDistanceDO::getDistance).orElse(0.0);
        List<RealRoutePoint> routePointList = new ArrayList<>(pointDOList.size());
        pointDOList.forEach(vehiclePointDO -> {
            RealRoutePoint point = new RealRoutePoint();
            point.setLongitude(vehiclePointDO.getLongitude());
            point.setLatitude(vehiclePointDO.getLatitude());
            routePointList.add(point);
        });

        // 更新任务记录
        Date endTime = mapCollectionTaskTimeRecordManager.updateRecord(drivingMileage, routePointList, username, taskId, vehicleName, paused);

        // 异步创建数据拷贝任务
        if (BooleanUtil.isTrue(openXataDataCopy)) {
            createDataCopyTask(taskId, vehicleName);
        }
        return endTime;
    }

    /**
     * 完成采集
     *
     * @param finishCollectionVO finishCollectionVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void finishCollection(FinishCollectionVO finishCollectionVO) {
        // 获取当前用户
        String username = JsfLoginUtil.getUsername();

        // 任务加锁，防止延时任务造成并发
        RedissonUtils.tryLock(MapCollectConstant.getTaskLock(finishCollectionVO.getTaskId()), 10, TimeUnit.SECONDS);
        try {
            // 取消延时任务
            DelayJob delayJobCancel = getDelayJob(finishCollectionVO.getTaskId(), finishCollectionVO.getVehicleName(), finishCollectionVO.getCockpitNumber());
            DelayJobProducer.cancelJob(delayJobCancel);

            // 校验任务状态
            Integer taskId = finishCollectionVO.getTaskId();
            MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTask(taskId);
            if (Objects.isNull(mapCollectionTask)) {
                throw new BusinessException(MonitorErrorEnum.ERROR_TASK.getCode(), MonitorErrorEnum.ERROR_TASK.getMessage());
            }
            if (!TaskStatusEnum.COLLECTING.getCode().equals(mapCollectionTask.getTaskStatus())) {
                throw new BusinessException(MonitorErrorEnum.ERROR_TASK_STATUS.getCode(), MonitorErrorEnum.ERROR_TASK_STATUS.getMessage());
            }

            // 校验采集状态
            String vehicleName = finishCollectionVO.getVehicleName();

            VehicleStatusDO vehicleStatusDO = vehicleStatusRepository.get(vehicleName);
            log.info("获取车辆状态信息返回结果, vehicleName: [{}] vehicleStatusDO: [{}].", vehicleName, vehicleStatusDO);

            if (Objects.isNull(vehicleStatusDO)) {
                throw new BusinessException(MonitorErrorEnum.ERROR_VEHICLE_STATUS_INFO.getCode(), MonitorErrorEnum.ERROR_VEHICLE_STATUS_INFO.getMessage());
            }

            String collectionStatus = vehicleStatusDO.getCollectionStatus();
            if (CollectionStatusEnum.NOT_STARTED.getCollectionStatus().equals(collectionStatus)) {
                throw new BusinessException(MonitorErrorEnum.ERROR_COLLECTION_STATUS.getCode(), MonitorErrorEnum.ERROR_COLLECTION_STATUS.getMessage());
            }

            // 是否处于暂停
            if (!CollectionStatusEnum.PAUSED.getCollectionStatus().equals(collectionStatus)) {
                // 更新任务记录，推送任务
                persistentRecord(vehicleName, username, taskId, duccMonitorProperties.getOpenXataDataCopy(), true);
                // 创建数据合并任务
                createDataMergeTask(vehicleName, new Date(Long.parseLong(vehicleStatusDO.getOpenCollectionTime())), new Date(), duccMonitorProperties.getOpenXataDataCopy());
            }

            // 更新任务状态，清除缓存
            closeTask(taskId, vehicleName);
        } finally {
            RedissonUtils.unLock(MapCollectConstant.getTaskLock(finishCollectionVO.getTaskId()));
        }
    }

    /**
     * 完成
     */
    private void closeTask(Integer taskId, String vehicleName) {
        // 更新任务状态：TASK_CLOSED
        mapCollectionTaskManager.updateTaskStatus(taskId, TaskStatusEnum.TASK_CLOSED);

        // 删除采图车辆行驶点位缓存
        boolean removeRoute = vehicleMapRealRouteRepository.remove(vehicleName);
        log.info("删除采图车辆行驶点位缓存, vehicleName: [{}] 结果: [{}].", vehicleName, removeRoute);

        // 删除采图车辆行驶距离缓存
        boolean removeDistance = vehicleMapDistanceRepository.remove(vehicleName);
        log.info("删除采图车辆行驶距离缓存, vehicleName: [{}] 结果: [{}].", vehicleName, removeDistance);

        // 清空采集状态缓存
        long removeMapKeyNum = vehicleStatusRepository.fastRemoveMapKey(vehicleName, VehicleStatusDO::getCollectionStatus, VehicleStatusDO::getTaskId, VehicleStatusDO::getTaskTotalMileage);
        log.info("清空采集状态缓存, vehicleName: [{}] 结果: [{}].", vehicleName, removeMapKeyNum);
    }

    /**
     * 创建数据拷贝任务
     */
    public void createDataCopyTask(Integer taskId, String vehicleName) {
        // 查询任务列表
        MapCollectionTaskTimeRecord record = mapCollectionTaskTimeRecordManager.queryRecord(taskId, vehicleName);
        if (ObjectUtil.isNull(record)) {
            log.error("[告警]创建数据拷贝任务,查询任务记录失败, taskId: [{}] vehicleName: [{}].", taskId, vehicleName);
            return;
        }

        // xata创建
        List<DataCopyTaskItemDTO> dataItems = new ArrayList<>();
        DataCopyTaskItemDTO dataItem = DataCopyTaskItemDTO.builder()
                .startTime(DateUtil.formatDateTime(record.getStartTime()))
                .endTime(DateUtil.formatDateTime(record.getEndTime()))
                .topics(new ArrayList<>())
                .build();
        dataItems.add(dataItem);
        DataCopyTaskDTO taskDTO = DataCopyTaskDTO.builder()
                .type("dataplateform")
                .carNum(vehicleName)
                .priority("normal")
                .scene("collect_map")
                .env("instant_task_solid")
                .description("地图采集数据")
                .creator("system")
                .dataItems(dataItems)
                .build();
        boolean success = xataManager.createDataCopyTask(taskDTO);
        log.info("创建数据拷贝任务, 输入参数: [{}] 处理结果: [{}].", taskDTO, success);
    }

    /**
     * 获取采图路线
     *
     * @param getRouteVO getRouteVO
     * @return GetRouteDTO
     */
    public GetRouteDTO getRoute(GetRouteVO getRouteVO) {
        // 获取实时信息
        String vehicleName = getRouteVO.getVehicleName();
        Integer taskId = getRouteVO.getTaskId();
        VehicleRealtimeInfoDTO realtimeInfoDTO = vehicleRealtimeRepository.get(vehicleName);

        // 获取导航路径+四车道
        MapCollectionTask mapCollectionTask = mapCollectionTaskManager.queryTask(taskId);
        if (Objects.isNull(mapCollectionTask)) {
            throw new BusinessException(MonitorErrorEnum.ERROR_TASK.getCode(), MonitorErrorEnum.ERROR_TASK.getMessage());
        }

        List<GetRouteDTO.RoutePointDTO> taskRouteList = new ArrayList<>();
        Optional.ofNullable(mapCollectionTask.getTaskRoute()).orElse(new ArrayList<>()).forEach(taskRoutePoint -> {
            CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(taskRoutePoint.getLongitude(), taskRoutePoint.getLatitude());
            GetRouteDTO.RoutePointDTO pointDTO = new GetRouteDTO.RoutePointDTO();
            pointDTO.setLongitude(coordinate.getLng());
            pointDTO.setLatitude(coordinate.getLat());
            taskRouteList.add(pointDTO);
        });

        List<GetRouteDTO.RouteLaneDTO> fourLaneList = new ArrayList<>();
        Optional.ofNullable(mapCollectionTask.getFourLane()).orElse(new ArrayList<>()).forEach(fourLaneInfo -> {
            GetRouteDTO.RouteLaneDTO laneDTO = new GetRouteDTO.RouteLaneDTO();
            laneDTO.setStartAddress(fourLaneInfo.getStartAddress());
            laneDTO.setEndAddress(fourLaneInfo.getEndAddress());
            laneDTO.setRouteIndexList(fourLaneInfo.getRouteIndexList());
            fourLaneList.add(laneDTO);
        });

        // 组装
        GetRouteDTO routeDTO = new GetRouteDTO();
        routeDTO.setVehicleName(vehicleName);
        Optional.ofNullable(realtimeInfoDTO).ifPresent(infoDTO -> {
            CoordinateUtil.Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(infoDTO.getLon(), infoDTO.getLat());
            routeDTO.setLongitude(coordinate.getLng());
            routeDTO.setLatitude(coordinate.getLat());
        });
        routeDTO.setTaskRouteList(taskRouteList);
        routeDTO.setFourLaneList(fourLaneList);
        return routeDTO;
    }

    /**
     * 获取勘查任务关联线路
     *
     * @param taskBaseVO taskBaseVO
     * @return TaskRouteDTO
     */
    public TaskRouteDTO getExplorationTaskDetail(TaskBaseVO taskBaseVO) {
        TaskRouteDTO taskRouteDTO = mapTaskService.getTaskRoute(taskBaseVO);

        // WGS84转GCJ02
        if (ObjectUtil.isNotNull(taskRouteDTO) && CollUtil.isNotEmpty(taskRouteDTO.getTaskRouteList())) {
            taskRouteDTO.getTaskRouteList().forEach(taskRoute -> {
                Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(taskRoute.getLongitude(), taskRoute.getLatitude());
                taskRoute.setLongitude(coordinate.getLng());
                taskRoute.setLatitude(coordinate.getLat());
            });
        }
        return taskRouteDTO;
    }

    /**
     * 获取标记列表
     *
     * @param positionVO positionVO
     * @return MarkSearchDTO
     */
    public MarkSearchDTO getNearbyMarks(PositionVO positionVO) {
        // 监控调用入参02转84
        Coordinate pos84 = CoordinateUtil.gcj02ToWgs84(positionVO.getLongitude(), positionVO.getLatitude());
        PositionVO convertedPosition = new PositionVO();
        convertedPosition.setLatitude(pos84.getLat());
        convertedPosition.setLongitude(pos84.getLng());
        MarkSearchDTO markSearchDTO = mapMarkService.getMarkList(convertedPosition);

        // WGS84转GCJ02
        if (ObjectUtil.isNotNull(markSearchDTO) && CollUtil.isNotEmpty(markSearchDTO.getMarkList())) {
            markSearchDTO.getMarkList().forEach(markDTO -> {
                Coordinate coordinate = CoordinateUtil.wgs84ToGcj02(markDTO.getLongitude(), markDTO.getLatitude());
                markDTO.setLongitude(coordinate.getLng());
                markDTO.setLatitude(coordinate.getLat());
            });
        }
        return markSearchDTO;
    }

    /**
     * 获取视频地址
     *
     * @param multiVideoUrlVO multiVideoUrlVO
     * @return 多车视频链接
     */
    public List<MultiVideoUrlDTO> getVideoUrlList(MultiVideoUrlVO multiVideoUrlVO) {
        List<MultiVideoBO> multiUrlList = videoManager.getMultiUrl(multiVideoUrlVO.getVehicleNameList(), "true", "true", "true", "true");
        List<MultiVideoUrlDTO> result = new ArrayList<>();
        for (MultiVideoBO multiVideoBO : multiUrlList) {
            MultiVideoUrlDTO multiVideoUrlDTO = new MultiVideoUrlDTO();
            multiVideoUrlDTO.setVehicleName(multiVideoBO.getCarId());
            multiVideoUrlDTO.setExpireTime(multiVideoBO.getExpireTime());
            multiVideoUrlDTO.setPushType(multiVideoBO.getPushType());
            multiVideoUrlDTO.setFront(multiVideoBO.getFront());
            multiVideoUrlDTO.setBack(multiVideoBO.getBack());
            multiVideoUrlDTO.setLeft(multiVideoBO.getLeft());
            multiVideoUrlDTO.setRight(multiVideoBO.getRight());
            result.add(multiVideoUrlDTO);
        }
        return result;
    }

    /**
     * 查看车辆固化数据
     *
     * @param solidifiedTaskSearchVO solidifiedTaskSearchVO
     * @return List<VehicleSolidifiedTaskDTO>
     */
    public List<VehicleSolidifiedTaskDTO> getSolidifiedTaskList(SolidifiedTaskSearchVO solidifiedTaskSearchVO) {
        return xataManager.queryVehicleSolidifiedTask(solidifiedTaskSearchVO.getVehicleName());
    }

    /**
     * 取消固化数据
     *
     * @param solidifiedTaskOperateVO solidifiedTaskOperateVO
     */
    public void cancelSolidifiedTask(SolidifiedTaskOperateVO solidifiedTaskOperateVO) {
        if (BooleanUtil.isTrue(duccMonitorProperties.getOpenXataDataCopy())) {
            xataManager.cancelSolidifiedTask(solidifiedTaskOperateVO.getTaskId());
        }
    }

    /**
     * 获取车辆已采集线路
     *
     * @param getRouteVO getRouteVO
     * @return GetRouteDTO
     */
    public GetRouteDTO getFinishedRoute(GetRouteVO getRouteVO) {
        GetRouteDTO getRouteDTO = new GetRouteDTO();
        List<List<RoutePointDTO>> finishedPointList = Lists.newArrayList();
        getRouteDTO.setFinishedPointList(finishedPointList);

        // 获取已落库点位
        List<MapCollectionTaskTimeRecord> realRoute = mapCollectionTaskTimeRecordManager.getRealRoute(getRouteVO.getTaskId());
        List<RoutePointDTO> currentRoute = Lists.newArrayList();
        if (CollUtil.isNotEmpty(realRoute)) {
            for (MapCollectionTaskTimeRecord mapCollectionTaskTimeRecord : realRoute) {
                List<RealRoutePoint> recordRoute = mapCollectionTaskTimeRecord.getRealRoute();
                if (CollUtil.isNotEmpty(recordRoute)) {
                    for (RealRoutePoint realRoutePoint : recordRoute) {
                        RoutePointDTO routePointDTO = new RoutePointDTO();
                        routePointDTO.setLatitude(realRoutePoint.getLatitude());
                        routePointDTO.setLongitude(realRoutePoint.getLongitude());
                        currentRoute.add(routePointDTO);
                    }
                }

                if (mapCollectionTaskTimeRecord.getPaused()) {
                    finishedPointList.add(Lists.newArrayList(currentRoute));
                    currentRoute.clear();
                }
            }
        }

        // 获取缓存中的车辆实时点位
        List<VehiclePointDO> vehiclePoints = vehicleMapRealRouteRepository.get(getRouteVO.getVehicleName());
        if (CollUtil.isNotEmpty(vehiclePoints)) {
            currentRoute.addAll(vehiclePoints.stream()
                .map(item -> {
                    RoutePointDTO routePointDTO = new RoutePointDTO();
                    routePointDTO.setLatitude(item.getLatitude());
                    routePointDTO.setLongitude(item.getLongitude());
                    return routePointDTO;
                })
                .collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(currentRoute)) {
            finishedPointList.add(Lists.newArrayList(currentRoute));
        }
        return getRouteDTO;
    }

    /**
     * 数据自动上传延时任务
     *
     * @param taskId        taskId
     * @param vehicleName   vehicleName
     * @param cockpitNumber cockpitNumber
     * @return DelayJob
     */
    public DelayJob getDelayJob(Integer taskId, String vehicleName, String cockpitNumber) {
        Map<String, Object> paramMap = new ParamMap<MapTaskDO>()
                .addProperty(MapTaskDO::getTaskId, taskId)
                .addProperty(MapTaskDO::getVehicleName, vehicleName)
                .addProperty(MapTaskDO::getCockpitNumber, cockpitNumber)
                .toMap();
        return new DelayJob(paramMap, MapCollectConstant.MAP_TASK_AUTO_UPLOAD_JOB_TASK);
    }

    /**
     * 获取全部启用站点
     *
     * @return List<MonitorBasicDataInfoUnderUseDTO>
     */
    public List<MonitorBasicDataInfoUnderUseDTO> getAllStationInfo() {
        List<MonitorBasicDataInfoUnderUseDTO> result = new ArrayList<>();
        CityStationInfoDTO allCityAndStation = metadataStationApiManager.getAllCityAndStation();
        if (CollUtil.isEmpty(allCityAndStation.getCityList())) {
            return result;
        }
        //组装数据
        for (CityStationInfoDTO.CityInfoDTO cityInfoDTO : allCityAndStation.getCityList()) {
            MonitorBasicDataInfoUnderUseDTO monitorBasicDataInfoUnderUseDTO = new MonitorBasicDataInfoUnderUseDTO();
            result.add(monitorBasicDataInfoUnderUseDTO);
            monitorBasicDataInfoUnderUseDTO.setId(cityInfoDTO.getCityId());
            monitorBasicDataInfoUnderUseDTO.setName(cityInfoDTO.getCityName());
            // 获取城市下所有站点
            List<MonitorBasicDataInfoUnderUseDTO> stationInfoDTOList = new ArrayList<>();
            monitorBasicDataInfoUnderUseDTO.setChild(stationInfoDTOList);
            if (CollUtil.isEmpty(cityInfoDTO.getStationList())) {
                continue;
            }
            for (CityStationInfoDTO.StationInfoDTO stationInfoDTO : cityInfoDTO.getStationList()) {
                MonitorBasicDataInfoUnderUseDTO stationInfo = new MonitorBasicDataInfoUnderUseDTO();
                stationInfo.setId(stationInfoDTO.getStationId());
                stationInfo.setName(stationInfoDTO.getStationName());
                stationInfoDTOList.add(stationInfo);
            }
        }
        return result;
    }
}