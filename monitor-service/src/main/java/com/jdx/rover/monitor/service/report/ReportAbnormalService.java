/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.report;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.constant.NumberConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.dto.technical.ErrorCodeTranslateInfoDTO;
import com.jdx.rover.monitor.manager.abnormal.GuardianVehicleAbnormalManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.po.GuardianVehicleAbnormal;
import com.jdx.rover.monitor.repository.mapper.GuardianVehicleAbnormalMapper;
import com.jdx.rover.monitor.repository.redis.metadata.ErrorCodeTranslateRepository;
import com.jdx.rover.monitor.service.base.BaseService;
import com.jdx.rover.server.api.domain.dto.report.abnormal.VehicleAbnormalDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 车辆异常service类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReportAbnormalService extends BaseService<GuardianVehicleAbnormalMapper, GuardianVehicleAbnormal> {
    @Autowired
    private GuardianVehicleAbnormalManager guardianVehicleAbnormalManager;

    @Autowired
    private SingleVehicleManager singleVehicleManager;

    @Autowired
    private ErrorCodeTranslateRepository errorCodeTranslateRepository;

    record GroupKey(String name, String moduleName, String errorCode, Date startTime) {
    }
    /**
     * 处理kafka消息
     *
     * @param messageList
     */
    public void handleMessage(List<String> messageList) {
        List<GuardianVehicleAbnormal> poList = new ArrayList<>();
        List<String> vehicleNameList = new ArrayList<>();
        for (String message : messageList) {
            TypeReference<List<VehicleAbnormalDTO>> typeReference = new TypeReference<List<VehicleAbnormalDTO>>() {
            };
            List<VehicleAbnormalDTO> dtoList = JsonUtils.readValue(message, typeReference);
            if (CollectionUtils.isEmpty(dtoList)) {
                log.error("读取异常信息为空!{}", message);
                continue;
            }
            List<GuardianVehicleAbnormal> list = convertDTOToPOList(dtoList);
            poList.addAll(list);
            vehicleNameList.add(dtoList.get(0).getVehicleName());
        }

        List<GuardianVehicleAbnormal> distinctList = distinctAbnormal(poList);
        guardianVehicleAbnormalManager.saveOrUpdateBatchList(distinctList);

        List<String> vehicleNameDistinctList = vehicleNameList.stream().distinct().collect(Collectors.toList());
        for (String vehicleName : vehicleNameDistinctList) {
            // 推送异常增量消息
            singleVehicleManager.pushSingleVehicleException(vehicleName);
        }
    }

    /**
     * 通过车号,模块名,错误码,开始时间去重,如果存在endTime保留,否则取第一个
     */
    private List<GuardianVehicleAbnormal> distinctAbnormal(List<GuardianVehicleAbnormal> poList) {
        // 通过车号,模块名,错误码,开始时间去重,如果存在endTime保留,否则取第一个
        List<GuardianVehicleAbnormal> distinctList = poList.stream().collect(Collectors.groupingBy(
                o -> new GroupKey(o.getVehicleName(), o.getModuleName(), o.getErrorCode(), o.getStartTime()),
                LinkedHashMap::new,
                Collectors.collectingAndThen(
                        Collectors.toList(),
                        group -> {
                            Optional<GuardianVehicleAbnormal> firstWithEndTime = group.stream()
                                    .filter(obj -> obj.getEndTime() != null)
                                    .findFirst();
                            return firstWithEndTime.orElse(group.get(0));
                        }
                )
        )).values().stream().toList();
        if (poList.size() != distinctList.size()) {
            log.info("去重前={},去重后={}", JsonUtils.writeValueAsString(poList), JsonUtils.writeValueAsString(distinctList));
        }
        return distinctList;
    }

    /**
     * DTO转换为PO
     *
     * @param dtoList
     * @return
     */
    private List<GuardianVehicleAbnormal> convertDTOToPOList(List<VehicleAbnormalDTO> dtoList) {
        List<GuardianVehicleAbnormal> result = new ArrayList<>();
        for (VehicleAbnormalDTO detailDTO : dtoList) {
            GuardianVehicleAbnormal po = new GuardianVehicleAbnormal();
            po.setVehicleName(detailDTO.getVehicleName());
            po.setModuleName(detailDTO.getModuleName());
            po.setErrorCode(detailDTO.getErrorCode());
            po.setErrorLevel(detailDTO.getErrorLevel());
            if (StringUtils.isNotBlank(detailDTO.getErrorMsg())) {
                String errMsg = StringUtils.substring(detailDTO.getErrorMsg(), 0, 500);
                po.setErrorMessage(errMsg);
                List<ErrorCodeTranslateInfoDTO> translateList = errorCodeTranslateRepository.get(detailDTO.getErrorCode());
                if (CollectionUtil.isNotEmpty(translateList)) {
                    Optional<ErrorCodeTranslateInfoDTO> optional = translateList.stream().filter(translate ->
                            errMsg.toLowerCase().contains(translate.getErrorMessage().toLowerCase())).sorted(new Comparator<ErrorCodeTranslateInfoDTO>() {
                        @Override
                        public int compare(ErrorCodeTranslateInfoDTO o1, ErrorCodeTranslateInfoDTO o2) {
                            return o2.getErrorMessage().length() - o1.getErrorMessage().length();
                        }
                    }).findFirst();
                    if (optional.isPresent()) {
                        po.setTranslateMessage(optional.get().getErrorTranslate());
                    }
                }
            }
            po.setStartTime(new Date(detailDTO.getStartTime() / NumberConstant.MILLION));
            if (!Objects.isNull(detailDTO.getEndTime()) && detailDTO.getEndTime() != 0) {
                po.setEndTime(new Date(detailDTO.getEndTime() / NumberConstant.MILLION));
            }
            result.add(po);
        }
        return result;
    }
}