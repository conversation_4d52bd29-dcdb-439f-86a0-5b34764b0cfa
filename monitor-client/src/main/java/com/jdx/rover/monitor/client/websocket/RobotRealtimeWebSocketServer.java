/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceAbnormalInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceAbnormalSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.dto.MonitorStationVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.RobotMapMessageListener;
import com.jdx.rover.monitor.service.listener.redis.MapRobotPositionMessageListener;
import com.jdx.rover.monitor.service.robot.MonitorRobotService;
import com.jdx.rover.monitor.service.robot.RobotCongestionInfoService;
import com.jdx.rover.monitor.service.robot.RobotMapService;
import com.jdx.rover.monitor.service.webterminal.WebTerminalService;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;
import com.jdx.rover.monitor.vo.vehicle.SingleVehicleVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  机器人远程遥控
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/robot/ws/realtime")
@Component
public class RobotRealtimeWebSocketServer {

  /**
   * <p>
   * 统计在线数
   * </p>
   */
  private static int onlineCount = 0;

  /**
   * <p>
   * 机器人服务
   * </p>
   */
  private static MonitorRobotService monitorRobotService;

  /**
   * <p>
   * 机器人拥堵服务
   * </p>
   */
  private static RobotCongestionInfoService robotCongestionInfoService;

  /**
   * <p>
   * 机器人地图服务
   * </p>
   */
  private static RobotMapService robotMapService;

  /**
   * 用于处理与机器人远程终端相关的操作的服务
   */
  private static WebTerminalService webTerminalService;

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * <p>
   * Receive sessionid
   * </p>
   */
  private String sessionId = "";

  @Autowired
  public void setMonitorRemoteControlService(MonitorRobotService monitorRobotService) {
    RobotRealtimeWebSocketServer.monitorRobotService = monitorRobotService;
  }

  @Autowired
  public void setRobotCongestionInfoService(RobotCongestionInfoService robotCongestionInfoService) {
    RobotRealtimeWebSocketServer.robotCongestionInfoService = robotCongestionInfoService;
  }

  @Autowired
  public void setRobotMapService(RobotMapService robotMapService) {
    RobotRealtimeWebSocketServer.robotMapService = robotMapService;
  }

  @Autowired
  public void setWebTerminalService(WebTerminalService webTerminalService) {
    RobotRealtimeWebSocketServer.webTerminalService = webTerminalService;
  }

  @OnOpen
  public void onOpen(Session session) {
    this.sessionId = session.getId();
    String userName = session.getRequestParameterMap().get("userName").get(0);
    this.client.setUsername(userName);
    this.client.setSessionId(session.getId());
    addOnlineCount();
    log.info("robot realtime socket on open session:{}, user {}", session.getId(), userName);
  }

  @OnClose
  public void onClose(Session session) {
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    unSubscribeMapVehicle();
    unSubscribeMapScheduleInfo();
    unSubscribeMapCongestionInfo();
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    log.info(String.format("robot realtime socket on message sessionId:%s,message:%s", session.getId(), message));
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue())) {
      // 取消之前位置订阅
      unSubscribeMapVehicle();
      // 机器人地图位置订阅
      TypeReference<WebsocketVO<RobotMapInfoRequestVO>> typeReference = new TypeReference<WebsocketVO<RobotMapInfoRequestVO>>() {
      };
      WebsocketVO<RobotMapInfoRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
      WsResult<List<RobotMapRealtimeInfoDTO>> wsResult = buildRobotMapPositionInfo(detailVO.getData());
      sendWebsocketData(session, wsResult);
      List<String> vehicleNameList = new ArrayList<>();
      if (CollectionUtils.isNotEmpty(wsResult.getData())) {
        vehicleNameList = wsResult.getData().stream().map(RobotMapRealtimeInfoDTO::getDeviceName).collect(Collectors.toList());
      }
      subscribeMapVehicle(session, vehicleNameList);
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.MAP_SINGLE_VEHICLE_REQUEST.getValue())) {
      // 取消之前拥堵和业务路径订阅
      unSubscribeMapVehicle();
      unSubscribeMapCongestionInfo();
      unSubscribeMapScheduleInfo();
      TypeReference<WebsocketVO<RobotDeviceBasicSearchVO>> typeReference = new TypeReference<WebsocketVO<RobotDeviceBasicSearchVO>>() {
      };
      WebsocketVO<RobotDeviceBasicSearchVO> detailVO = JsonUtils.readValue(message, typeReference);
      RobotDeviceBasicSearchVO robotDeviceBasicVo = detailVO.getData();
      // 发送车辆实时位置
      RobotMapInfoRequestVO mapInfoRequestVo = new RobotMapInfoRequestVO();
      mapInfoRequestVo.setProductKey(robotDeviceBasicVo.getProductKey());
      mapInfoRequestVo.setDeviceName(robotDeviceBasicVo.getDeviceName());
      WsResult<List<RobotMapRealtimeInfoDTO>> wsResult = buildRobotMapPositionInfo(mapInfoRequestVo);
      sendWebsocketData(session, wsResult);
      // 发送拥堵区域
      List<String> robotList = robotCongestionInfoService.getSameCongestionRobotList(robotDeviceBasicVo.getProductKey(), robotDeviceBasicVo.getDeviceName());
      List<RobotMapRealtimeInfoDTO> mapRobotRealtimeInfoList = robotCongestionInfoService.
              getMapRobotRealtimeUnderCongestion(robotDeviceBasicVo.getProductKey(), robotList);
      WsResult congestionResult = WsResult.success(WebsocketEventTypeEnum.MAP_CONGESTION_INFO_UPDATE.getValue(), mapRobotRealtimeInfoList);
      sendWebsocketData(session, congestionResult);
      // 发送路线信息
      RobotMapRouteInfoDTO routeInfoDto = robotMapService.getMapDeviceRoute(robotDeviceBasicVo);
      WsResult routeWsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_UPDATE.getValue(), routeInfoDto);
      sendWebsocketData(session, routeWsResult);
      // 发送告警信息
      RobotDeviceAbnormalSearchVO deviceAbnormalSearchVo = new RobotDeviceAbnormalSearchVO();
      deviceAbnormalSearchVo.setProductKey(robotDeviceBasicVo.getProductKey());
      deviceAbnormalSearchVo.setDeviceName(robotDeviceBasicVo.getDeviceName());
      List<RobotDeviceAbnormalInfoDTO> abnormalInfoList = monitorRobotService.getDeviceAbnormalInfo(deviceAbnormalSearchVo);
      WsResult alarmInfoResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_ALARM.getValue(), abnormalInfoList);
      sendWebsocketData(session, alarmInfoResult);
      // 订阅实时位置+拥堵+业务路径
      subscribeMapCongestionInfo(session, robotDeviceBasicVo.getDeviceName());
      subscribeMapScheduleInfo(session, robotDeviceBasicVo.getDeviceName());
      subscribeMapVehicle(session, Lists.newArrayList(robotDeviceBasicVo.getDeviceName()));
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_REQUEST.getValue())) {
      // 取消之前拥堵订阅
      unSubscribeMapScheduleInfo();
      TypeReference<WebsocketVO<RobotDeviceBasicSearchVO>> typeReference = new TypeReference<WebsocketVO<RobotDeviceBasicSearchVO>>() {
      };
      WebsocketVO<RobotDeviceBasicSearchVO> detailVO = JsonUtils.readValue(message, typeReference);
      RobotDeviceBasicSearchVO robotDeviceBasicVo = detailVO.getData();
      subscribeMapScheduleInfo(session, robotDeviceBasicVo.getDeviceName());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_CANCEL_REQUEST.getValue())) {
      // 取消之前调度订阅
      unSubscribeMapScheduleInfo();
    } else if (StringUtils.equals(websocketVO.getEventType(),WebsocketEventTypeEnum.REMOTE_WEB_TERMINAL_TOOL.getValue())) {
       TypeReference<WebsocketVO<SingleVehicleVO>> typeReference = new TypeReference<WebsocketVO<SingleVehicleVO>>() {
      };
      WebsocketVO<SingleVehicleVO> detailVO = JsonUtils.readValue(message, typeReference);
      WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_WEB_TERMINAL_TOOL.getValue(), webTerminalService.getWebTerminalList(detailVO.getData().getVehicleName()));
      sendWebsocketData(session, wsResult);
    } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue())) {
      TypeReference<WebsocketVO<WebTerminalRequestVO>> typeReference = new TypeReference<WebsocketVO<WebTerminalRequestVO>>() {
      };
      WebsocketVO<WebTerminalRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
      webTerminalService.sendTransferStatusToVehicle(detailVO.getData(), this.client);
    }
  }

  /**
   * 构建机器人地图实时位置信息。
   * @param robotMapInfoRequestVo Websocket消息体
   * @return 包含机器人地图实时位置信息的WsResult对象
   */
  private WsResult<List<RobotMapRealtimeInfoDTO>> buildRobotMapPositionInfo(RobotMapInfoRequestVO robotMapInfoRequestVo) {
    List<RobotMapRealtimeInfoDTO> mapRobotRealtimeInfoList = monitorRobotService.getMapRobotRealtimeInfo(robotMapInfoRequestVo);
    WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue(), mapRobotRealtimeInfoList);
    return wsResult;
  }

  /**
   * 订阅设备的调度信息。
   * @param session 会话对象。
   * @param deviceName 设备名称。
   */
  private void subscribeMapScheduleInfo(Session session, String deviceName) {
    log.info("开始订阅车辆调度数据!{}", this.client);
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + deviceName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new RobotMapMessageListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    this.client.setScheduleListenerId(listenerId);
    this.client.setVehicleName(deviceName);
    log.info("订阅地图调度数据!{}", this.client);
  }

  /**
   * 取消订阅所有地图调度信息的主题。
   */
  private void unSubscribeMapScheduleInfo() {
    if (this.client.getScheduleListenerId() == null || StringUtils.isBlank(this.client.getVehicleName())) {
      return;
    }
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + this.client.getVehicleName();
    unSubscribeTopic(topicName, this.client.getScheduleListenerId());
  }

  /**
   * 订阅地图页机器人信息
   */
  private void subscribeMapVehicle(Session session, List<String> deviceNameList) {
    log.info("开始接收机器人地图页数据!{}", this.client);
    for (String vehicle : deviceNameList) {
      String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + vehicle;
      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      MessageListener messageListener = new MapRobotPositionMessageListener(session);
      int listenerId = rTopic.addListener(String.class, messageListener);
      log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
      if (this.client.getMapListenerIds() == null) {
        Map<String, Integer> map = new HashMap<>();
        map.put(vehicle, listenerId);
        this.client.setMapListenerIds(map);
      } else {
        this.client.getMapListenerIds().put(vehicle, listenerId);
      }
    }
    log.info("订阅车辆地图页数据!{}", this.client);
  }

  /**
   * 订阅设备的拥塞信息。
   * @param session 会话对象，用于与服务器通信。
   * @param deviceName 设备名称，指定要订阅的设备。
   */
  private void subscribeMapCongestionInfo(Session session, String deviceName) {
    log.info("开始接收地图拥堵数据!{}", this.client);
    String topicName = RedisTopicEnum.MAP_ROBOT_CONGESTION_PREFIX.getValue() + deviceName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new RobotMapMessageListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
      this.client.setVehicleName(deviceName);
    }
    log.info("订阅地图拥堵数据!{}", this.client);
  }

  /**
   * 取消订阅地图页数据
   */
  private void unSubscribeMapVehicle() {
    if (this.client.getMapListenerIds() == null) {
      return;
    }
    for (Map.Entry<String, Integer> entry : this.client.getMapListenerIds().entrySet()) {
      String topicName = RedisTopicEnum.MAP_ROBOT_POSITION_PREFIX.getValue() + entry.getKey();
      unSubscribeTopic(topicName, entry.getValue());
    }
  }

  /**
   * 取消订阅地图页数据
   */
  private void unSubscribeMapCongestionInfo() {
    if (this.client.getEventListenerId() == null || StringUtils.isBlank(this.client.getVehicleName())) {
      return;
    }
    String topicName = RedisTopicEnum.MAP_ROBOT_CONGESTION_PREFIX.getValue() + this.client.getVehicleName();
    unSubscribeTopic(topicName, this.client.getEventListenerId());
  }

  /**
   * 取消订阅数据
   */
  private void unSubscribeTopic(String topicName, Integer listenerId) {
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (listenerId != null) {
      rTopic.removeListener(listenerId);
    }
    log.info("webSocket remove topic={},listenerId={},subscribers={}", topicName, listenerId, rTopic.countSubscribers());
  }

  /**
   * @param session
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("monitor robot realtime socket on error:{},{}", session.getId(), throwable);
    throwable.printStackTrace();
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    log.info(String.format("monitor robot realtime socket on close user:%s,sessionId:%s,sersionNum:%s", sessionId, session.getId(),
        getOnlineCount()));
    try {
      session.close();
    } catch (IOException e) {
      log.error("monitor robot realtime websocket on error ioException {}", e);
    }
  }

  public static synchronized int getOnlineCount() {
    return onlineCount;
  }

  public static synchronized void addOnlineCount() {
    RobotRealtimeWebSocketServer.onlineCount++;
  }

  public static synchronized void subOnlineCount() {
    RobotRealtimeWebSocketServer.onlineCount--;
  }

   /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    try {
      synchronized (session) {
        session.getBasicRemote().sendText(jsonStr);
      }
    } catch (Exception e) {
      log.error("Monitor robot realtime ws send result exception", e);
    }
  }

}
