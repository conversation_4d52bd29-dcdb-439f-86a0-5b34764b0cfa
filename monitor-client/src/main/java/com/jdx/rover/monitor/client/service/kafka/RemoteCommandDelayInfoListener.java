package com.jdx.rover.monitor.client.service.kafka;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.server.api.domain.dto.hermes.HermesVehicleControlCommandDelayInfoDTO;
import com.jdx.rover.server.api.domain.enums.ServerKafkaTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class RemoteCommandDelayInfoListener {

  @KafkaListener(topics = {ServerKafkaTopicConstant.SERVER_REMOTE_COMMAND_DELAY})
  public void onMessage(ConsumerRecord<String, String> record) {
    Optional op = Optional.ofNullable(record);
    if (!op.isPresent()) {
      return;
    }
    String message = record.value();
    HermesVehicleControlCommandDelayInfoDTO delayInfoDto = JsonUtils.readValue(message, HermesVehicleControlCommandDelayInfoDTO.class);
    if (delayInfoDto == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + delayInfoDto.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_RESPONSE.getValue(), delayInfoDto);
      long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
      log.info("Success result {} send vehicle delay info to topic {}", result, topicName);
    } else {
      log.error("Error find topic {} listener", topicName);
    }
  }

}
