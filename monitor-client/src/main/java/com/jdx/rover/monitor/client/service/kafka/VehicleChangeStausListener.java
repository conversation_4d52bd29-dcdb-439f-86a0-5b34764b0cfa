package com.jdx.rover.monitor.client.service.kafka;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 车辆状态变化监控
 * </p>
 * <p>
 * * <AUTHOR>
 * * @version 1.0
 */
@Service
@Slf4j
public class VehicleChangeStausListener {
    /**
     * 处理车辆实时状态变化消息
     *
     * @param messageList
     */
    @KafkaListener(topics = {KafkaTopicConstant.SERVER_VEHICLE_CHANGE_STATUS}, containerFactory = "batchListenerContainerFactory")
    public void onMessage(List<String> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        messageList.forEach(this::handleOneMessage);
    }


    public void handleOneMessage(String message) {
        ChangeStatusDTO dto = JsonUtils.readValue(message, ChangeStatusDTO.class);
        ChangeStatusDTO.ChangeTypeEnum changeTypeEnum = ChangeStatusDTO.ChangeTypeEnum.of(dto.getChangeType());
        if (Objects.isNull(changeTypeEnum)) {
            return;
        }
        switch (changeTypeEnum) {
            case VEHICLE_STATE: {
                // 切换手柄控制
                if (VehicleStateEnum.LOCAL_CONTROL == VehicleStateEnum.valueOf(dto.getNewValue())) {
                    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + dto.getVehicleName();
                    RTopic rTopic = RedissonUtils.getRTopic(topicName);
                    if (rTopic.countSubscribers() > 0) {
                        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.CHANGE_VEHICLE_STATE_EVENT.getValue(), dto.getNewValue());
                        long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
                        log.info("Success result {} send vehicle mobile control to topic {}", result, topicName);
                    } else {
                        log.error("Error find topic {} listener", topicName);
                    }
                }
            }
            break;

            default:
                log.info("未匹配的消息{}", dto);
        }
    }


}
