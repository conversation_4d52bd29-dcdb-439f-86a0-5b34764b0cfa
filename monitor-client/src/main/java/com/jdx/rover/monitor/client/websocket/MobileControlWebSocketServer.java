/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.util.websocket.WebsocketUtils;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.RemoteControlVehicleResponseListener;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.vo.MiniMonitorResetAbnormalControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 运营端车辆遥控
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/minimonitor/client/ws/mobile/control",configurator = ServletAwareConfigurator.class)
@Component
public class MobileControlWebSocketServer {

  /**
   * <p>
   * Remote control service.
   * </p>
   */
  private static MiniMonitorCommandService miniMonitorCommandService;

  /**
   * Websocket客户端
   */
  private final WebsocketClientBO client = new WebsocketClientBO();

  /*
   * 时间轮, 槽位数32个，每个2S,5min 保障在1圈以内
   */
  private static final HashedWheelTimer wheelTimer = new HashedWheelTimer(2, TimeUnit.SECONDS, 20);

  /**
   * <p>
   * 会话
   * </p>
   */
  private String sessionId = "";

  /**
   * 注册服务
   */
  @Autowired
  public void setMiniMonitorRemoteControlService(MiniMonitorCommandService miniMonitorCommandService) {
    MobileControlWebSocketServer.miniMonitorCommandService = miniMonitorCommandService;
  }

  @OnOpen
  public void onOpen(Session session) {
    this.sessionId = session.getId();
    String userName = WebsocketUtils.getWebSocketUserName(session);
    if (StringUtils.isBlank(userName)) {
      log.error("mobile control websocket user absent.");
      try {
        session.close();
      } catch (IOException e) {
        log.info("mobile remote control socket on error ioException.", e);
      }
      return;
    }
    this.client.setUsername(userName);
    this.client.setSessionId(session.getId());
    log.info("mobile web socket on open session:{}, user {}", session.getId(), userName);
  }

  @OnClose
  public void onClose(Session session) {
    this.sessionId = session.getId();
    String vehicleName = this.client.getVehicleName();
    log.info("mobile close control socket sessionId:{},message:{}", this.sessionId, vehicleName);
    unSubscribeVehicleResponse(vehicleName);
    wheelTimer.newTimeout(new TimerTask() {
      @Override
      public void run(Timeout timeout) throws Exception {
        String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
        RTopic rTopic = RedissonUtils.getRTopic(topicName);
        if (rTopic.countSubscribers() > 0) {
          log.error("车辆遥控长链接关闭，监测有新链接遥控中");
        } else {
          miniMonitorCommandService.manualRecoveryRequest(vehicleName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
          miniMonitorCommandService.manualRecoveryRequest(vehicleName, RemoteCommandSourceEnum.WORKBENCH_MONITOR.getCommandSource());
        }
      }
    }, 10, TimeUnit.SECONDS);
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    log.info("mobile web control socket on message sessionId:{},message:{}.", session.getId(), message);
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    WsResult result = null;
    if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_ENTER_CONTROL.getValue())) {
      // 车辆进入遥控
      TypeReference<WebsocketVO<RemoteCommandVO>> typeReference = new TypeReference<WebsocketVO<RemoteCommandVO>>() {
      };
      WebsocketVO<RemoteCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      RemoteCommandVO remoteCommandVo = detailVO.getData();
      this.client.setVehicleName(remoteCommandVo.getVehicleName());
      subscribeVehicleResponse(session, remoteCommandVo.getVehicleName());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL.getValue())) {
      // 遥控指令下发
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MonitorRemoteControlCommandVO remoteCommandVo = detailVO.getData();
      remoteCommandVo.setUserName(client.getUsername());
      this.client.setVehicleName(remoteCommandVo.getVehicleName());
      result = miniMonitorCommandService.postRemoteControlRequest(remoteCommandVo);
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL.getValue())) {
      // 异常指令恢复
      TypeReference<WebsocketVO<MiniMonitorResetAbnormalControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MiniMonitorResetAbnormalControlCommandVO>>() {
      };
      WebsocketVO<MiniMonitorResetAbnormalControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MiniMonitorResetAbnormalControlCommandVO remoteCommandVo = detailVO.getData();
      result = miniMonitorCommandService.postResetAbnormalControlRequest(remoteCommandVo, client.getUsername());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_USER_CONTROL.getValue())) {
      // 记录用户操作指令
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      result = miniMonitorCommandService.postUserControlRequest(detailVO.getData(), client.getUsername());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_QUIT_CONTROL.getValue())) {
      // 车辆退出遥控
      TypeReference<WebsocketVO<MonitorRemoteCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      unSubscribeVehicleResponse(detailVO.getData().getVehicleName());
      result = WsResult.success("Quit Success");
    }
    sendWebsocketData(session, result);
  }

  /**
   * 订阅车辆遥控响应
   */
  private void subscribeVehicleResponse(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      log.info("mobile webSocket has subscribers session={}, topic={},subscribers={}", session.getId(), topicName, rTopic.countSubscribers());
      rTopic.removeAllListeners();
    }
    MessageListener messageListener = new RemoteControlVehicleResponseListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("mobile webSocket add topic={},listener count={},subscribers={}, listenerId={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(), listenerId);
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
    }
    log.info("订阅车辆{}遥控响应数据{}", this.client.getVehicleName(), session.getId());
  }

  /**
   * 取消订阅单车辆遥控响应
   */
  private void unSubscribeVehicleResponse(String vehicleName) {
    if (this.client.getEventListenerId() == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeListener(this.client.getEventListenerId());
    this.client.setEventListenerId(null);
    log.info("mobile webSocket remove session {}, topic={},listenerId={}", this.sessionId, topicName, this.client.getEventListenerId());
  }

  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("mobile web socket on error:{},{}", session.getId(), throwable);
    this.sessionId = session.getId();
    log.info("mobile web socket on close user:{},sessionId:{}", client.getUsername(), sessionId);
    try {
      session.close();
    } catch (IOException e) {
      log.error("mobile web socket on error ioException", e);
    }
  }

   /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    try {
      synchronized (session) {
        session.getBasicRemote().sendText(jsonStr);
      }
    } catch (Exception e) {
      log.error("mobile control ws send result exception", e);
    }
  }
}