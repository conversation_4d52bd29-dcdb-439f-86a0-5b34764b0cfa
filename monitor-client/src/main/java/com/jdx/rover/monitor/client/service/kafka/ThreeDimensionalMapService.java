/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.service.kafka;

import cn.hutool.core.collection.CollectionUtil;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.KafkaException;
import org.apache.kafka.common.TopicPartition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.websocket.Session;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ThreeDimensionalMapService {

  /**
   * <p>
   * Stores the map session. Session id -> Topic.
   * </p>
   */
  private Map<String, String> sessionTopicMap = new ConcurrentHashMap<String, String>();

  /**
   * <p>
   * Stores the list of session. Topic -> session id -> session.
   * </p>
   */
  private Map<String, Map<String, Session>> sessionMap = new ConcurrentHashMap<String, Map<String, Session>>();

  /**
   * <p>
   * Stores the lock.
   * </p>
   */
  private ReadWriteLock lock = new ReentrantReadWriteLock();

  @Value("${project.kafka-byte.bootstrap-servers}")
  private String bootstrapServers;

  /**
   *
   * <p>
   * This method provides the function to join the session into a topic.
   * </p>
   *
   * @param topic   the topic.
   * @param session the session.
   * @throws IllegalArgumentException if the argument does not meet requirement.
   */
  public void join(String topic, Session session) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(topic, "topic");
    ParameterCheckUtility.checkNotNull(session, "session");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(session.getId(), "session#id");
    try {
      lock.writeLock().lock();
      if (sessionTopicMap.containsKey(session.getId())) {
        log.info("This session id has been used.");
        return;
      }
      boolean unknownTopic = !sessionMap.containsKey(topic);
      log.info("Join record {} and topic {}", unknownTopic,topic);
      if (unknownTopic) {
        sessionMap.put(topic, new ConcurrentHashMap<String, Session>());
      }
      sessionMap.get(topic).put(session.getId(), session);
      sessionTopicMap.put(session.getId(), topic);
      if (unknownTopic) {
        new Thread(() -> {
          KafkaConsumer<String, byte[]> kafkaConsumer = null;
          try {
            lock.readLock().lock();
            if (!sessionMap.containsKey(topic)) {
              log.error("Invalid topic %s thread", topic);
            }
            lock.readLock().unlock();
            kafkaConsumer = createByteTemplate();
            kafkaConsumer.subscribe(Arrays.asList(topic));
            consumerRecord(kafkaConsumer);
            while (true) {
              try {
                lock.readLock().lock();
                Map<String, Session> activeSession = sessionMap.get(topic);
                if (CollectionUtil.isEmpty(activeSession)) {
                  lock.readLock().unlock();
                  log.error("interrupt {} kafka consumer {}", topic, JsonUtils.writeValueAsString(sessionTopicMap));
                  break;
                }
                lock.readLock().unlock();
                ConsumerRecords<String, byte[]> records =
                        kafkaConsumer.poll(Duration.ofSeconds(10));

                ConsumerRecord<String, byte[]> record = null;
                for (ConsumerRecord<String, byte[]> item : records) {
                  record = item;
                }
                if (record == null) {
                  continue;
                }
                List<Thread> threads = new ArrayList<Thread>();
                Session[] sessions = activeSession.values().toArray(new Session[0]);
                for (Session insideSession : sessions) {
                    final ConsumerRecord<String, byte[]> recordData = record;
                    threads.add(new Thread(() -> {
                        ByteBuffer data = ByteBuffer.wrap(recordData.value());
                        try {
                          insideSession.getBasicRemote().sendBinary(data);
                        } catch (IOException e) {
                          log.error("Failed to send binary data of kafka.");
                        }
                    }));
                }
                for (Thread thread : threads) {
                  thread.start();
                }
                for (Thread thread : threads) {
                  thread.join();
                }
              } catch (IllegalStateException | KafkaException e) {
                log.error("Failed to poll from Kafka.", e);
              } catch (Exception e) {
                log.error("Failed to fill the array.", e);
              }
            }
          } catch (Exception e) {
            log.error("Thread has be interrupted.", e);
          } finally {
            if (kafkaConsumer != null) {
              kafkaConsumer.close();
            }
            log.error("close kafka consumer {}", JsonUtils.writeValueAsString(sessionTopicMap));
          }
        }).start();
      }

    } finally {
      lock.writeLock().unlock();
    }
  }

  /**
   * <p>
   * This method provides the function to leave the session from a topic.
   * </p>
   *
   * @param session the session.
   * @throws IllegalArgumentException if the argument does not meet requirement.
   */
  public void leave(Session session) {
    ParameterCheckUtility.checkNotNull(session, "session");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(session.getId(), "session#id");
    log.error("Remove local view session: {}", session.getId());
    try {
      lock.writeLock().lock();
      String topic = sessionTopicMap.remove(session.getId());
      if (topic == null || !sessionMap.containsKey(topic) || !sessionMap.get(topic).containsKey(session.getId())) {
        return;
      }
      sessionMap.get(topic).remove(session.getId());
      if (CollectionUtil.isEmpty(sessionMap.get(topic))) {
        sessionMap.remove(topic);
      }
    } finally {
      lock.writeLock().unlock();
    }
  }

  /**
   * 创建接收byte[]数组kafka模板
   */
  private KafkaConsumer<String, byte[]> createByteTemplate() {
    Properties props = new Properties();
    props.put("bootstrap.servers", bootstrapServers);
    props.put("group.id", getHostName());
    props.put("enable.auto.commit", true);
    props.put("auto.offset.reset", "latest");
    props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
    props.put("value.deserializer", "org.apache.kafka.common.serialization.ByteArrayDeserializer");
    props.put("max.poll.records", 10000);
    props.put("max.poll.interval.ms", 120000);
    props.put("session.timeout.ms", 300000);

    return new KafkaConsumer<String, byte[]>(props);
  }

  /**
   * 自动消费kafka内消息
   */
  private void consumerRecord(KafkaConsumer<String, byte[]> kafkaConsumer) {
    Set<TopicPartition> assignment = kafkaConsumer.assignment();
    while(assignment.size() == 0) {
      kafkaConsumer.poll(Duration.ofSeconds(10));
      assignment = kafkaConsumer.assignment();
    }
    Map<TopicPartition, Long> endOffsets = kafkaConsumer.endOffsets(assignment);
    for (TopicPartition topicPartition : assignment) {
      Long offset = endOffsets.get(topicPartition);
      if (offset.intValue() == 0) {
        continue;
      }
      kafkaConsumer.seek(topicPartition, offset.intValue()-1);
    }
  }

  /**
   * <p>
   * This method helps to get hostname.
   * </p>
   *
   * @return The host name.
   * @throws UnknownHostException if host name is invliad.
   */
  public static String getHostName() {
    try {
      return InetAddress.getLocalHost().getHostName();
    } catch (UnknownHostException e) {
      log.error("Failed to get hostname of self. [" + e.getMessage() + "]");
      return "host";
    }
  }
}
