/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.util.websocket.WebsocketUtils;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.RemoteControlVehicleResponseListener;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.vo.MiniMonitorResetAbnormalControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * This is a controller class to forward user's business logic to backend.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe when properties
 * parameters passed to it are used by the caller in thread safe manner.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/minimonitor/client/ws/control/{vehicleName}",configurator = ServletAwareConfigurator.class)
@Component
public class MiniMonitorControlWebSocketServer {

  /**
   * <p>
   * Used to record the current number of online connections
   * </p>
   */
  private static int onlineCount = 0;

  /**
   * <p>
   * Remote control service.
   * </p>
   */
  private static MiniMonitorCommandService miniMonitorCommandService;

    /*
     * 时间轮, 槽位数32个，每个2S,5min 保障在1圈以内
     */
    private static final HashedWheelTimer wheelTimer = new HashedWheelTimer(2, TimeUnit.SECONDS, 20);

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * <p>
   * Receive sessionid
   * </p>
   */
  private String sessionId = "";

  @Autowired
  public void setMiniMonitorRemoteControlService(MiniMonitorCommandService miniMonitorCommandService) {
    MiniMonitorControlWebSocketServer.miniMonitorCommandService = miniMonitorCommandService;
  }

  @OnOpen
  public void onOpen(Session session, @PathParam("vehicleName") String vehicleName) {
    this.sessionId = String.format("%s", session.getId());
    String userName = WebsocketUtils.getWebSocketUserName(session);
    if (StringUtils.isBlank(userName)) {
      log.error("mini-monior control websocket user {} absent", userName);
      try {
        session.close();
      } catch (IOException e) {
        log.info("mini-monitor remote control socket on error ioException {}", e);
      }
      return;
    }
    this.client.setUsername(userName);
    this.client.setSessionId(session.getId());
    this.client.setVehicleName(vehicleName);
    subscribeVehicleResponse(session, vehicleName);
    addOnlineCount();
    log.info(String.format("mini-monitor web socket on open user:{},vehicleName {}, user {}, sessionId:{},sersionNum:{}",
          sessionId, vehicleName, userName, session.getId(), getOnlineCount()));
  }

  @OnClose
  public void onClose(Session session) {
    this.sessionId = String.format("%s", session.getId());
    String vehicleName = this.client.getVehicleName();
    log.info("mini-monitor close control socket sessionId:{},message:{}", this.sessionId, vehicleName);
    subOnlineCount();
    unSubscribeVehicleResponse(vehicleName);
    wheelTimer.newTimeout(new TimerTask() {
          @Override
          public void run(Timeout timeout) throws Exception {
              String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
              RTopic rTopic = RedissonUtils.getRTopic(topicName);
              if (rTopic.countSubscribers() > 0) {
                  log.error("车辆遥控长链接关闭，监测有新链接遥控中");
              } else {
                  miniMonitorCommandService.manualRecoveryRequest(vehicleName, RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource());
              }
          }
      }, 10, TimeUnit.SECONDS);
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    log.info(String.format("mini-monitor web control socket on message sessionId:{},message:{}", session.getId(), message));
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    WsResult result = null;
    if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_MOBILE_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MonitorRemoteControlCommandVO remoteCommandVo = detailVO.getData();
      remoteCommandVo.setUserName(client.getUsername());
      remoteCommandVo.setCommandType("MOBILE");
      result = miniMonitorCommandService.postRemoteControlRequest(remoteCommandVo);
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MiniMonitorResetAbnormalControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MiniMonitorResetAbnormalControlCommandVO>>() {
      };
      WebsocketVO<MiniMonitorResetAbnormalControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MiniMonitorResetAbnormalControlCommandVO remoteCommandVo = detailVO.getData();
      result = miniMonitorCommandService.postResetAbnormalControlRequest(remoteCommandVo, client.getUsername());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_USER_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      this.client.setVehicleName(detailVO.getData().getVehicleName());
      result = miniMonitorCommandService.postUserControlRequest(detailVO.getData(), client.getUsername());
    }
    sendWebsocketData(session, result);
  }

  private void subscribeVehicleResponse(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      log.info("mini-monitor webSocket has subscribers session={}, topic={},subscribers={}", session.getId(), topicName, rTopic.countSubscribers());
      rTopic.removeAllListeners();
    }
    MessageListener messageListener = new RemoteControlVehicleResponseListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("mini-monitor webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
    }
    log.info("订阅车辆遥控响应数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeVehicleResponse(String vehicleName) {
    if (this.client.getEventListenerId() == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeListener(this.client.getEventListenerId());
    log.info("mini-monitor webSocket remove session {}, topic={},listenerId={}", this.sessionId, topicName, this.client.getEventListenerId());
  }

  /**
   * @param session
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("mini-monitor web socket on error:{},{}", session.getId(), throwable);
    throwable.printStackTrace();
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    log.info(String.format("mini-monitor web socket on close user:{},sessionId:{},sersionNum:{}", sessionId, session.getId(),
        getOnlineCount()));
    try {
      session.close();
    } catch (IOException e) {
      log.error("mini-monitor web socket on error ioException", e);
    }
  }

  public static synchronized int getOnlineCount() {
    return onlineCount;
  }

  public static synchronized void addOnlineCount() {
    MiniMonitorControlWebSocketServer.onlineCount++;
  }

  public static synchronized void subOnlineCount() {
    MiniMonitorControlWebSocketServer.onlineCount--;
  }

   /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    try {
      synchronized (session) {
        session.getBasicRemote().sendText(jsonStr);
      }
    } catch (Exception e) {
      log.error("mini-monitor control ws send result exception", e);
    }
  }

}
