/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.util.websocket.WebsocketUtils;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.ManualLocationResponseListener;
import com.jdx.rover.monitor.service.web.MonitorLocationService;
import com.jdx.rover.monitor.vo.MonitorRequestVehicleCloudMapCommandVO;
import com.jdx.rover.monitor.vo.MonitorResetVehiclePoseCommandVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.redisson.client.RedisTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 人工辅助定位websocket
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/location", configurator = ServletAwareConfigurator.class)
@Component
public class MonitorManualLocationWebSocketServer {

  private static MonitorLocationService locationService;

  @Autowired
  public void setMonitorLocationService(MonitorLocationService locationService) {
    MonitorManualLocationWebSocketServer.locationService = locationService;
  }

  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * 接收webSocket消息
   */
  @OnMessage
  public void onMessage(String message, Session session) {
    log.info("收到人工定位websocket消息{}", message);
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    this.client.setEventType(websocketVO.getEventType());
    try {
      if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.REQUEST_VEHICLE_CLOUD_MAP.getValue())) {
        TypeReference<WebsocketVO<MonitorRequestVehicleCloudMapCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRequestVehicleCloudMapCommandVO>>() {
        };
        WebsocketVO<MonitorRequestVehicleCloudMapCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
        MonitorRequestVehicleCloudMapCommandVO remoteCommandVo = detailVO.getData();
        remoteCommandVo.setUserName(client.getUsername());
        unSubscribeVehicleCloudMap(remoteCommandVo.getVehicleName());
        HttpResult result = locationService.requestVehicleMap(remoteCommandVo);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REQUEST_VEHICLE_CLOUD_MAP.getValue());
        subscribeVehicleCloudMap(session, remoteCommandVo.getVehicleName(), remoteCommandVo.getId());
        if (!HttpResult.isSuccess(result)) {
          wsResult = WsResult.error(WebsocketEventTypeEnum.REQUEST_VEHICLE_CLOUD_MAP.getValue(), result.getMessage());
          sendWebsocketData(session, wsResult);
          return;
        }
        this.client.setPoseListenerId(remoteCommandVo.getId());
        this.client.setVehicleName(remoteCommandVo.getVehicleName());
        sendWebsocketData(session, wsResult);
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.HEARTBEAT.getValue())) {
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_LOCATION_POSE.getValue())) {
        TypeReference<WebsocketVO<MonitorResetVehiclePoseCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorResetVehiclePoseCommandVO>>() {
        };
        WebsocketVO<MonitorResetVehiclePoseCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
        MonitorResetVehiclePoseCommandVO remoteCommandVo = detailVO.getData();
        remoteCommandVo.setUserName(client.getUsername());
        HttpResult result = locationService.resetVehicleLocationPose(remoteCommandVo);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_LOCATION_POSE.getValue());
        if (!HttpResult.isSuccess(result)) {
          wsResult = WsResult.error(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_LOCATION_POSE.getValue(), result.getMessage());
        }
        sendWebsocketData(session, wsResult);
      }
    } catch (RedisTimeoutException r) {
      log.error("Redisson处理超时错误,message={}", message, r);
      sendWebsocketData(session, WsResult.error(websocketVO.getEventType()));
      throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getMessage());
    } catch (Exception e) {
      log.error("人工辅助定位处理异常,message={}", message, e);
      sendWebsocketData(session, WsResult.error(websocketVO.getEventType()));
    }
  }

  /**
   * 打开连接
   *
   * @param session The session.
   */
  @OnOpen
  public void onOpen(Session session) {
    // 在线数加1
    onlineCount.incrementAndGet();
    String username = session.getRequestParameterMap().get("username").get(0);
    this.client.setUsername(username);
    this.client.setSessionId(session.getId());
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Opening session id with {}. Online count is {}. Ip is {}.", session.getId(), onlineCount.get(), clientIp);
  }

  /**
   * 订阅单车页数据
   */
  private void subscribeVehicleCloudMap(Session session, String vehicleName, Long requestId) {
    if (this.client.getEventListenerId() != null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_CLOUD_MAP_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new ManualLocationResponseListener(session, requestId);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
    }
    log.info("订阅车辆点云响应数据!{}", this.client);
  }

  /**
   * 取消订阅数据
   */
  private void unSubscribeVehicleCloudMap(String vehicleName) {
    if (this.client.getEventListenerId() == null) {
      return;
    }
    if (StringUtils.isBlank(this.client.getVehicleName())) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_CLOUD_MAP_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeListener(this.client.getEventListenerId());
    if (rTopic.countSubscribers() > 0) {
      rTopic.removeListener(this.client.getEventListenerId());
    }
    log.info("webSocket remove topic={},listenerId={}", topicName, this.client.getEventListenerId());
  }

  /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    synchronized (session) {
      try {
        session.getBasicRemote().sendText(jsonStr);
      } catch (IOException e) {
        throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
      }
    }
  }

  /**
   * 关闭连接
   *
   * @param session The session.
   */
  @OnClose
  public void onClose(Session session) {
    // 在线数减1
    onlineCount.decrementAndGet();
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Closing session with id {}. Online count is {}. Ip is {}.", session.getId(), onlineCount.get(), clientIp);
  }

  /**
   * 连接异常
   *
   * @param throwable The exception.
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.error("Logging exception from session {}.Ip is {}", session.getId(), clientIp, throwable);
    // TODO 权限不够,直接关闭webSocket
  }
}
