/*
 * Copyright (c) 2021 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.client.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
public class WebSocketConfig {
  @Value("${websocket.maxTextMessageBufferSize}")
  private Integer maxTextMessageBufferSize;
  @Value("${websocket.maxBinaryMessageBufferSize}")
  private Integer maxBinaryMessageBufferSize;
  @Value("${websocket.maxSessionIdleTimeout}")
  private Long maxSessionIdleTimeout;

  /**
   * 注入一个ServerEndpointExporter,该Bean会自动注册使用@ServerEndpoint注解申明的websocket endpoint
   */
  @Bean
  public ServerEndpointExporter serverEndpointExporter() {
    return new ServerEndpointExporter();
  }

  @Bean
  public ServletServerContainerFactoryBean createWebSocketContainer() {
    ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
    container.setMaxTextMessageBufferSize(maxTextMessageBufferSize);
    container.setMaxBinaryMessageBufferSize(maxBinaryMessageBufferSize);
    container.setMaxSessionIdleTimeout(maxSessionIdleTimeout);
    return container;
  }
}