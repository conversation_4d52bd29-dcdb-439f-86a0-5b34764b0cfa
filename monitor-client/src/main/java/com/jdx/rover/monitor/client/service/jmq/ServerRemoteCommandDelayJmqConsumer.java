/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.server.api.domain.dto.hermes.HermesVehicleControlCommandDelayInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收车辆指令延迟
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Service
@Slf4j
public class ServerRemoteCommandDelayJmqConsumer implements MessageListener {

  /**
   * 处理接收到的消息列表。
   * @param messages 消息列表。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理车辆指令延迟变化消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息并将其转发到指定的Redis主题。
   * @param message 要处理的消息，类型为String。
   */
  private void handleOneMessage(String message) {
    HermesVehicleControlCommandDelayInfoDTO delayInfoDto = JsonUtils.readValue(message, HermesVehicleControlCommandDelayInfoDTO.class);
    if (delayInfoDto == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + delayInfoDto.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_RESPONSE.getValue(), delayInfoDto);
      long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
      log.info("Success result {} send vehicle delay info to topic {}", result, topicName);
    } else {
      log.error("Error find topic {} listener", topicName);
      rTopic.removeAllListeners();
    }
  }
}
