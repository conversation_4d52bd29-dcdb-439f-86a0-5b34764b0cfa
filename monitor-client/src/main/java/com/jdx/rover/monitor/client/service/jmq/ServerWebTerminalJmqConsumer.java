/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.monitor.service.webterminal.WebTerminalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * web terminal消息接收Listener
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ServerWebTerminalJmqConsumer implements MessageListener {

  /**
   * 提供Web终端服务的实例。
   */
  private final WebTerminalService webTerminalService;

  /**
   * 处理来自远程工具的消息。
   * @param messages 消息列表。
   */
  @Override
  public void onMessage(List<Message> messages) throws Exception {
    for (Message message : messages) {
      log.info("Received topic={}, message={}", message.getTopic(), message.getText());
      if (StringUtils.isBlank(message.getText())) {
        continue;
      }
      try {
        handleOneMessage(message.getText());
      } catch (Exception e) {
        log.error("处理远程工具消息失败!{}", message.getText(), e);
      }
    }
  }

  /**
   * 处理单个消息
   * @param message 要处理的消息内容
   */
  private void handleOneMessage(String message) {
    webTerminalService.handleMessage(message);
  }
}
