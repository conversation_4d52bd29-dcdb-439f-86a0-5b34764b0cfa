/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.websocket;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.MonitorVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.util.websocket.WebsocketUtils;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.MapVehicleMessageListener;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.redisson.client.RedisTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 安卓屏平行驾驶websocket
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/remote/drive/{vehicleName}", configurator = ServletAwareConfigurator.class)
@Component
public class RemoteDriveWebSocketServer {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  private static MonitorMapInfoService mapInfoService;

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * 车号
   */
  private String vehicleName;

  @Autowired
  public void setMapInfoService(MonitorMapInfoService mapInfoService) {
    RemoteDriveWebSocketServer.mapInfoService = mapInfoService;
  }

  /**
   * 接收webSocket消息
   */
  @OnMessage
  public void onMessage(String message, Session session) {
    try {
      WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
      this.client.setEventType(websocketVO.getEventType());
       if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue())) {
        MonitorVehicleMapInfoDTO dto = mapInfoService.getVehicleRealtimePositionInfo(this.vehicleName);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue(), dto);
        sendWebsocketData(session, wsResult);
      }
    } catch (RedisTimeoutException r) {
      log.error("Redisson处理超时错误,message={}", message, r);
      sendWebsocketData(session, WsResult.error(""));
      throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getMessage());
    } catch (Exception e) {
      log.error("Realtime实时信息错误,message={}", message, e);
      sendWebsocketData(session, WsResult.error(""));
    }
  }

  /**
   * 打开ws链接
   * @param session
   * @param vehicleName
   */
  @OnOpen
  public void onOpen(Session session, @PathParam("vehicleName") String vehicleName) {
    this.vehicleName = vehicleName;
    addOnlineCount();
    log.info("Opening remote drive session id with {}. Online count is {}.", session.getId(), onlineCount.get());
    unSubscribeMapVehicle();
    subscribeMapVehicle(session, this.vehicleName);
  }

  /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    synchronized (session) {
      try {
        session.getBasicRemote().sendText(jsonStr);
      } catch (IOException e) {
        throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_MAPSCHEDULE.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_MAPSCHEDULE.getMessage());
      }
    }
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeTopic(String topicName, Integer listenerId) {
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (listenerId != null) {
      rTopic.removeListener(listenerId);
    }
    log.info("webSocket remove topic={},listenerId={},subscribers={}", topicName, listenerId, rTopic.countSubscribers());
  }

  /**
   * 订阅地图页车辆信息
   */
  private void subscribeMapVehicle(Session session, String vehicleName) {
    log.info("开始接收车辆地图页数据!{}", this.client);
    String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new MapVehicleMessageListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getMapListenerIds() == null) {
      Map<String, Integer> map = new HashMap<>();
      map.put(vehicleName, listenerId);
      this.client.setMapListenerIds(map);
    } else {
      this.client.getMapListenerIds().put(vehicleName, listenerId);
    }
    log.info("订阅车辆地图页数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeMapVehicle() {
    if (this.client.getMapListenerIds() == null) {
      return;
    }
    for (Map.Entry<String, Integer> entry : this.client.getMapListenerIds().entrySet()) {
      String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + entry.getKey();
      unSubscribeTopic(topicName, entry.getValue());
    }
  }

  /**
   * 关闭连接
   *
   * @param session The session.
   */
  @OnClose
  public void onClose(Session session) {
    // 在线数减1
    subOnlineCount();
    unSubscribeMapVehicle();
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Closing session with id {}. Online count is {}. Ip is {}.", session.getId(), onlineCount.get(), clientIp);
  }

  /**
   * 连接异常
   *
   * @param throwable The exception.
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.error("Logging exception from session {}.Ip is {}", session.getId(), clientIp, throwable);
    // TODO 权限不够,直接关闭webSocket
  }

  public static synchronized int addOnlineCount() {
    return onlineCount.incrementAndGet();
  }

  public static synchronized int subOnlineCount() {
    return onlineCount.decrementAndGet();
  }
}
