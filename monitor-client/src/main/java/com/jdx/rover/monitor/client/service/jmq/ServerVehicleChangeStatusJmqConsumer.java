/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.service.jmq;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.vehicle.VehicleGpsManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.VehicleStatusRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.server.api.domain.dto.status.ChangeStatusDTO;
import com.jdx.rover.server.api.domain.enums.guardian.VehicleStateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 接收车辆状态变更信息
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerVehicleChangeStatusJmqConsumer implements MessageListener {

    private final VehicleGpsManager vehicleGpsManager;
    private final VehicleStatusRepository vehicleStatusRepository;
    private final TrackingEventCollectService trackingEventCollectService;

    /**
     * 处理接收到的消息列表。
     * @param messages 消息列表，包含多个Message对象。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理车辆状态变化消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息并更新相应的车辆状态。
     * @param message JSON格式的消息，包含车辆名称、更改类型和新值。
     */
    public void handleOneMessage(String message) {
        ChangeStatusDTO dto = JsonUtils.readValue(message, ChangeStatusDTO.class);
        ChangeStatusDTO.ChangeTypeEnum changeTypeEnum = ChangeStatusDTO.ChangeTypeEnum.of(dto.getChangeType());
        if (Objects.isNull(changeTypeEnum)) {
            return;
        }
        switch (changeTypeEnum) {
            case VEHICLE_STATE: {
                // 切换手柄控制
                if (VehicleStateEnum.LOCAL_CONTROL == VehicleStateEnum.valueOf(dto.getNewValue())) {
                    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + dto.getVehicleName();
                    RTopic rTopic = RedissonUtils.getRTopic(topicName);
                    if (rTopic.countSubscribers() > 0) {
                        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.CHANGE_VEHICLE_STATE_EVENT.getValue(), dto.getNewValue());
                        long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
                        log.info("Success result {} send vehicle mobile control to topic {}", result, topicName);
                    } else {
                        log.error("Error find topic {} listener", topicName);
                    }
                }
            }
            break;

            default:
                log.info("未匹配的消息{}", dto);
        }
    }
}
