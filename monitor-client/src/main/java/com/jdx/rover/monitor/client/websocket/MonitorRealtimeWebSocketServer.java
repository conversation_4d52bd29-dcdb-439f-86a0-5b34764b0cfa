/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.MonitorVehicleMapInfoDTO;
import com.jdx.rover.monitor.dto.issue.IssuePendingDTO;
import com.jdx.rover.monitor.dto.vehicle.*;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.issue.IssueStateEnum;
import com.jdx.rover.monitor.enums.redis.RedisKeyEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.vehicle.single.SingleVehicleTabTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.util.websocket.WebsocketUtils;
import com.jdx.rover.monitor.manager.vehicle.AlarmEventManager;
import com.jdx.rover.monitor.manager.vehicle.PowerManagerManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.manager.vehicle.VehicleGpsManager;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.ChangeAlarmEventMessageListener;
import com.jdx.rover.monitor.service.listener.redis.IssueRecordChangeMessageListener;
import com.jdx.rover.monitor.service.listener.redis.MapVehicleMessageListener;
import com.jdx.rover.monitor.service.listener.redis.SingleVehicleMessageListener;
import com.jdx.rover.monitor.service.vehicle.MultiVehicleService;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import com.jdx.rover.monitor.service.web.MonitorIssueService;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.service.webterminal.WebTerminalService;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.MonitorIssueChangeRequestVO;
import com.jdx.rover.monitor.vo.MonitorStationVehicleMapInfoRequestVO;
import com.jdx.rover.monitor.vo.vehicle.MultiVehicleRealtimeVO;
import com.jdx.rover.monitor.vo.vehicle.SingleVehicleVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.redisson.client.RedisTimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 单车页面websocket
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/realtime", configurator = ServletAwareConfigurator.class)
@Component
public class MonitorRealtimeWebSocketServer {
  /**
   * 记录当前在线连接数
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  private static MultiVehicleService multiVehicleService;

  private static SingleVehicleManager singleVehicleManager;

  private static SingleVehicleService singleVehicleService;

  private static AlarmEventManager alarmEventManager;

  private static MonitorMapInfoService mapInfoService;

  private static MonitorIssueService issueService;

  private static PowerManagerManager powerManagerManager;

  private static WebTerminalService webTerminalService;

  private static VehicleGpsManager vehicleGpsManager;

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  @Autowired
  public void setSingleVehicleManager(SingleVehicleManager singleVehicleManager) {
    MonitorRealtimeWebSocketServer.singleVehicleManager = singleVehicleManager;
  }

  @Autowired
  public void setSingleVehicleService(SingleVehicleService singleVehicleService) {
    MonitorRealtimeWebSocketServer.singleVehicleService = singleVehicleService;
  }

  @Autowired
  public void setMultiVehicleService(MultiVehicleService multiVehicleService) {
    MonitorRealtimeWebSocketServer.multiVehicleService = multiVehicleService;
  }

  @Autowired
  public void setAlarmEventService(AlarmEventManager alarmEventManager) {
    MonitorRealtimeWebSocketServer.alarmEventManager = alarmEventManager;
  }

  @Autowired
  public void setAlarmEventService(MonitorMapInfoService mapInfoService) {
    MonitorRealtimeWebSocketServer.mapInfoService = mapInfoService;
  }

  @Autowired
  public void setIssueService(MonitorIssueService issueService) {
    MonitorRealtimeWebSocketServer.issueService = issueService;
  }
  @Autowired
  public void setPowerManagerManager(PowerManagerManager powerManagerManager) {
    MonitorRealtimeWebSocketServer.powerManagerManager = powerManagerManager;
  }
  @Autowired
  public void setWebTerminalService(WebTerminalService webTerminalService) {
    MonitorRealtimeWebSocketServer.webTerminalService = webTerminalService;
  }
  @Autowired
  public void setVehicleGpsManager(VehicleGpsManager vehicleGpsManager) {
    MonitorRealtimeWebSocketServer.vehicleGpsManager = vehicleGpsManager;
  }

  /**
   * 接收webSocket消息
   */
  @OnMessage
  public void onMessage(String message, Session session) {
    try {
      WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
      this.client.setEventType(websocketVO.getEventType());
      if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.MULTI_VEHICLE.getValue())) {
        this.client.setSingleVehicleTabType(null);
        TypeReference<WebsocketVO<MultiVehicleRealtimeVO>> typeReference = new TypeReference<WebsocketVO<MultiVehicleRealtimeVO>>() {
        };
        WebsocketVO<MultiVehicleRealtimeVO> detailVO = JsonUtils.readValue(message, typeReference);
        detailVO.getData().setUsername(this.client.getUsername());
        WsResult<MultiVehicleRealtimeDTO> dto = multiVehicleService.listMultiVehicleWs(detailVO.getData());
        sendWebsocketData(session, dto);
        if (this.client.getEventListenerId() != null) {
          unSubscribeSingleVehicle();
        }
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.SINGLE_VEHICLE.getValue())) {
        TypeReference<WebsocketVO<SingleVehicleVO>> typeReference = new TypeReference<WebsocketVO<SingleVehicleVO>>() {
        };
        WebsocketVO<SingleVehicleVO> detailVO = JsonUtils.readValue(message, typeReference);
        String tabType = detailVO.getData().getTabType();
        if (StringUtils.isBlank(tabType)) {
          tabType = SingleVehicleTabTypeEnum.DRIVABLE.getValue();
        }
        this.client.setSingleVehicleTabType(tabType);

        String vehicleName = detailVO.getData().getVehicleName();
        this.client.setVehicleName(vehicleName);
        log.info("开始接收单车数据!vehicleName={}", vehicleName);
        // 立即发送初始化数据
        initSingleVehicleData(session, vehicleName);
        subscribeSingleVehicle(session, vehicleName);
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.STOP.getValue())) {
        this.client.setSingleVehicleTabType(null);
        unSubscribeSingleVehicle();
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.STOP.getValue());
        sendWebsocketData(session, wsResult);
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_REQUEST.getValue())) {
        TypeReference<WebsocketVO<MonitorStationVehicleMapInfoRequestVO>> typeReference = new TypeReference<WebsocketVO<MonitorStationVehicleMapInfoRequestVO>>() {
        };
        WebsocketVO<MonitorStationVehicleMapInfoRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
        unSubscribeMapVehicle();
        subscribeMapVehicle(session, detailVO.getData().getVehicleName());
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.MAP_VEHICLE_SCHEDULE_CANCEL_REQUEST.getValue())) {
        unSubscribeMapVehicle();
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue())) {
        TypeReference<WebsocketVO<MonitorStationVehicleMapInfoRequestVO>> typeReference = new TypeReference<WebsocketVO<MonitorStationVehicleMapInfoRequestVO>>() {
        };
        WebsocketVO<MonitorStationVehicleMapInfoRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
        Map<String, MonitorVehicleMapInfoDTO> dto = mapInfoService.getVehicleRealtimePositionInfo(detailVO.getData());
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.MAP_VEHICLE_POSITION_REQUEST.getValue(), dto);
        sendWebsocketData(session, wsResult);
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.ISSUE_SUBSCRIBE_REQUEST.getValue())) {
        TypeReference<WebsocketVO<MonitorIssueChangeRequestVO>> typeReference = new TypeReference<WebsocketVO<MonitorIssueChangeRequestVO>>() {
        };
        WebsocketVO<MonitorIssueChangeRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
        MonitorIssueChangeRequestVO requestVo = detailVO.getData();
        IssueRecordListRequestVO issueRecordListRequestVo = new IssueRecordListRequestVO();
        issueRecordListRequestVo.setStateList(Lists.newArrayList(IssueStateEnum.WAITING.getIssueState(), IssueStateEnum.PROCESS.getIssueState()));
        IssuePendingDTO dto = issueService.listPendingIssue(issueRecordListRequestVo, client.getUsername());
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.ISSUE_SUBSCRIBE_REQUEST.getValue(), dto);
        sendWebsocketData(session, wsResult);
        subscribeIssueRecord(session, requestVo);
      } else if (StringUtils.equals(websocketVO.getEventType(),WebsocketEventTypeEnum.ISSUE_UNSUBSCRIBE_REQUEST.getValue())) {
        unsubscribeIssueRecord();
      } else if (StringUtils.equals(websocketVO.getEventType(),WebsocketEventTypeEnum.REMOTE_WEB_TERMINAL_TOOL.getValue())) {
        TypeReference<WebsocketVO<SingleVehicleVO>> typeReference = new TypeReference<WebsocketVO<SingleVehicleVO>>() {
        };
        WebsocketVO<SingleVehicleVO> detailVO = JsonUtils.readValue(message, typeReference);
        WsResult wsResult = WsResult.success(WebsocketEventTypeEnum.REMOTE_WEB_TERMINAL_TOOL.getValue(), webTerminalService.getWebTerminalList(detailVO.getData().getVehicleName()));
        sendWebsocketData(session, wsResult);
      } else if (StringUtils.equals(websocketVO.getEventType(), WebsocketEventTypeEnum.WEB_TERMINAL_COMMAND_DATA.getValue())) {
        TypeReference<WebsocketVO<WebTerminalRequestVO>> typeReference = new TypeReference<WebsocketVO<WebTerminalRequestVO>>() {
        };
        WebsocketVO<WebTerminalRequestVO> detailVO = JsonUtils.readValue(message, typeReference);
        webTerminalService.sendTransferStatusToVehicle(detailVO.getData(), this.client);
      }
    } catch (RedisTimeoutException r) {
      log.error("Redisson处理超时错误,message={}", message, r);
      sendWebsocketData(session, WsResult.error(""));
      throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_REDIS_TIMEOUT.getMessage());
    } catch (Exception e) {
      log.error("Realtime实时信息错误,message={}", message, e);
      sendWebsocketData(session, WsResult.error(""));
    }
  }

  /**
   * 打开连接
   *
   * @param session The session.
   */
  @OnOpen
  public void onOpen(Session session) {
    // 在线数加1
    onlineCount.incrementAndGet();
    String username = session.getRequestParameterMap().get("username").get(0);
    this.client.setUsername(username);
    this.client.setSessionId(session.getId());
    subscribeAlarmEvent(session, username);
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Opening session id with {}. Online count is {}. Ip is {}.", session.getId(), onlineCount.get(), clientIp);
  }

  /**
   * 订阅单车页数据
   */
  private void subscribeSingleVehicle(Session session, String vehicleName) {
    if (this.client.getEventListenerId() != null) {
      return;
    }
    subscribeSingleVehicle(session);
    RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(RedisKeyEnum.SORTED_SET_PUSH_SINGLE_VEHICLE.getValue());
    scoredSortedSet.add(System.currentTimeMillis(), vehicleName);
  }

  /**
   * 初始化响应数据
   *
   * @param session
   * @param vehicleName
   */
  private void initSingleVehicleData(Session session, String vehicleName) {
    // 立即发送初始化数据
    WsResult<SingleVehicleDTO> wsResult = singleVehicleService.listSingleVehicle(vehicleName);
    sendWebsocketData(session, wsResult);

    WsResult<SingleVehicleTakeOverAndIssueDTO> takeOverAndIssueResult = singleVehicleManager.getTakeOverAndIssue(vehicleName);
    sendWebsocketData(session, takeOverAndIssueResult);

    WsResult<SingleVehicleScheduleDTO> scheduleResult = singleVehicleService.listSingleVehicleSchedule(vehicleName);
    sendWebsocketData(session, scheduleResult);

    if (Objects.equals(this.client.getSingleVehicleTabType(), SingleVehicleTabTypeEnum.EXCEPTION.getValue())) {
      WsResult<VehicleNameAndDataListDTO<SingleVehicleExceptionDTO>> alarmResult = singleVehicleManager.listSingleVehicleException(vehicleName);
      sendWebsocketData(session, alarmResult);
    } else if (Objects.equals(this.client.getSingleVehicleTabType(), SingleVehicleTabTypeEnum.OPERATION.getValue())) {
      WsResult<VehicleNameAndDataListDTO<SingleVehicleOperationDTO>> operationResult =
          singleVehicleManager.listSingleVehicleOperation(vehicleName);
      sendWebsocketData(session, operationResult);
    }

    WsResult<VehicleNameAndDataListDTO<SingleVehicleAccidentDTO>> accidentResult = singleVehicleManager.listSingleVehicleAccident(vehicleName);
    sendWebsocketData(session, accidentResult);

    WsResult<SingleVehiclePncTaskAndTrafficLightDTO> trafficLightResult = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_TRAFFIC_LIGHT.getValue());
    WsResult<SingleVehicleNoSignalIntersectionDTO> noSignalIntersection = WsResult.success(WebsocketEventTypeEnum.SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION.getValue());
    if (takeOverAndIssueResult.getData() != null && takeOverAndIssueResult.getData().getTakeOverUser() != null) {
      trafficLightResult = singleVehicleManager.listPncTaskAndTrafficLight(vehicleName);
      noSignalIntersection = singleVehicleManager.buildNoSignalIntersection(vehicleName);
    }
    sendWebsocketData(session, trafficLightResult);
    sendWebsocketData(session, noSignalIntersection);

    WsResult<SingleVehiclePowerManagerDTO> powerManagerResult = powerManagerManager.getPowerManager(vehicleName);
    if (powerManagerResult != null) {
      sendWebsocketData(session, powerManagerResult);
    }
    WsResult<Map<String, Object>> gpsResult = vehicleGpsManager.getGps(vehicleName);
    if (gpsResult != null) {
      sendWebsocketData(session, gpsResult);
    }
  }

  /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    synchronized (session) {
      try {
        session.getBasicRemote().sendText(jsonStr);
      } catch (IOException e) {
        throw new AppException(MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getCode(), MonitorErrorEnum.CLIENT_WEBSOCKET_SEND_REALTIME.getMessage());
      }
    }
  }

  /**
   * 订阅告警据
   */
  private void subscribeAlarmEvent(Session session, String username) {
    if (StringUtils.isBlank(username)) {
      return;
    }
    WsResult<List<AlarmEventRealtimeDTO>> wsResult = alarmEventManager.listAlarmEventWs(username);
    sendWebsocketData(session, wsResult);

    String topicName = RedisTopicEnum.ALARM_EVENT_PREFIX.getValue();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new ChangeAlarmEventMessageListener(session, this.client);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={},username={}", topicName, rTopic.countListeners(), rTopic.countSubscribers(),username);
    this.client.setAlarmListenerId(listenerId);
  }

  /**
   * 订阅告警据
   */
  private void subscribeSingleVehicle(Session session) {
    String topicName = RedisTopicEnum.SINGLE_VEHICLE_PREFIX.getValue() + this.client.getVehicleName();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new SingleVehicleMessageListener(session, this.client);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={}", topicName, rTopic.countListeners());

    this.client.setEventListenerId(listenerId);
    log.info("订阅车辆数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeSingleVehicle() {
    Integer listenerId = this.client.getEventListenerId();
    if (listenerId != null) {
      String topicName = RedisTopicEnum.SINGLE_VEHICLE_PREFIX.getValue() + this.client.getVehicleName();
      unSubscribeTopic(topicName, this.client.getEventListenerId());
      this.client.setEventListenerId(null);
      this.client.setVehicleName(null);

      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      if (rTopic.countSubscribers() <= 0) {
        RScoredSortedSet<String> scoredSortedSet = RedissonUtils.getRedissonClient().getScoredSortedSet(RedisKeyEnum.SORTED_SET_PUSH_SINGLE_VEHICLE.getValue());
        scoredSortedSet.remove(this.client.getVehicleName());
      }
    }
  }

  /**
   * 订阅工单数据
   */
  private void subscribeIssueRecord(Session session, MonitorIssueChangeRequestVO requestVo) {
    if (this.client.getIssueListenerId() != null) {
      log.info("工单已经订阅");
      return;
    }
    String topicName = RedisTopicEnum.ISSUE_EVENT_PREFIX.getValue();
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new IssueRecordChangeMessageListener(session, this.client.getUsername());
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    this.client.setIssueListenerId(listenerId);
  }

  /**
   * 取消订阅工单数据
   */
  private void unsubscribeIssueRecord() {
    String topicName = RedisTopicEnum.ISSUE_EVENT_PREFIX.getValue();
    if (this.client.getIssueListenerId() != null) {
      unSubscribeTopic(topicName, this.client.getIssueListenerId());
    }
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeAlarmEvent() {
    Integer alarmListenerId = this.client.getAlarmListenerId();
    if (alarmListenerId != null) {
      String topicName = RedisTopicEnum.ALARM_EVENT_PREFIX.getValue();
      unSubscribeTopic(topicName, this.client.getAlarmListenerId());
    }
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeTopic(String topicName, Integer listenerId) {
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (listenerId != null) {
      rTopic.removeListener(listenerId);
    }
    log.info("webSocket remove topic={},listenerId={},subscribers={}", topicName, listenerId, rTopic.countSubscribers());
  }

  /**
   * 订阅地图页车辆信息
   */
  private void subscribeMapVehicle(Session session, List<String> vehicleName) {
    log.info("开始接收车辆地图页数据!{}", this.client);
//    if (vehicleName.size() == 1) {
//      String positionTopicName = RedisTopicEnum.MAP_VEHICLE_POSITION_PREFIX.getValue() + this.client.getVehicleName();
//      RTopic rTopic = RedissonUtils.getRTopic(positionTopicName);
//      int listenerId = rTopic.addListener(String.class, new MapVehiclePositionMessageListener(session));
//      this.client.setPositionListenerId(listenerId);
//    }
    for (String vehicle : vehicleName) {
      String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + vehicle;
      RTopic rTopic = RedissonUtils.getRTopic(topicName);
      MessageListener messageListener = new MapVehicleMessageListener(session);
      int listenerId = rTopic.addListener(String.class, messageListener);
      log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
      if (this.client.getMapListenerIds() == null) {
        Map<String, Integer> map = new HashMap<>();
        map.put(vehicle, listenerId);
        this.client.setMapListenerIds(map);
      } else {
        this.client.getMapListenerIds().put(vehicle, listenerId);
      }
    }
    log.info("订阅车辆地图页数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeMapVehicle() {
//    if (client.getPositionListenerId() != null) {
//      String topicName = RedisTopicEnum.MAP_VEHICLE_POSITION_PREFIX.getValue() + client.getVehicleName();
//      unSubscribeTopic(topicName, client.getPositionListenerId());
//    }
    if (this.client.getMapListenerIds() == null) {
      return;
    }
    for (Map.Entry<String, Integer> entry : this.client.getMapListenerIds().entrySet()) {
      String topicName = RedisTopicEnum.MAP_VEHICLE_SCHEDULE_PREFIX.getValue() + entry.getKey();
      unSubscribeTopic(topicName, entry.getValue());
    }
  }

  /*
   * 关闭连接
   *
   * @param session The session.
   */
  @OnClose
  public void onClose(Session session) {
    // 在线数减1
    onlineCount.decrementAndGet();
    unSubscribeSingleVehicle();
    unSubscribeAlarmEvent();
    unSubscribeMapVehicle();
    unsubscribeIssueRecord();
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.info("Closing session with id {}. Online count is {}. Ip is {}.", session.getId(), onlineCount.get(), clientIp);
  }

  /**
   * 连接异常
   *
   * @param throwable The exception.
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    String clientIp = WebsocketUtils.getWebSocketIp(session);
    log.error("Logging exception from session {}.Ip is {}", session.getId(), clientIp, throwable);
    // TODO 权限不够,直接关闭webSocket
  }
}
