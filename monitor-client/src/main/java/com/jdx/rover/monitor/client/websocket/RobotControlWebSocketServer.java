/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.RemoteControlVehicleResponseListener;
import com.jdx.rover.monitor.service.robot.RobotCommandService;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <p>
 *  机器人远程遥控
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/robot/ws/control")
@Component
public class RobotControlWebSocketServer {

  /**
   * <p>
   * Used to record the current number of online connections
   * </p>
   */
  private static int onlineCount = 0;

  /**
   * <p>
   * 远遥服务
   * </p>
   */
  private static RobotCommandService robotCommandService;

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * <p>
   * Receive sessionid
   * </p>
   */
  private String sessionId = "";

  @Autowired
  public void setMonitorRemoteControlService(RobotCommandService robotCommandService) {
    RobotControlWebSocketServer.robotCommandService = robotCommandService;
  }

  @OnOpen
  public void onOpen(Session session) {
    this.sessionId = session.getId();
    String userName = session.getRequestParameterMap().get("userName").get(0);
    String productKey = session.getRequestParameterMap().get("productKey").get(0);
    String vehicleName = session.getRequestParameterMap().get("vehicleName").get(0);
    if (StringUtils.isAnyBlank(userName, productKey, vehicleName)) {
      log.error("robot control websocket request param absent.");
      try {
        session.close();
      } catch (IOException e) {
        log.info("robot control socket on error ioException.", e);
      }
      return;
    }
    this.client.setVehicleName(vehicleName);
    this.client.setProductKey(productKey);
    this.client.setUsername(userName);
    this.client.setSessionId(session.getId());
    checkRemoteControlAbsent(session, vehicleName);
    subscribeVehicleResponse(session, vehicleName);
    addOnlineCount();
    log.info("robot control socket on open session:{}, user {}", session.getId(), userName);
  }

  @OnClose
  public void onClose(Session session) {
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    String vehicleName = this.client.getVehicleName();
    String productKey = this.client.getProductKey();
    unSubscribeVehicleResponse(vehicleName);
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    log.info(String.format("robot control socket on message sessionId:%s,message:%s", session.getId(), message));
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    WsResult result = null;
    if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue())) {
      // 遥控指令下发
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MonitorRemoteControlCommandVO remoteCommandVo = detailVO.getData();
      remoteCommandVo.setUserName(client.getUsername());
      this.client.setVehicleName(remoteCommandVo.getVehicleName());
      result = robotCommandService.remoteControl(remoteCommandVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }
    sendWebsocketData(session, result);

  }

  private void subscribeVehicleResponse(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new RemoteControlVehicleResponseListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("monitor robot control websocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
    }
    log.info("订阅车辆遥控响应数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeVehicleResponse(String vehicleName) {
    if (this.client.getEventListenerId() == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeListener(this.client.getEventListenerId());
    if (rTopic.countSubscribers() > 0) {
      rTopic.removeListener(this.client.getEventListenerId());
    }
    log.info("monitor robot control websocket remove topic={},listenerId={}", topicName, this.client.getEventListenerId());
  }

  /**
   * @param session
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("monitor robot control socket on error:{},{}", session.getId(), throwable);
    throwable.printStackTrace();
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    log.info(String.format("monitor robot control socket on close user:%s,sessionId:%s,sersionNum:%s", sessionId, session.getId(),
        getOnlineCount()));
    try {
      session.close();
    } catch (IOException e) {
      log.error("monitor web socket on error ioException {}", e);
    }
  }

  /**
   * 检查指定车辆是否处于远程控制状态，若存在订阅者则发送错误消息
   * @param session 当前会话对象
   * @param vehicleName 待检查的车辆名称
   */
  private void checkRemoteControlAbsent(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      WsResult wsResult = WsResult.error(WebsocketEventTypeEnum.REMOTE_CONTROL_SESSION_CLOSE.getValue(),
              HttpCodeEnum.FORBIDDEN.getValue(), MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getMessage());
      long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
      log.info("Remote control webSocket subscribers {} count {} error, result {}", vehicleName, rTopic.countSubscribers(), result);
    }
  }

  public static synchronized int getOnlineCount() {
    return onlineCount;
  }

  public static synchronized void addOnlineCount() {
    RobotControlWebSocketServer.onlineCount++;
  }

  public static synchronized void subOnlineCount() {
    RobotControlWebSocketServer.onlineCount--;
  }

   /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    try {
      synchronized (session) {
        session.getBasicRemote().sendText(jsonStr);
      }
    } catch (Exception e) {
      log.error("Monitor control ws send result exception", e);
    }
  }

}
