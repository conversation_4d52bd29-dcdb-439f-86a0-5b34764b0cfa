/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.config;

import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;
import jakarta.websocket.server.ServerEndpointConfig.Configurator;
import java.lang.reflect.Field;

/**
 * webSocket获取IP
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class ServletAwareConfigurator extends Configurator {
  /**
   * IP常量
   */
  private static final String CLIENT_IP_KEY = "CLIENT_IP";

  /**
   * 获取IP,放入常量CLIENT_IP字段
   */
  @Override
  public void modifyHandshake(ServerEndpointConfig config, HandshakeRequest request, HandshakeResponse response) {
    HttpServletRequest httpservletRequest = getField(request, HttpServletRequest.class);
    String clientIP = httpservletRequest.getRemoteAddr();
    config.getUserProperties().put(CLIENT_IP_KEY, clientIP);
    String vehicleName = httpservletRequest.getHeader("vehicleName");
    String userName = httpservletRequest.getHeader("Rover-Username");
    if (vehicleName != null) {
      config.getUserProperties().put("vehicleName", vehicleName);
    }
    if (userName != null) {
      config.getUserProperties().put("userName", userName);
    }
  }

  /**
   * <p>
   * The method is hacking reflector to expose fields.
   * </p>
   *
   * @param <I>       The instance class
   * @param <F>       The field type class
   * @param instance  The instance.
   * @param fieldType The type for field.
   * @return The fieldType.
   */
  private static <I, F> F getField(I instance, Class<F> fieldType) {
    try {
      for (Class<?> type = instance.getClass(); type != Object.class; type = type.getSuperclass()) {
        for (Field field : type.getDeclaredFields()) {
          if (fieldType.isAssignableFrom(field.getType())) {
            field.setAccessible(true);
            return (F) field.get(instance);
          }
        }
      }
    } catch (IllegalArgumentException | IllegalAccessException e) {
      log.error("Have no access to define the specified class, field, method or constructor.", e);
      throw new RuntimeException("Have no access to define the specified class, field, method or constructor.", e);
    } catch (SecurityException | ClassCastException e) {
      log.error("Fail to convert instance to F class.", e);
      throw new RuntimeException("Fail to convert instance to F class.", e);
    }
    return null;
  }
}
