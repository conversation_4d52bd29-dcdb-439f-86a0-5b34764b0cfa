/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.dto.MonitorVehicleEnvEnableDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.RemoteControlVehicleResponseListener;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.service.web.MonitorVehicleRealtimeInfoService;
import com.jdx.rover.monitor.vo.MonitorQuitRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteControlCommandVO;
import com.jdx.rover.monitor.vo.MonitorResetAbnormalControlCommandVO;
import com.jdx.rover.monitor.vo.websocket.WebsocketVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;

/**
 * <p>
 * This is a controller class to forward user's business logic to backend.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe when properties
 * parameters passed to it are used by the caller in thread safe manner.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/control/{vehicleName}")
@Component
public class MonitorControlWebSocketServer {

  /**
   * <p>
   * Used to record the current number of online connections
   * </p>
   */
  private static int onlineCount = 0;

  /**
   * <p>
   * Remote control service.
   * </p>
   */
  private static MonitorRemoteCommandService monitorRemoteControlService;

  private static MonitorVehicleRealtimeInfoService monitorVehicleRealtimeInfoService;

  /**
   * Websocket客户端
   */
  private WebsocketClientBO client = new WebsocketClientBO();

  /**
   * <p>
   * Receive sessionid
   * </p>
   */
  private String sessionId = "";

  @Autowired
  public void setMonitorRemoteControlService(MonitorRemoteCommandService monitorRemoteControlService) {
    MonitorControlWebSocketServer.monitorRemoteControlService = monitorRemoteControlService;
  }

  @Autowired
  public void setMonitorRealtimeService(MonitorVehicleRealtimeInfoService monitorVehicleRealtimeInfoService) {
    MonitorControlWebSocketServer.monitorVehicleRealtimeInfoService = monitorVehicleRealtimeInfoService;
  }
  @OnOpen
  public void onOpen(Session session, @PathParam("vehicleName") String vehicleName) {
    this.sessionId = String.format("%s", session.getId());
    String username = session.getRequestParameterMap().get("username").get(0);
    this.client.setUsername(username);
    this.client.setSessionId(session.getId());
    this.client.setVehicleName(vehicleName);
    checkRemoteControlAbsent(session, vehicleName);
    subscribeVehicleResponse(session, vehicleName);
    addOnlineCount();
    log.info(String.format("monitor web socket on open user:%s,vehicleName %s, user %s, sessionId:%s,sersionNum:%s",
          sessionId, vehicleName, username, session.getId(), getOnlineCount()));
  }

  @OnClose
  public void onClose(Session session) {
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    unSubscribeVehicleResponse(this.client.getVehicleName());
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    log.info(String.format("monitor web control socket on message sessionId:%s,message:%s", session.getId(), message));
    WebsocketVO websocketVO = JsonUtils.readValue(message, WebsocketVO.class);
    WsResult result = null;
    if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_REMOTE_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MonitorRemoteControlCommandVO remoteCommandVo = detailVO.getData();
      remoteCommandVo.setUserName(client.getUsername());
      result = monitorRemoteControlService.postRemoteControlRequest(remoteCommandVo);
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_RESET_ABNORMAL_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorResetAbnormalControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorResetAbnormalControlCommandVO>>() {
      };
      WebsocketVO<MonitorResetAbnormalControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      MonitorResetAbnormalControlCommandVO remoteCommandVo = detailVO.getData();
      result = monitorRemoteControlService.postResetAbnormalControlRequest(remoteCommandVo, client.getUsername());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_QUIT_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorQuitRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorQuitRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorQuitRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      unSubscribeVehicleResponse(detailVO.getData().getVehicleName());
      result = WsResult.success("Quit Success");
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_USER_CONTROL.getValue())) {
      TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>> typeReference = new TypeReference<WebsocketVO<MonitorRemoteControlCommandVO>>() {
      };
      WebsocketVO<MonitorRemoteControlCommandVO> detailVO = JsonUtils.readValue(message, typeReference);
      result = monitorRemoteControlService.postUserControlRequest(detailVO.getData(), client.getUsername());
    } else if (StringUtils.equals(websocketVO.getEventType(),
            WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_ENV.getValue())) {
      MonitorVehicleEnvEnableDTO dto = monitorVehicleRealtimeInfoService.getVehicleEnvEnable(this.client.getVehicleName());
      result = WsResult.success(WebsocketEventTypeEnum.REMOTE_REQUEST_VEHICLE_ENV.getValue(),dto);
    }
    sendWebsocketData(session, result);

  }

  private void checkRemoteControlAbsent(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    if (rTopic.countSubscribers() > 0) {
      WsResult wsResult = WsResult.error(WebsocketEventTypeEnum.REMOTE_CONTROL_SESSION_CLOSE.getValue(),
              HttpCodeEnum.FORBIDDEN.getValue(), MonitorErrorEnum.ERROR_VEHICLE_WITHIN_CONTROL.getMessage());
      long result = rTopic.publish(JsonUtils.writeValueAsString(wsResult));
      log.info("Remote control webSocket subscribers {} count {} error, result {}", vehicleName, rTopic.countSubscribers(), result);
    }
  }

  private void subscribeVehicleResponse(Session session, String vehicleName) {
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    MessageListener messageListener = new RemoteControlVehicleResponseListener(session);
    int listenerId = rTopic.addListener(String.class, messageListener);
    log.info("webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
    if (this.client.getEventListenerId() == null) {
      this.client.setEventListenerId(listenerId);
    }
    log.info("订阅车辆遥控响应数据!{}", this.client);
  }

  /**
   * 取消订阅单车页数据
   */
  private void unSubscribeVehicleResponse(String vehicleName) {
    if (this.client.getEventListenerId() == null) {
      return;
    }
    String topicName = RedisTopicEnum.REMOTE_CONTROL_VEHICLE_RESPONSE_PREFIX.getValue() + vehicleName;
    RTopic rTopic = RedissonUtils.getRTopic(topicName);
    rTopic.removeListener(this.client.getEventListenerId());
    if (rTopic.countSubscribers() > 0) {
      rTopic.removeListener(this.client.getEventListenerId());
    }
    log.info("webSocket remove topic={},listenerId={}", topicName, this.client.getEventListenerId());
  }

  /**
   * @param session
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("monitor web socket on error:{},{}", session.getId(), throwable);
    throwable.printStackTrace();
    this.sessionId = String.format("%s", session.getId());
    subOnlineCount();
    log.info(String.format("monitor web socket on close user:%s,sessionId:%s,sersionNum:%s", sessionId, session.getId(),
        getOnlineCount()));
    try {
      session.close();
    } catch (IOException e) {
      log.error("monitor web socket on error ioException {}", e);
    }
  }

  public static synchronized int getOnlineCount() {
    return onlineCount;
  }

  public static synchronized void addOnlineCount() {
    MonitorControlWebSocketServer.onlineCount++;
  }

  public static synchronized void subOnlineCount() {
    MonitorControlWebSocketServer.onlineCount--;
  }

   /**
   * 发送数据
   *
   * @param session
   * @param data
   */
  private void sendWebsocketData(Session session, Object data) {
    String jsonStr = JsonUtils.writeValueAsString(data);
    try {
      synchronized (session) {
        session.getBasicRemote().sendText(jsonStr);
      }
    } catch (Exception e) {
      log.error("Monitor control ws send result exception", e);
    }
  }

}
