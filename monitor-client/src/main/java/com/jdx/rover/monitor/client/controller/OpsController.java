package com.jdx.rover.monitor.client.controller;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.ops.OpsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控OPS接口
 */
@RequestMapping(value = "/monitor/client/ops")
@RestController
@RequiredArgsConstructor
public class OpsController {

    private final OpsService opsService;

    /**
     * 清除遥控页车辆响应信息监听器
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    @GetMapping("/clear_remote_vehicle_response")
    public MonitorErrorEnum clearRemoteVehicleResponse(@RequestParam("vehicleName") String vehicleName) {
        ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
        return opsService.clearRemoteVehicleResponse(vehicleName);
    }
}