/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.client.websocket;

import com.jdx.rover.monitor.client.service.kafka.ThreeDimensionalMapService;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * This is a controller class to forward user's business logic to backend.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe when properties
 * parameters passed to it are used by the caller in thread safe manner.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/local_view/{vehicleName}")
@Component
public class MonitorLocalViewWebSocketServer {

  /**
   * <p>
   * Used to record the current number of online connections
   * </p>
   */
  private static AtomicInteger onlineCount = new AtomicInteger(0);

  private String vehicleName;

  /**
   * <p>
   * Represents topic session service.
   * </p>
   */
  private static ThreeDimensionalMapService threeDimensionalMapService;

  /**
   * <p>
   * This method helps to inject service from spring.
   * </p>
   *
   * @param threeDimensionalMapService The local view service.
   */
  @Autowired
  public void setLocalViewClientService(ThreeDimensionalMapService threeDimensionalMapService) {
    MonitorLocalViewWebSocketServer.threeDimensionalMapService = threeDimensionalMapService;
  }

  /**
   * <p>
   * Receive sessionid
   * </p>
   */
  private String sessionId = "";

  @OnOpen
  public void onOpen(Session session, @PathParam("vehicleName") String vehicleName) {
    this.sessionId = session.getId();
    this.vehicleName = vehicleName;
    addOnlineCount();
    log.info("Opening localView session id with {}. Online count is {}.", sessionId, getOnlineCount());
  }

  @OnClose
  public void onClose(Session session) {
    log.info("Onclose Local view session : {}", session.getId());
    threeDimensionalMapService.leave(session);
    subOnlineCount();
    log.info("Closing localView session id with {}. Online count is {}.", sessionId, getOnlineCount());
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    String topic = KafkaTopicConstant.SERVER_LOCAL_VIEW + this.vehicleName;
    log.info(String.format("monitor web local view socket on message sessionId:%s,message:%s, vehicleName: %s", session.getId(), message, this.vehicleName));
    threeDimensionalMapService.join(topic, session);
  }

  /**
   * @param session
   */
  @OnError
  public void onError(Session session, Throwable throwable) {
    log.error("monitor localview socket on error:{}", session.getId(), throwable);
    throwable.printStackTrace();
    this.sessionId = String.format("%s", session.getId());
    log.info(String.format("monitor localview socket on close user:%s,sessionId:%s,sersionNum:%s", sessionId, session.getId(),
        getOnlineCount()));
  }

  public static synchronized int getOnlineCount() {
    return onlineCount.intValue();
  }

  public static synchronized int addOnlineCount() {
    return onlineCount.incrementAndGet();
  }

  public static synchronized int subOnlineCount() {
    return onlineCount.decrementAndGet();
  }

}
