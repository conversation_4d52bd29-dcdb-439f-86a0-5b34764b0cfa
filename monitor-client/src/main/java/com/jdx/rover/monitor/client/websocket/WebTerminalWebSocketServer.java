/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.client.websocket;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.request.RequestIdUtils;
import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.client.config.ServletAwareConfigurator;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.service.listener.redis.WebterminalResponseListener;
import com.jdx.rover.monitor.service.webterminal.WebTerminalService;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseDTO;
import com.jdx.rover.server.api.domain.dto.webterminal.WebTerminalResponseHeader;
import com.jdx.rover.server.api.domain.enums.webterminal.WebTerminalMessageTypeEnum;
import com.jdx.rover.server.api.domain.enums.ws.WsMessageStateEnum;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestHeader;
import com.jdx.rover.server.api.domain.vo.webterminal.WebTerminalRequestVO;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnError;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 单车页面 web terminal websocket
 *
 * <AUTHOR>
 */
@Slf4j
@ServerEndpoint(value = "/monitor/client/ws/webterminal", configurator = ServletAwareConfigurator.class)
@Component
public class WebTerminalWebSocketServer {

    /**
     * <p>
     * Used to record the current number of online connections
     * </p>
     */
    private static int onlineCount = 0;


    /**
     * Websocket客户端
     */
    private WebsocketClientBO client = new WebsocketClientBO();
    /**
     * <p>
     * Receive sessionid
     * </p>
     */
    private String sessionId = "";

    /**
     * <p>
     * Remote control service.
     * </p>
     */
    private static WebTerminalService webTerminalService;

    @Autowired
    public void setWebTerminalService(WebTerminalService webTerminalService) {
        WebTerminalWebSocketServer.webTerminalService = webTerminalService;
    }
    /**
     * 打开连接
     *
     * @param session The session.
     */
    @OnOpen
    public void onOpen(Session session) {
        this.sessionId = String.format("%s", session.getId());
        String userName = session.getRequestParameterMap().get("userName").get(0);
        String vehicleName = session.getRequestParameterMap().get("vehicleName").get(0);
        String terminalName = session.getRequestParameterMap().get("terminalName").get(0);
        if (StringUtils.isBlank(userName) || StringUtils.isBlank(vehicleName) || StringUtils.isBlank(terminalName)) {
            log.error("web terminal control websocket user:{} ,vehicle:{},terminalName:{}", userName,vehicleName,terminalName);
            try {
                session.close();
            } catch (IOException e) {
                log.info("web terminal remote control socket on error ioException {}", e);
            }
            return;
        }
        this.client.setUsername(userName);
        this.client.setSessionId(session.getId());
        this.client.setVehicleName(vehicleName);
        this.client.setTerminalName(terminalName);
        this.client.setWebTerminalId(RequestIdUtils.getRequestId());
        this.client.setWsConnectionTime(System.currentTimeMillis());
        //数据topic
        subscribeTerminalResponse(session);
        addOnlineCount();
        WebTerminalResponseDTO webTerminalResponseDTO = setWebTerminalResponseDTO(WebTerminalMessageTypeEnum.OPEN_SESSION.getValue(),WsMessageStateEnum.RECEIVED.getValue());
        // 响应request Id
        sendWebsocketData(session,webTerminalResponseDTO);
        //发向Rover 创建终端
        WebTerminalRequestVO webTerminalRequestVO = setWebTerminalRequestVO(WebTerminalMessageTypeEnum.NEW_TERMINAL.getValue(),false,null);
        webTerminalRequestVO.setMessageType(WebTerminalMessageTypeEnum.NEW_TERMINAL.getValue());
        webTerminalService.sendCommandToVehicle(webTerminalRequestVO);
        webTerminalService.sendCommandToRecord(userName, vehicleName, terminalName);
        log.info(String.format("web terminal  web socket on open user:%s,vehicleName:%s, terminalName:%s, user:%s, sessionId:%s,sersionNum:%s",
                sessionId, vehicleName, terminalName, userName, session.getId(), getOnlineCount()));
    }

    /**
     * 设置数据响应头信息
     * @return
     */
    private WebTerminalResponseDTO setWebTerminalResponseDTO(String messageType, String messageState){
        WebTerminalResponseHeader webTerminalResponseHeader = new WebTerminalResponseHeader();
        webTerminalResponseHeader.setResponseTime(System.currentTimeMillis());
        webTerminalResponseHeader.setTerminalName(this.client.getTerminalName());
        webTerminalResponseHeader.setVehicleName(this.client.getVehicleName());
        webTerminalResponseHeader.setRequestId(this.client.getWebTerminalId());
        webTerminalResponseHeader.setMessageState(messageState);
        WebTerminalResponseDTO webTerminalResponseDTO = new WebTerminalResponseDTO();
        webTerminalResponseDTO.setHeader(webTerminalResponseHeader);
        webTerminalResponseDTO.setMessageType(messageType);
        return webTerminalResponseDTO;
    }
    /**
     * 设置数据请求头信息
     * @return
     */
    private WebTerminalRequestVO setWebTerminalRequestVO(String messageType,Boolean needResponse,Object data){
        WebTerminalRequestHeader webTerminalRequestHeader = new WebTerminalRequestHeader();
        webTerminalRequestHeader.setVehicleName(this.client.getVehicleName());
        webTerminalRequestHeader.setTerminalName(this.client.getTerminalName());
        webTerminalRequestHeader.setRequestId(this.client.getWebTerminalId());
        webTerminalRequestHeader.setRequestTime(System.currentTimeMillis());
        webTerminalRequestHeader.setNeedResponse(needResponse);
        webTerminalRequestHeader.setExpireMilliSecond(0);
        WebTerminalRequestVO webTerminalRequestVO = new WebTerminalRequestVO();
        webTerminalRequestVO.setHeader(webTerminalRequestHeader);
        webTerminalRequestVO.setMessageType(messageType);
        webTerminalRequestVO.setUserName(this.client.getUsername());
        webTerminalRequestVO.setData(data);
        webTerminalRequestVO.setUserName(this.client.getUsername());
        return webTerminalRequestVO;
    }
    /**
     * 订阅数据
     *
     * @param session
     */
    private void subscribeTerminalResponse(Session session) {
        StringBuffer topicName = new StringBuffer(16);
        topicName.append(RedisTopicEnum.REMOTE_CONTROL_WEB_TERMINAL_PREFIX.getValue());
        topicName.append(this.client.getVehicleName() + ":");
        topicName.append(this.client.getTerminalName() + ":");
        topicName.append(this.client.getWebTerminalId());
        RTopic rTopic = RedissonUtils.getRTopic(topicName.toString());
        MessageListener messageListener = new WebterminalResponseListener(session);
        int listenerId = rTopic.addListener(String.class, messageListener);
        log.info("web terminal webSocket add topic={},listener count={},subscribers={}", topicName, rTopic.countListeners(), rTopic.countSubscribers());
        if (this.client.getEventListenerId() == null) {
            this.client.setEventListenerId(listenerId);
        }
        log.info("订阅远程工具响应数据!{}", this.client);
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        WebTerminalWebSocketServer.onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        WebTerminalWebSocketServer.onlineCount--;
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info(String.format("web terminal web control socket on message sessionId:%s,message:%s", session.getId(), message));
        WebTerminalRequestVO webTerminalRequestVO = JsonUtils.readValue(message, WebTerminalRequestVO.class);
        WsResult result = null;
        // 心跳
        if (StringUtils.equals(webTerminalRequestVO.getMessageType(), WebTerminalMessageTypeEnum.HEARTBEAT.getValue())) {
            log.info(String.format("web terminal web control socket on hearbeat sessionId:%s,message:%s", session.getId(), message));
            if(System.currentTimeMillis() - this.client.getWsConnectionTime() > 300000){
                //超时会话 关闭连接
                sendWebsocketData(session,setWebTerminalResponseDTO(WebTerminalMessageTypeEnum.CLOSE_SESSION.getValue(),WsMessageStateEnum.INIT.getValue()));
                //超时会话 关闭终端
                webTerminalRequestVO = setWebTerminalRequestVO(WebTerminalMessageTypeEnum.CLOSE_TERMINAL.getValue(),false,null);
                webTerminalService.sendCommandToVehicle(webTerminalRequestVO);
                return;
            }
            sendWebsocketData(session,setWebTerminalResponseDTO(WebTerminalMessageTypeEnum.HEARTBEAT.getValue(),WsMessageStateEnum.RECEIVED.getValue()));
            return;
        }
        //指令内容
        if (StringUtils.equals(webTerminalRequestVO.getMessageType(), WebTerminalMessageTypeEnum.COMMAND_DATA.getValue())) {
            this.client.setWsConnectionTime(System.currentTimeMillis());
            webTerminalService.sendCommandToVehicle(setWebTerminalRequestVO(WebTerminalMessageTypeEnum.COMMAND_DATA.getValue(),false,webTerminalRequestVO.getData()));
            return;
        }
    }
    /**
     * 发送数据
     *
     * @param session
     * @param data
     */
    private void sendWebsocketData(Session session, Object data) {
        String jsonStr = JsonUtils.writeValueAsString(data);
        try {
            synchronized (session) {
                session.getBasicRemote().sendText(jsonStr);
            }
        } catch (Exception e) {
            log.error("web terminal ws send result exception", e);
        }
    }

    /**
     * 关闭连接
     *
     * @param session The session.
     */
    @OnClose
    public void onClose(Session session) {
        this.sessionId = String.format("%s", session.getId());
        subOnlineCount();
        unSubscribeTerminalResponse(session);
        WebTerminalRequestVO webTerminalRequestVO = setWebTerminalRequestVO(WebTerminalMessageTypeEnum.CLOSE_TERMINAL.getValue(),false,null);
        log.info("web terminal webSocket close session {}", JsonUtils.writeValueAsString(this.client));
        webTerminalService.sendCommandToVehicle(webTerminalRequestVO);
    }

    /**
     * 取消订阅远程工具数据
     */
    private void unSubscribeTerminalResponse(Session session) {
        if (this.client.getEventListenerId() == null) {
            return;
        }
        StringBuffer topicName = new StringBuffer(16);
        topicName.append(RedisTopicEnum.REMOTE_CONTROL_WEB_TERMINAL_PREFIX.getValue());
        topicName.append(this.client.getVehicleName() + ":");
        topicName.append(this.client.getTerminalName() + ":");
        topicName.append(this.client.getWebTerminalId());
        RTopic rTopic = RedissonUtils.getRTopic(topicName.toString());
        rTopic.removeListener(this.client.getEventListenerId());
        log.info("web terminal webSocket remove topic={},listenerId={}", topicName, this.client.getEventListenerId());
    }

    /**
     * 连接异常
     *
     * @param throwable The exception.
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("web terminal web socket on error:{},{}", session.getId(), throwable);
        throwable.printStackTrace();
        this.sessionId = String.format("%s", session.getId());
        subOnlineCount();
        log.info(String.format("web terminal web socket on close user:%s,sessionId:%s,sersionNum:%s", sessionId, session.getId(),
                getOnlineCount()));
        try {
            session.close();
        } catch (IOException e) {
            log.error("web terminal web socket on error ioException {}", e);
        }
    }
}
