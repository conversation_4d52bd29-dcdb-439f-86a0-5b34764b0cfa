package com.jdx.rover.monitor.client.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;

/**
 * <AUTHOR>
 */
@Configuration
@EnableKafka
public class KafkaConfig {
  /**
   * 监听容器工厂（批量）
   *
   * @param consumerFactory {@link ConsumerFactory} 消费者工厂
   * @return {@link ConcurrentKafkaListenerContainerFactory}
   */
  @Bean("batchListenerContainerFactory")
  public ConcurrentKafkaListenerContainerFactory<Integer, String> batchListenerContainerFactory(
      @Autowired ConsumerFactory<Integer, String> consumerFactory) {
    ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(consumerFactory);
    // 设置并发量，小于或等于 Topic 的分区数
    factory.setConcurrency(5);
    // 设置为批量监听
    factory.setBatchListener(true);
    return factory;
  }
}