package com.jdx.rover.monitor.client.service.kafka;

import com.jdx.rover.monitor.service.webterminal.WebTerminalService;
import com.jdx.rover.server.api.domain.constants.KafkaTopicConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * web terminal消息接收Listener
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WebTerminalKafkaListener {
  @Autowired
  private WebTerminalService webTerminalService;

  @KafkaListener(topics = {KafkaTopicConstant.SERVER_WEB_TERMINAL_COMMAND_UP})
  public void onMessage(String message) {
    webTerminalService.handleMessage(message);
  }
}
