server:
  port: 8083
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-yh7thvwgyt4s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
project:
  kafka-byte: # 特定的kafka集群,接收byte[]数组
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
websocket:
  maxTextMessageBufferSize: 202400
  maxBinaryMessageBufferSize: 102400