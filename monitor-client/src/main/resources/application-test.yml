server:
  port: 8083
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************
          username: root
          password: 123456
        postgresql:
          url: *******************************************************
          username: postgres
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
  kafka:
    bootstrap-servers: *************:9092
project:
  kafka-byte: # 特定的kafka集群,接收byte[]数组
    bootstrap-servers: *************:9092