spring:
  application:
    name: rover-monitor-client
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          druid:
            initial-size: 1
            max-active: 15
            max-wait: 60000 #缺省使用公平锁
            min-idle: 1 #最小连接池数量
            max-evictable-idle-time-millis: 600000 #连接最大生存时间, ms
            min-evictable-idle-time-millis: 300000 #连接最小生存时间, ms，默认300s
            test-on-borrow: false
            test-on-return: false #默认值,不配置
            test-while-idle: true
            validation-query: SELECT 1 FROM DUAL
            validation-query-timeout: 10000
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  kafka:
    producer:
      retries: 5
    consumer:
      enable-auto-commit: true
      auto-commit-interval: 1000
      group-id: ${spring.application.name}
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:sqlmap/*.xml
  type-handlers-package: com.jdx.rover.monitor.domain.handler
  configuration:
    auto-mapping-behavior: FULL
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志

websocket:
  maxTextMessageBufferSize: 512000
  maxBinaryMessageBufferSize: 512000
  maxSessionIdleTimeout: 60000

logging:
  config: classpath:log4j2.xml
jira-service:
  userName: org.jdx.data1
  passWord: Zhenxinaini%521
project:
  redisson:
    timeout: 10000
    subscriptionsPerConnection: 5000
    subscriptionConnectionPoolSize: 5000
    connectionPoolSize: 500
    nettyThreads: 256
  local:
    cache:
      localCacheEvictTopic: local:cache:evict
s3:
  access-key: 85C76AB2AB89077E8500CB6021A81EC7
  secret-key: EEDD3436DF9B9ED075A39DEE468F66A2
  endpoint: https://s3-internal.cn-north-1.jdcloud-oss.com
  out-endpoint: https://s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  signer-override: AWSS3V4SignerType
jmq:
  password: d94a9e72f1ae414e8c0bc78920db8345
  app: monitorclienttest
  address: test-nameserver.jmq.jd.local:50088
monitor:
  jmq:
    topic:
      remote_command_delay: server_remote_command_delay_${spring.profiles.active}
      web_terminal_info: server_web_terminal_command_up_${spring.profiles.active}
      vehicle-status-change: server_vehicle_change_status_${spring.profiles.active}
    provider:
      topic:
        monitor_cockpit_realtime_status_change: cockpit_realtime_status_change_${spring.profiles.active}
        monitor_remote_command_log: monitor_remote_command_log_${spring.profiles.active}
        shadow_tracking_event: rover_shadow_tracking_event_${spring.profiles.active}
        shadow_jira_event: rover_shadow_jira_event_${spring.profiles.active}
        monitor_pnc_route: monitor_pnc_route_${spring.profiles.active}
        server_web_terminal_command_down: server_web_terminal_command_down_${spring.profiles.active}
        monitor_vehicle_remote_command_operation: vehicle_remote_command_operation_${spring.profiles.active}
        monitor_remote_control_command: monitor_remote_control_command_${spring.profiles.active}
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm_${spring.profiles.active}
        monitor_view_order_detail: monitor_view_order_detail_${spring.profiles.active}
        monitor_vehicle_change_event: monitor_vehicle_change_event_${spring.profiles.active}
        monitor_single_vehicle_realtime: monitor_single_vehicle_realtime_${spring.profiles.active}
        monitor_single_vehicle_schedule: monitor_single_vehicle_schedule_${spring.profiles.active}
        monitor_accident_flow_event: monitor_accident_flow_event_${spring.profiles.active}
        map_vehicle_alarm: map_vehicle_alarm_${spring.profiles.active}