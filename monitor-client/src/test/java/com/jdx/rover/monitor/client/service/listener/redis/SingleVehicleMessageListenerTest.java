package com.jdx.rover.monitor.client.service.listener.redis;

import com.jdx.rover.monitor.bo.websocket.WebsocketClientBO;
import com.jdx.rover.monitor.service.listener.redis.SingleVehicleMessageListener;
import org.junit.jupiter.api.Test;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2023/3/30
 */
class SingleVehicleMessageListenerTest {
    private SingleVehicleMessageListener singleVehicleMessageListener = new SingleVehicleMessageListener(null, new WebsocketClientBO());

    @Test
    void onMessage() {
        String msg = "{\"eventType\":\"SINGLE_VEHICLE_NO_SIGNAL_INTERSECTION\",\"code\":\"0000\",\"message\":null,\"data\":{\"recordTime\":\"2023-03-30 10:10:51\",\"status\":\"RUNNING\",\"groupLaneIdList\":[\"11\",\"12\"]}}";
        singleVehicleMessageListener.onMessage("", msg);
    }
}