package com.jdx.rover.monitor.service.webterminal;

import com.jdx.rover.monitor.client.RoverClientApplication;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/15
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverClientApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WebTerminalServiceTest {
    private final WebTerminalService webTerminalService;

    @Test
    void handleMessage() {
        String message = "{\"messageType\":\"VEHICLE_TO_SERVER_FILE\",\"userName\":null,\"header\":{\"vehicleName\":\"JD0055\",\"requestId\":16920961589813157,\"requestTime\":1692096158897,\"expireMilliSecond\":null,\"needResponse\":null,\"terminalName\":\"smile-Precision-Tower-3420\"},\"data\":[{\"fileName\":\"rover_pmap.txt\",\"downloadUrl\":\"https://webterminal.s3.cn-north-1.jdcloud-oss.com/2023-08-15/JD0055/rover_pmap.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230815T071435Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20230815%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=ab652f2f3f48a284f360355a862ee7fc211158b816cf319a42e2889882ef5927\",\"processProgress\":100},{\"fileName\":\"rover_pmap.txt\",\"downloadUrl\":\"https://webterminal.s3.cn-north-1.jdcloud-oss.com/2023-08-15/JD0055/rover_pmap.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230815T071436Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20230815%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=6b4d3bff9e738ef2c75295c0374e25432c46f9c6718e9aafd10ae76714168b54\",\"processProgress\":100},{\"fileName\":\"rover_pmap.txt\",\"downloadUrl\":\"https://webterminal.s3.cn-north-1.jdcloud-oss.com/2023-08-15/JD0055/rover_pmap.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230815T071442Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20230815%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=909e5f56bca0a205e295642d37a1ef7a000df6eee1cdd8a9ad28431ccc04951d\",\"processProgress\":100},{\"fileName\":\"rover_pmap.txt\",\"downloadUrl\":\"https://webterminal.s3.cn-north-1.jdcloud-oss.com/2023-08-15/JD0055/rover_pmap.txt?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230815T071443Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20230815%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=01da79658916d6f917a336078103a03ae316c1bd03da4fc6491b057745654c6c\",\"processProgress\":100}]}";
        webTerminalService.handleMessage(message);
    }
}