package com.jdx.rover.monitor.client.websocket;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.enums.ReadyState;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Slf4j
class MonitorRealtimeWebSocketServerTest {

  @Test
  void multiThread() throws Exception {
    ExecutorService service = Executors.newFixedThreadPool(300);
//    String url = "ws://127.0.0.1:8083/monitor/client/ws/realtime?username=test";
    String url = "ws://jdxmonitor-client-beta.jd.local/monitor/client/ws/realtime?username=test";
    try {
      MyWebSocketClient myClient = new MyWebSocketClient(new URI(url));
      myClient.connect();
      while (!myClient.getReadyState().equals(ReadyState.OPEN)) {
        log.info("连接中。。。");
        Thread.sleep(1000);
      }
      // 连接成功往websocket服务端发送数据
      String msg = "{\"eventType\":\"MULTI_VEHICLE\",\"data\":{\"tabType\":\"ALL\",\"multiVehiclePageList\":[{\"category\":\"ALL\",\"pageNum\":1,\"pageSize\":5}]}}";
      myClient.send(msg);
      log.info("发送ws完成!");
      Thread.sleep(1000);
    } catch (Exception e) {
      e.printStackTrace();
    }

    Runnable wsRunnable = () -> {
      try {
        Thread.sleep(RandomUtil.randomInt(0, 5000));
        MyWebSocketClient myClient = new MyWebSocketClient(new URI(url));
        myClient.connect();
        while (!myClient.getReadyState().equals(ReadyState.OPEN)) {
          log.info("连接中。。。");
          Thread.sleep(1000);
        }
        Thread.sleep(RandomUtil.randomInt(0, 5000));
        // 连接成功往websocket服务端发送数据
        String msg = "{\"eventType\":\"MULTI_VEHICLE\",\"data\":{\"vehicleBusinessType\":\"\",\"vehicleUseCase\":\"\",\"sortType\":\"VEHICLE_NAME\",\"currentCategory\":\"CONNECTION_LOST\",\"multiVehiclePageList\":[{\"category\":\"CONNECTION_LOST\",\"pageNum\":1,\"pageSize\":15}]}}";
        while (true) {
          myClient.send(msg);
          log.info("发送ws完成!");
          Thread.sleep(2000);
          break;
        }
      } catch (Exception e) {
        log.error("启动ws连接错误!", e);
      }
    };

    for (int i = 0; i < 3; i++) {
      service.execute(wsRunnable);
    }

    Thread.sleep(1000);
  }

  @Test
  void onMessage() {
  }
}