spring:
  application:
    name: rover-monitor-client
  profiles:
    active: "test"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 1
      max-active: 15
      max-wait: 60000 #缺省使用公平锁
      min-idle: 1 #最小连接池数量
      max-evictable-idle-time-millis: 600000 #连接最大生存时间, ms
      min-evictable-idle-time-millis: 300000 #连接最小生存时间, ms，默认300s
      test-on-borrow: false
      test-on-return: false #默认值,不配置
      test-while-idle: true
      validation-query: SELECT 1 FROM DUAL
      validation-query-timeout: 10000
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  kafka:
    producer:
      retries: 5
    consumer:
      enable-auto-commit: true
      auto-commit-interval: 1000
      group-id: ${spring.application.name}
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:sqlmap/*.xml
  type-handlers-package: com.jdx.rover.oauth.web.typehandler
  configuration:
    auto-mapping-behavior: FULL
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志

websocket:
  maxTextMessageBufferSize: 1024000
  maxBinaryMessageBufferSize: 1024000
  maxSessionIdleTimeout: 60000

logging:
  config: classpath:log4j2.xml
project:
  local:
    cache:
      localCacheEvictTopic: local:cache:evict



