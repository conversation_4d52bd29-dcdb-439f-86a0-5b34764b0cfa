/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapmode.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleBasicDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehiclePathDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleRealtimeDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleVersionDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleIssueDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.map.InsuranceAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleInsuranceDTO;
import com.jdx.rover.monitor.vo.mobile.common.PositionVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.vo.mobile.mapmode.VehicleInsuranceVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11 19:22
 * @description 车辆地图模式
 */
public interface MobileMapModeJsfService {

    /**
     * 获取用户权限下车辆列表
     */
    HttpResult<List<VehicleListDTO>> getVehicleList();

    /**
     * 获取地图上车辆
     */
    HttpResult<List<MapVehicleDTO>> getMapVehicle(@NotNull(message = "非法请求") @Valid PositionVO positionVO);

    /**
     * 获取车辆基础信息（用户是否有权限）
     */
    HttpResult<MapVehicleBasicDTO> getVehicleBasic(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取工单信息（运维工单）
     */
    HttpResult<VehicleIssueDTO> getVehicleIssue(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取车辆版本信息
     */
    HttpResult<MapVehicleVersionDTO> getVehicleVersion(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取实时信息
     */
    HttpResult<MapVehicleRealtimeDTO> getVehicleRealTime(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取规划路径
     */
    HttpResult<MapVehiclePathDTO> getVehiclePath(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取车辆生效中保单信息列表
     */
    HttpResult<List<VehicleInsuranceDTO>> getVehicleEffectiveInsuranceList(@NotNull(message = "非法请求") VehicleInsuranceVO vehicleBaseVO);

    /**
     * 获取保单文件外链
     */
    HttpResult<InsuranceAttachmentDTO> getPolicyAttachmentFileUrl(@NotNull(message = "非法请求") VehicleInsuranceVO vehicleBaseVO);
}
