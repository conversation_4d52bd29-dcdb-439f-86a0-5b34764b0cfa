package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.map.InsuranceAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleBasicDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehiclePathDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleRealtimeDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleVersionDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleInsuranceDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleIssueDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleListDTO;
import com.jdx.rover.monitor.service.mobile.MapModeService;
import com.jdx.rover.monitor.vo.mobile.common.PositionVO;
import com.jdx.rover.monitor.vo.mobile.mapmode.VehicleInsuranceVO;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/17 10:36
 * @description 车辆地图模式
 */
@RequestMapping(value = "/mobile/applet/map")
@RestController
@RequiredArgsConstructor
public class MapModeController {

    /**
     * 车辆地图模式
     */
    private final MapModeService mapModeService;

    /**
     * 获取用户权限下车辆列表
     *
     * @return List<MapGetVehicleListDTO>
     */
    @GetMapping("/getVehicleList")
    public List<VehicleListDTO> getVehicleList() {
        String username = UserUtils.getAndCheckLoginUser();
        return mapModeService.getVehicleList(username);
    }

    /**
     * 获取地图上车辆
     *
     * @param positionVO positionVO
     * @return List<MapVehicleDTO>
     */
    @PostMapping("/getMapVehicle")
    public List<MapVehicleDTO> getMapVehicle(@Validated @RequestBody PositionVO positionVO) {
        return mapModeService.getMapVehicle(positionVO);
    }

    /**
     * 获取车辆基础信息（用户是否有权限）
     *
     * @param vehicleName vehicleName
     * @return VehicleBasicDTO
     */
    @GetMapping("/getVehicleBasic")
    public MapVehicleBasicDTO getVehicleBasic(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        String username = UserUtils.getAndCheckLoginUser();
        return mapModeService.getVehicleBasic(vehicleName, username);
    }

    /**
     * 获取工单信息（运维工单）
     *
     * @param vehicleName vehicleName
     * @return VehicleIssueDTO
     */
    @GetMapping("/getVehicleIssue")
    public VehicleIssueDTO getVehicleIssue(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return mapModeService.getVehicleIssue(vehicleName);
    }

    /**
     * 获取车辆版本信息
     *
     * @param vehicleName vehicleName
     * @return MapVehicleVersionDTO
     */
    @GetMapping("/getVehicleVersion")
    public MapVehicleVersionDTO getVehicleVersion(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return mapModeService.getVehicleVersion(vehicleName);
    }

    /**
     * 获取实时信息
     *
     * @param vehicleName vehicleName
     * @return MapVehicleRealtimeDTO
     */
    @GetMapping("/getVehicleRealTime")
    public MapVehicleRealtimeDTO getVehicleRealTime(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return mapModeService.getVehicleRealTime(vehicleName);
    }

    /**
     * 获取规划路径
     *
     * @param vehicleName vehicleName
     * @return MapVehiclePathDTO
     */
    @GetMapping("/getVehiclePath")
    public MapVehiclePathDTO getVehiclePath(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return mapModeService.getVehiclePath(vehicleName);
    }

    /**
     * 获取车辆生效中保单信息列表
     */
    @PostMapping("/getVehicleInsurance")
    public List<VehicleInsuranceDTO> getVehicleEffectiveInsuranceList(@RequestBody VehicleInsuranceVO vehicleInsuranceVO) {
        return mapModeService.getVehicleEffectiveInsuranceList(vehicleInsuranceVO);
    }

    /**
     * 获取保单文件外链
     */
    @PostMapping("/getInsuranceAttachmentFileUrl")
    public InsuranceAttachmentDTO getPolicyAttachmentFileUrl(@RequestBody VehicleInsuranceVO vehicleInsuranceVO) {
        return mapModeService.getPolicyAttachmentFileUrl(vehicleInsuranceVO);
    }
}
