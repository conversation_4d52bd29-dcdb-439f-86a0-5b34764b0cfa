/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mapcollection.MarkCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.vo.mapcollection.MarkCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkUpdateVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:16
 * @description 地图采集标记服务
 */
public interface MapCollectionMarkJsfService {

    /**
     * 获取标记列表
     */
    HttpResult<MarkSearchDTO> getMarkList(@Valid PositionVO positionVO);

    /**
     * 新增标记
     */
    HttpResult<MarkCreateDTO> createMark(@Valid MarkCreateVO markCreateVO);

    /**
     * 修改标记
     */
    HttpResult<Void> updateMark(@Valid MarkUpdateVO markUpdateVO);

    /**
     * 删除标记
     */
    HttpResult<Void> deleteMark(@Valid MarkDeleteVO markDeleteVO);
}
