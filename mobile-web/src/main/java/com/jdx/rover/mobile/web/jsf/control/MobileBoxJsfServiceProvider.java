/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.control.service.MobileBoxJsfService;
import com.jdx.rover.monitor.dto.mobile.box.GetBoxGridDTO;
import com.jdx.rover.monitor.service.mobile.VehicleBoxService;
import com.jdx.rover.monitor.vo.mobile.box.VehicleOpenBoxVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/18 11:17
 * @description 开舱门
 */
@Service
@RequiredArgsConstructor
public class MobileBoxJsfServiceProvider extends AbstractProvider<MobileBoxJsfService> implements MobileBoxJsfService {

    /**
     * VehicleBoxService
     */
    private final VehicleBoxService vehicleBoxService;

    @Override
    @ServiceInfo(name = "获取货箱格口列表", webUrl = "/mobile/applet/box/getBoxGrid")
    public HttpResult<GetBoxGridDTO> getBoxGrid(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> vehicleBoxService.getBoxGrid(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "开箱&一键全开", webUrl = "/mobile/applet/box/openBox")
    public HttpResult<Void> openBox(VehicleOpenBoxVO vehicleOpenBoxVO) {
        return JsfResponse.response(() -> vehicleBoxService.openBox(vehicleOpenBoxVO));
    }
}
