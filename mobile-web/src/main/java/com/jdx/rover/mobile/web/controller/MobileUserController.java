/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.MobileUserConfigDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserMaskPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserStationNoticeConfigDTO;
import com.jdx.rover.monitor.service.mobile.MobileUserService;
import com.jdx.rover.monitor.vo.mobile.MobileUserConfigVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 运营端用户配置服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/mobile/applet/user")
@RestController
@RequiredArgsConstructor
public class MobileUserController {

  /**
   * 事件记录服务
   */
  private final MobileUserService mobileUserService;

  /**
   * 获取车辆搜索记录
   *
   */
  @GetMapping("/getVehicleSearchRecord")
  public HttpResult<List<String>> getVehicleSearchRecord() {
    return HttpResult.success(mobileUserService.getVehicleSearchRecord());
  }

  /**
   * 新增车辆搜索记录
   *
   * @param vehicleName 车号
   */
  @GetMapping("/addVehicleSearchRecord")
  public HttpResult<Void> addVehicleSearchRecord(@RequestParam(value = "vehicleName") String vehicleName) {
    mobileUserService.addSearchRecord(vehicleName);
    return HttpResult.success();
  }

  /**
   * 清空车辆搜索记录
   *
   */
  @GetMapping("/clearVehicleSearchRecord")
  public HttpResult<Void> clearVehicleSearchRecord() {
    mobileUserService.clearVehicleSearchRecord();
    return HttpResult.success();
  }


  /**
   * 获取用户配置
   */
  @GetMapping("/getUserConfig")
  public HttpResult<List<MobileUserConfigDTO>> getUserConfig() {
    return HttpResult.success(mobileUserService.getUserConfig());
  }

  /**
   * 获取配置站点列表
   */
  @GetMapping("/getConfigStationCrashList")
  public HttpResult<List<MobileUserStationNoticeConfigDTO>> getConfigStationCrashList() {
    return HttpResult.success(mobileUserService.getConfigStationCrashList());
  }

  /**
   * 更新配置站点
   */
  @PostMapping("/updateConfig/{config}")
  public HttpResult<Void> updateConfig(@PathVariable(value = "config") String config, @RequestBody MobileUserConfigVO userConfigVo) {
    userConfigVo.setConfig(config);
    return mobileUserService.updateConfigStation(userConfigVo);
  }

  /**
   * 根据userName获取掩码手机号
   */
  @GetMapping("/getMaskPhone")
  public HttpResult<MobileUserMaskPhoneDTO> getMaskPhone(@RequestParam(value = "userName") String userName) {
    return HttpResult.success(mobileUserService.getMaskPhone(userName));
  }
}