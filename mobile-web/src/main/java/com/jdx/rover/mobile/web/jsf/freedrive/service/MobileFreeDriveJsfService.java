/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.freedrive.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.freeDrive.ArbitraryNavigationDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetMapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetStopListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.NavigationDTO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.ArbitraryNavigationVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetMapVehicleVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetStopListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.NavigationVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18 10:41
 * @description 自由跑行
 */
public interface MobileFreeDriveJsfService {

    /**
     * 获取车辆列表
     */
    HttpResult<List<GetVehicleListDTO>> getVehicleList(@NotNull(message = "非法请求") @Valid GetVehicleListVO getVehicleListVO);

    /**
     * 获取地图上车辆
     */
    HttpResult<List<GetMapVehicleDTO>> getMapVehicle(@NotNull(message = "非法请求") @Valid GetMapVehicleVO getMapVehicleVO);

    /**
     * 车辆选择校验
     */
    HttpResult<Void> checkVehicle(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取去往停靠点列表
     */
    HttpResult<List<GetStopListDTO>> getStopList(@NotNull(message = "非法请求") @Valid GetStopListVO getStopListVO);

    /**
     * 立即出发
     */
    HttpResult<NavigationDTO> navigation(@NotNull(message = "非法请求") @Valid NavigationVO navigationVO);

    /**
     * 立即出发前往任意点
     */
    HttpResult<ArbitraryNavigationDTO> arbitraryNavigation(@NotNull(message = "非法请求") @Valid ArbitraryNavigationVO arbitraryNavigationVO);
}
