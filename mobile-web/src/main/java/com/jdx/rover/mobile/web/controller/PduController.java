package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.monitor.common.exception.AppException;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.manager.mqtt.PduMqttManager;
import com.jdx.rover.monitor.vo.mobile.command.PduCommandSendVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <p>
 * This is a controller for sending pdu mqtt command.
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/26
 */
@RestController
@RequestMapping(value = "/mobile/ops/pdu/command")
@RequiredArgsConstructor
public class PduController {

    private final PduMqttManager pduMqttManager;

    /**
     * 指令操作
     *
     * @param pduCommandSendVO pduCommandSendVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/send")
    public MonitorErrorEnum send(@Valid @RequestBody PduCommandSendVO pduCommandSendVO) {

        CommandTypeEnum commandTypeEnum = CommandTypeEnum.of(pduCommandSendVO.getCommandType());
        if (Objects.isNull(commandTypeEnum)) {
            return MonitorErrorEnum.ERROR_CALL_CHECK_PARAM;
        }

        try {
            pduMqttManager.sendPowerCommand(pduCommandSendVO.getVehicleName(), commandTypeEnum);
        } catch (AppException e) {
            return MonitorErrorEnum.ERROR_POWER_MANAGER_ABSENT;
        }
        return MonitorErrorEnum.OK;
    }
}
