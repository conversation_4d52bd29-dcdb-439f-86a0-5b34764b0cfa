/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/17 21:18
 * @description 运营端远程指令
 */
public interface MobileRemoteControlJsfService {

    /**
     * 接管车辆
     */
    HttpResult<Object> openRemoteControl(@NotNull(message = "非法请求") @Valid MiniMonitorRemoteCommandVO emergencyStopCommandVO);

    /**
     * 急刹车辆
     */
    HttpResult<Object> brakeRemoteControl(@NotNull(message = "非法请求") @Valid MiniMonitorRemoteCommandVO emergencyBrakeCommandVO);

    /**
     * 恢复车辆
     */
    HttpResult<Object> quitRemoteControl(@NotNull(message = "非法请求") @Valid MiniMonitorRemoteCommandVO recoveryCommandVO);
}
