/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.mobile.web.jsf.control.service.MobileRemoteCommandJsfService;
import com.jdx.rover.monitor.dto.transport.UserOperateCommandDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.mobile.CommandTypeEnum;
import com.jdx.rover.monitor.enums.mobile.H5RemotePowerEnum;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.service.mobile.CommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/17 21:49
 * @description MobileRemoteCommandJsfServiceImpl
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MobileRemoteCommandJsfServiceProvider extends AbstractProvider<MobileRemoteCommandJsfService> implements MobileRemoteCommandJsfService {

    /**
     * CommandService
     */
    private final CommandService commandService;

    private final JmqProducerService jmqProducerService;

    @Override
    @ServiceInfo(name = "指令操作", webUrl = "/mobile/applet/command/send")
    public HttpResult<Void> send(SendVO sendVO) {
        String username = JsfLoginUtil.getUsername();
        Date operateTime = new Date();
        MonitorErrorEnum monitorErrorEnum = commandService.send(sendVO);
        // 发送mq消息
        if (CommandTypeEnum.REMOTE_POWER_ON.getCode().equals(sendVO.getCommandType()) || CommandTypeEnum.REMOTE_SHUTDOWN.getCode().equals(sendVO.getCommandType())) {
            try {
                String operateType = CommandTypeEnum.REMOTE_POWER_ON.getCode().equals(sendVO.getCommandType()) ? H5RemotePowerEnum.POWER_ON.getValue() : H5RemotePowerEnum.POWER_OFF.getValue();
                UserOperateCommandDTO commandDTO = UserOperateCommandDTO.builder()
                        .operateUser(username)
                        .vehicleName(sendVO.getVehicleName())
                        .operateTime(operateTime)
                        .source(RemoteCommandSourceEnum.MINI_MONITOR.getCommandSource())
                        .operateType(operateType)
                        .operateResult(MonitorErrorEnum.OK.equals(monitorErrorEnum) ? 1 : 0)
                        .build();
                jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_H5_TRANSPORT_COMMAND.getTopic(), commandDTO.getVehicleName(), commandDTO);
            } catch (Exception e) {
                log.error("发送mq消息失败", e);
            }
        }
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "声音播报", webUrl = "/mobile/applet/command/broadCast")
    public HttpResult<Void> remoteShoutWord(MiniMonitorRemoteShoutVO miniMonitorRemoteShoutVO) {
        ParameterCheckUtility.checkNotNull(miniMonitorRemoteShoutVO.getVoiceMsg(), "miniMonitorRemoteShoutVO#msg");
        commandService.remoteBroadCastWord(miniMonitorRemoteShoutVO.getVehicleName(), miniMonitorRemoteShoutVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "声音鸣笛", webUrl = "/mobile/applet/command/whistle")
    public HttpResult<Void> remoteVoiceWhistle(VehicleBaseVO vehicleBaseVO) {
        commandService.remoteVoiceWhistle(vehicleBaseVO.getVehicleName());
        return HttpResult.success();
    }
}
