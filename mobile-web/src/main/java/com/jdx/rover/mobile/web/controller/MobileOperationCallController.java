/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.dto.mobile.call.MobileAttentionPhoneDTO;
import com.jdx.rover.monitor.service.mobile.MobileUserService;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 运营端呼叫服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/mobile/applet/call")
@RestController
@RequiredArgsConstructor
public class MobileOperationCallController {

  /**
   * 告警服务接口
   */
  private final MonitorAlarmService monitorAlarmService;

  /**
   * 用户服务接口
   */
  private final MobileUserService mobileUserService;

  /**
   * 获取一线安全员列表
   *
   * @param vehicleName 车号
   */
  @GetMapping("/getSiteUserList")
  public HttpResult getSiteUserList(@RequestParam(value = "vehicleName") String vehicleName) {
    return mobileUserService.getSiteUserList(vehicleName);
  }

  /**
   * 获取用户手机号
   *
   * @param userName 用户名
   */
  @GetMapping("/getUserPhone")
  public HttpResult getUserPhone(@RequestParam(value = "userName") String userName) {
    return mobileUserService.getUserInfo(userName);
  }

  /**
   * 呼叫云端智驾
   *
   * @param manualAlarmReportVo 人工告警
   */
  @PostMapping("/callCockpit")
  public HttpResult callCockpit(@RequestBody MonitorManualAlarmReportVO manualAlarmReportVo) {
    return monitorAlarmService.reportManualAlarm(manualAlarmReportVo, ManualAlarmSourceEnum.MINI_MONITOR);
  }

  /**
   * 根据车号查询对应站点负责人手机号
   *
   * @param vehicleBaseVO vehicleBaseVO
   */
  @PostMapping("/getStationPersonPhone")
  public HttpResult<MobileAttentionPhoneDTO> callCockpit(@RequestBody @Valid VehicleBaseVO vehicleBaseVO) {
    return mobileUserService.getStationPersonPhone(vehicleBaseVO);
  }
}