/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.common;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.common.service.MobileUserJsfService;
import com.jdx.rover.monitor.dto.mobile.MobileUserMaskPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserStationNoticeConfigDTO;
import com.jdx.rover.monitor.service.mobile.MobileUserService;
import com.jdx.rover.monitor.vo.mobile.MobileUserConfigVO;
import com.jdx.rover.monitor.vo.mobile.common.UserBaseVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/17 20:23
 * @description 运营端用户配置服务
 */
@Service
@RequiredArgsConstructor
public class MobileUserJsfServiceProvider extends AbstractProvider<MobileUserJsfService> implements MobileUserJsfService {

    /**
     * 事件记录服务
     */
    private final MobileUserService mobileUserService;

    @Override
    @ServiceInfo(name = "获取车辆搜索记录", webUrl = "/mobile/applet/user/getVehicleSearchRecord")
    public HttpResult<List<String>> getVehicleSearchRecord() {
        return JsfResponse.response(mobileUserService::getVehicleSearchRecord);
    }

    @Override
    @ServiceInfo(name = "新增车辆搜索记录", webUrl = "/mobile/applet/user/addVehicleSearchRecord")
    public HttpResult<Void> addVehicleSearchRecord(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> mobileUserService.addSearchRecord(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "清空车辆搜索记录", webUrl = "/mobile/applet/user/clearVehicleSearchRecord")
    public HttpResult<Void> clearVehicleSearchRecord() {
        return JsfResponse.response(mobileUserService::clearVehicleSearchRecord);
    }

    @Override
    @ServiceInfo(name = "获取配置站点列表", webUrl = "/mobile/applet/user/getConfigStationCrashList")
    public HttpResult<List<MobileUserStationNoticeConfigDTO>> getConfigStationCrashList() {
        return JsfResponse.response(mobileUserService::getConfigStationCrashList);
    }

    @Override
    @ServiceInfo(name = "更新配置站点", webUrl = "/mobile/applet/user/updateConfig")
    public HttpResult<Void> updateConfig(MobileUserConfigVO mobileUserConfigVO) {
        return mobileUserService.updateConfigStation(mobileUserConfigVO);
    }

    @Override
    @ServiceInfo(name = "根据userName获取掩码手机号", webUrl = "/mobile/applet/user/getMaskPhone")
    public HttpResult<MobileUserMaskPhoneDTO> getMaskPhone(UserBaseVO userBaseVO) {
        return JsfResponse.response(() -> mobileUserService.getMaskPhone(userBaseVO.getUserName()));
    }
}
