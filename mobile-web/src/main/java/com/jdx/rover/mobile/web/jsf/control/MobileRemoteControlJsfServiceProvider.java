/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.mobile.web.jsf.control.service.MobileRemoteControlJsfService;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/17 21:24
 * @description 运营端远程指令
 */
@Service
@RequiredArgsConstructor
public class MobileRemoteControlJsfServiceProvider extends AbstractProvider<MobileRemoteControlJsfService> implements MobileRemoteControlJsfService {

    /**
     * 遥控服务
     */
    private final MiniMonitorCommandService miniMonitorCommandService;

    @Override
    @ServiceInfo(name = "接管车辆", webUrl = "/mobile/applet/remoteControl/open")
    public HttpResult<Object> openRemoteControl(MiniMonitorRemoteCommandVO emergencyStopCommandVO) {
        return miniMonitorCommandService.postEmergencyStopRequest(emergencyStopCommandVO);
    }

    @Override
    @ServiceInfo(name = "急刹车辆", webUrl = "/mobile/applet/remoteControl/brake")
    public HttpResult<Object> brakeRemoteControl(MiniMonitorRemoteCommandVO emergencyBrakeCommandVO) {
        return miniMonitorCommandService.postEmergencyBrakeRequest(emergencyBrakeCommandVO);
    }

    @Override
    @ServiceInfo(name = "恢复车辆", webUrl = "/mobile/applet/remoteControl/quit")
    public HttpResult<Object> quitRemoteControl(MiniMonitorRemoteCommandVO recoveryCommandVO) {
        return miniMonitorCommandService.postRecoveryRequest(recoveryCommandVO);
    }
}
