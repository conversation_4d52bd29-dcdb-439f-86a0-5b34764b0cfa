/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorMetadataPositionDTO;
import com.jdx.rover.monitor.enums.UserMetaDataEnum;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 运营端导航跟车服务
 * </p>
 *
 *  当前小程序端未调用
 *
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/mobile/applet/nav")
@RestController
@RequiredArgsConstructor
@Deprecated
public class MobileNavigationController {

  /**
   * 地图车辆服务
   */
  private final MonitorMapInfoService monitorMapInfoService;

  /**
   * 获取车辆实时位置
   *
   * @param vehicleName 车号
   * @param positionType 坐标类型
   */
  @GetMapping("/getVehiclePosition/{vehicleName}")
  public HttpResult getVehiclePosition(@PathVariable(value = "vehicleName") String vehicleName,
                                            @RequestParam(value = "positionType", defaultValue = "WGS84") String positionType) {
    MonitorMetadataPositionDTO metadataPositionDto = monitorMapInfoService.getMetaDataPositionInfo(UserMetaDataEnum.VEHICLE.getType(), vehicleName, positionType);
    return HttpResult.success(metadataPositionDto);
  }

}