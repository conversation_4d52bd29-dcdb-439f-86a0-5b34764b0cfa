package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.monitor.dto.mobile.message.AccidentOperateDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAbnormalVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentReportDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetRepairDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetUserTodoTaskListDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.MessageService;
import com.jdx.rover.monitor.vo.mobile.message.AccidentGenerateVO;
import com.jdx.rover.monitor.vo.mobile.message.RepairOperateVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 消息相关controller
 */
@RestController
@RequestMapping(value = "/mobile/applet/message")
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 获取用户代办事项列表
     * @return GetUserTodoTaskListDTO
     */
    @GetMapping("/getUserTodoTaskList")
    public GetUserTodoTaskListDTO getUserTodoTaskList() {
        return messageService.getUserTodoTaskList();
    }

    /**
     * 获取异常车辆信息
     * @return GetAbnormalVehicleListDTO
     */
    @GetMapping("/getAbnormalVehicleList")
    public GetAbnormalVehicleListDTO getAbnormalVehicleList() {
        return messageService.GetAbnormalVehicleListDTO();
    }

    /**
     * 获取事故卡片详情
     * @param messageId messageId
     * @return GetAccidentDetailDTO
     */
    @GetMapping("/getAccidentDetail")
    public GetAccidentDetailDTO getAccidentDetail(@RequestParam(value = "messageId") Integer messageId) {
        return messageService.getAccidentDetail(messageId);
    }

    /**
     * 获取维修卡片详情
     * @param messageId messageId
     * @return GetRepairDetailDTO
     */
    @GetMapping("/getRepairDetail")
    public GetRepairDetailDTO getRepairDetail(@RequestParam(value = "messageId") Integer messageId) {
        return messageService.getRepairDetail(messageId);
    }

    /**
     * 操作维修卡片
     * @param repairOperateVO repairOperateDTO
     * @return MonitorErrorEnum
     */
    @PostMapping("/repairOperate")
    public MonitorErrorEnum repairOperate(@Valid @RequestBody RepairOperateVO repairOperateVO) {
        return messageService.repairOperate(repairOperateVO);
    }

    /**
     * 操作事故卡片
     * @param accidentOperateDTO accidentOperateDTO
     * @return MonitorErrorEnum
     */
    @PostMapping("/accidentOperate")
    public MonitorErrorEnum accidentOperate(@Valid @RequestBody AccidentOperateDTO accidentOperateDTO) {
        return messageService.accidentOperate(accidentOperateDTO);
    }

    /**
     * 生成事故单
     * @param accidentGenerateVO accidentGenerateVO
     * @return MonitorErrorEnum
     */
    @PostMapping("/accidentGenerate")
    public MonitorErrorEnum accidentGenerate(@Valid @RequestBody AccidentGenerateVO accidentGenerateVO) {
        return messageService.accidentGenerate(accidentGenerateVO);
    }

    /**
     * 获取事故单详情
     * @param accidentNo accidentNo
     * @return GetAccidentReportDetailDTO
     */
    @GetMapping("/getAccidentReportDetail")
    public GetAccidentReportDetailDTO getAccidentReportDetail(@RequestParam(value = "accidentNo") String accidentNo) {
        return messageService.getAccidentReportDetail(accidentNo);
    }
}
