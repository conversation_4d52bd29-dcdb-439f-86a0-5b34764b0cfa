/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 运营端远程指令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/mobile/applet/remoteControl")
@RestController
@RequiredArgsConstructor
public class MobileRemoteControlController {

  /**
   * 遥控服务
   */
  private final MiniMonitorCommandService miniMonitorCommandService;

  /**
   * 接管车辆
   *
   */
  @PostMapping("/open")
  public HttpResult openRemoteControl(@RequestBody MiniMonitorRemoteCommandVO emergencyStopCommandVo) {
    return miniMonitorCommandService.postEmergencyStopRequest(emergencyStopCommandVo);
  }

  /**
   * 急刹车辆
   *
   */
  @PostMapping("/brake")
  public HttpResult brakeRemoteControl(@RequestBody MiniMonitorRemoteCommandVO emergencyStopCommandVo) {
    return miniMonitorCommandService.postEmergencyBrakeRequest(emergencyStopCommandVo);
  }

  /**
   * 恢复车辆
   *
   */
  @PostMapping("/quit")
  public HttpResult quitRemoteControl(@RequestBody MiniMonitorRemoteCommandVO recoveryCommandVo) {
    return miniMonitorCommandService.postRecoveryRequest(recoveryCommandVo);
  }


}