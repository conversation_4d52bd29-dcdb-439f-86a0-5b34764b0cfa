package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.box.GetBoxGridDTO;
import com.jdx.rover.monitor.service.mobile.VehicleBoxService;
import com.jdx.rover.monitor.vo.mobile.box.VehicleOpenBoxVO;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/16 13:57
 * @description 开舱门 Controller
 */
@RequestMapping(value = "/mobile/applet/box")
@RestController
@RequiredArgsConstructor
public class MobileBoxController {

    /**
     * 车辆货箱Service
     */
    private final VehicleBoxService vehicleBoxService;

    /**
     * 1、获取货箱格口列表
     *
     * @param vehicleName vehicleName
     * @return GetBoxGridDTO
     */
    @GetMapping("/getBoxGrid")
    public GetBoxGridDTO getBoxGrid(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return vehicleBoxService.getBoxGrid(vehicleName);
    }

    /**
     * 2、开箱&一键全开
     *
     * @param vehicleOpenBoxVO vehicleOpenBoxVO
     * @return HttpResult
     */
    @PostMapping("/openBox")
    public HttpResult openBox(@Validated @RequestBody VehicleOpenBoxVO vehicleOpenBoxVO) {
        vehicleBoxService.openBox(vehicleOpenBoxVO);
        return HttpResult.success();
    }
}
