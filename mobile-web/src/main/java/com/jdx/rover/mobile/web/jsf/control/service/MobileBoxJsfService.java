/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.box.GetBoxGridDTO;
import com.jdx.rover.monitor.vo.mobile.box.VehicleOpenBoxVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/18 11:16
 * @description 开舱门
 */
public interface MobileBoxJsfService {

    /**
     * 获取货箱格口列表
     */
    HttpResult<GetBoxGridDTO> getBoxGrid(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 开箱&一键全开
     */
    HttpResult<Void> openBox(@NotNull(message = "非法请求") @Valid VehicleOpenBoxVO vehicleOpenBoxVO);
}
