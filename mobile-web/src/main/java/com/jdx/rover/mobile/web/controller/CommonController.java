package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.common.CommonVehicleInfoDTO;
import com.jdx.rover.monitor.dto.mobile.common.FileDTO;
import com.jdx.rover.monitor.dto.mobile.common.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.service.mobile.CommonService;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import com.jdx.rover.monitor.vo.mobile.common.GetSelectVehicleListVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @description: CommonController
 * @author: wangguotai
 * @create: 2024-07-15 09:29
 **/
@RequestMapping(value = "/mobile/applet/common")
@RestController
@RequiredArgsConstructor
public class CommonController {

    private final CommonService commonService;

    private final EnumService enumService;

    /**
     * <p>
     * 前后端枚举同步
     * </p>
     *
     */
    @PostMapping("/enum_list")
    public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@Valid @RequestBody CommonDrownListVO commonDrownListVO) {
        Map<String, List<Map<String, Object>>> result = enumService.getEnumListMap(commonDrownListVO.getKeyList());
        return HttpResult.success(result);
    }

    /**
     * 文件上传
     *
     * @param file MultipartFile
     * @return FileDTO
     */
    @PostMapping(value = "/fileUpload")
    public FileDTO fileUpload(@RequestPart("file") MultipartFile file) {
        return commonService.fileUpload(file);
    }

    /**
     * 获取选择车辆列表
     *
     * @param getSelectVehicleListVO getSelectVehicleListVO
     * @return List<GetSelectVehicleListDTO>
     */
    @PostMapping(value = "/getSelectVehicleList")
    public List<GetSelectVehicleListDTO> getSelectVehicleList(@Valid @RequestBody GetSelectVehicleListVO getSelectVehicleListVO) {
        return commonService.getSelectVehicleList(getSelectVehicleListVO);
    }

    /**
     * 判断车辆能够提报维修单
     * @param vehicleName vehicleName
     * @return Boolean
     */
    @GetMapping(value = "require_check")
    public Boolean requireCheck(@RequestParam(value = "vehicleName") String vehicleName) {
        return commonService.requireCheck(vehicleName);
    }

    /**
     * 获取车辆基本信息（经纬度、系统状态、接管信息）
     *
     * @param vehicleName vehicleName
     * @return CommonVehicleInfoDTO
     */
    @GetMapping("/getVehicleInfo")
    public CommonVehicleInfoDTO getVehicleInfo(@Validated @NotBlank(message = "车辆名称不能为空") @RequestParam("vehicleName") String vehicleName) {
        return commonService.getVehicleInfo(vehicleName);
    }
}