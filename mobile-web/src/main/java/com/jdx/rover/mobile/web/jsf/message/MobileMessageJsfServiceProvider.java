/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.message;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.message.service.MobileMessageJsfService;
import com.jdx.rover.monitor.dto.mobile.message.AccidentOperateDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAbnormalVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentReportDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetRepairDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetUserTodoTaskListDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.MessageService;
import com.jdx.rover.monitor.vo.mobile.message.AccidentBaseVO;
import com.jdx.rover.monitor.vo.mobile.message.AccidentGenerateVO;
import com.jdx.rover.monitor.vo.mobile.message.MessageBaseVO;
import com.jdx.rover.monitor.vo.mobile.message.RepairOperateVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/18 13:31
 * @description 消息通知
 */
@Service
@RequiredArgsConstructor
public class MobileMessageJsfServiceProvider extends AbstractProvider<MobileMessageJsfService> implements MobileMessageJsfService {

    /**
     * MessageService
     */
    private final MessageService messageService;

    @Override
    @ServiceInfo(name = "获取用户代办事项列表", webUrl = "/mobile/applet/message/getUserTodoTaskList")
    public HttpResult<GetUserTodoTaskListDTO> getUserTodoTaskList() {
        return JsfResponse.response(messageService::getUserTodoTaskList);
    }

    @Override
    @ServiceInfo(name = "获取异常车辆信息", webUrl = "/mobile/applet/message/getAbnormalVehicleList")
    public HttpResult<GetAbnormalVehicleListDTO> getAbnormalVehicleList() {
        return JsfResponse.response(messageService::GetAbnormalVehicleListDTO);
    }

    @Override
    @ServiceInfo(name = "获取事故卡片详情", webUrl = "/mobile/applet/message/getAccidentDetail")
    public HttpResult<GetAccidentDetailDTO> getAccidentDetail(MessageBaseVO messageBaseVO) {
        return JsfResponse.response(() -> messageService.getAccidentDetail(messageBaseVO.getMessageId()));
    }

    @Override
    @ServiceInfo(name = "获取维修卡片详情", webUrl = "/mobile/applet/message/getRepairDetail")
    public HttpResult<GetRepairDetailDTO> getRepairDetail(MessageBaseVO messageBaseVO) {
        return JsfResponse.response(() -> messageService.getRepairDetail(messageBaseVO.getMessageId()));
    }

    @Override
    @ServiceInfo(name = "操作维修卡片", webUrl = "/mobile/applet/message/repairOperate")
    public HttpResult<Void> repairOperate(RepairOperateVO repairOperateVO) {
        MonitorErrorEnum monitorErrorEnum = messageService.repairOperate(repairOperateVO);
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "操作事故卡片", webUrl = "/mobile/applet/message/accidentOperate")
    public HttpResult<Void> accidentOperate(AccidentOperateDTO accidentOperateDTO) {
        MonitorErrorEnum monitorErrorEnum = messageService.accidentOperate(accidentOperateDTO);
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "生成事故单", webUrl = "/mobile/applet/message/accidentGenerate")
    public HttpResult<Void> accidentGenerate(AccidentGenerateVO accidentGenerateVO) {
        MonitorErrorEnum monitorErrorEnum = messageService.accidentGenerate(accidentGenerateVO);
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "获取事故单详情", webUrl = "/mobile/applet/message/getAccidentReportDetail")
    public HttpResult<GetAccidentReportDetailDTO> getAccidentReportDetail(AccidentBaseVO accidentBaseVO) {
        return JsfResponse.response(() -> messageService.getAccidentReportDetail(accidentBaseVO.getAccidentNo()));
    }
}
