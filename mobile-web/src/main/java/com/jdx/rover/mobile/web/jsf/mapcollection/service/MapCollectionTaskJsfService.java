/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mapcollection.StationStopRangeSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskSearchDTO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskRouteSaveVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskUpdateVO;
import jakarta.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:16
 * @description 地图采集勘查任务服务
 */
public interface MapCollectionTaskJsfService {

    /**
     * 获取勘查任务
     */
    HttpResult<TaskSearchDTO> getTaskList(@Valid TaskSearchVO taskSearchVO);

    /**
     * 创建勘查任务
     */
    HttpResult<TaskCreateDTO> createTask(@Valid TaskCreateVO taskCreateVO);

    /**
     * 编辑勘查任务
     */
    HttpResult<Void> updateTask(@Valid TaskUpdateVO taskUpdateVO);

    /**
     * 删除勘查任务
     */
    HttpResult<Void> deleteTask(@Valid TaskDeleteVO taskDeleteVO);

    /**
     * 开始勘查
     */
    HttpResult<Void> processStartTask(@Valid TaskBaseVO taskBaseVO);

    /**
     * 结束勘查
     */
    HttpResult<Void> processEndTask(@Valid TaskBaseVO taskBaseVO);

    /**
     * 提交勘查任务
     */
    HttpResult<Void> submitTask(@Valid TaskBaseVO taskBaseVO);

    /**
     * 获取附近站点和停靠点
     */
    HttpResult<StationStopRangeSearchDTO> getInitialPoints(@Valid PositionVO positionVO);

    /**
     * 暂存勘查线路
     */
    HttpResult<Void> saveTaskRoute(@Valid TaskRouteSaveVO taskRouteSaveVO);

    /**
     * 获取任务关联线路
     */
    HttpResult<TaskRouteDTO> getTaskRoute(@Valid TaskBaseVO taskBaseVO);

    /**
     * 删除勘查任务线路
     */
    HttpResult<Void> deleteTaskRoute(@Valid TaskBaseVO taskBaseVO);
}
