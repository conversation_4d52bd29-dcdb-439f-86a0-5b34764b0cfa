/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/17 21:44
 * @description MobileRemoteCommandJsfService
 */
public interface MobileRemoteCommandJsfService {

    /**
     * 指令操作
     */
    HttpResult<Void> send(@NotNull(message = "非法请求") @Valid SendVO sendVO);

    /**
     * 声音播报
     */
    HttpResult<Void> remoteShoutWord(@NotNull(message = "非法请求") @Valid MiniMonitorRemoteShoutVO miniMonitorRemoteShoutVO);

    /**
     * 远程喊话
     */
    HttpResult<Void> remoteVoiceWhistle(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);
}
