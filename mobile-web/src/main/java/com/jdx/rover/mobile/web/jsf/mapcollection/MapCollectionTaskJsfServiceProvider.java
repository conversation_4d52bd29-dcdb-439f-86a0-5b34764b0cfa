/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.mapcollection.StationStopRangeSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskSearchDTO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskRouteSaveVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskUpdateVO;
import com.jdx.rover.mobile.web.jsf.mapcollection.service.MapCollectionTaskJsfService;
import com.jdx.rover.monitor.service.mapcollection.MapTaskService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:54
 * @description 地图采集勘查任务服务
 */
@Service
@RequiredArgsConstructor
public class MapCollectionTaskJsfServiceProvider extends AbstractProvider<MapCollectionTaskJsfService> implements MapCollectionTaskJsfService {

    /**
     * CollectionTaskService
     */
    private final MapTaskService mapTaskService;

    @Override
    @ServiceInfo(name = "获取勘查任务", webUrl = "/mobile/exploration/task/list")
    public HttpResult<TaskSearchDTO> getTaskList(@Valid TaskSearchVO taskSearchVO) {
        return HttpResult.success(mapTaskService.getTaskList(taskSearchVO));
    }

    @Override
    @ServiceInfo(name = "创建勘查任务", webUrl = "/mobile/exploration/task/create")
    public HttpResult<TaskCreateDTO> createTask(@Valid TaskCreateVO taskCreateVO) {
        return HttpResult.success(mapTaskService.createTask(taskCreateVO));
    }

    @Override
    @ServiceInfo(name = "编辑勘查任务", webUrl = "/mobile/exploration/task/update")
    public HttpResult<Void> updateTask(@Valid TaskUpdateVO taskUpdateVO) {
        mapTaskService.updateTask(taskUpdateVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "删除勘查任务", webUrl = "/mobile/exploration/task/delete")
    public HttpResult<Void> deleteTask(@Valid TaskDeleteVO taskDeleteVO) {
        mapTaskService.deleteTask(taskDeleteVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "开始勘查", webUrl = "/mobile/exploration/task/process-start")
    public HttpResult<Void> processStartTask(@Valid TaskBaseVO taskBaseVO) {
        mapTaskService.processStartTask(taskBaseVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "结束勘查", webUrl = "/mobile/exploration/task/process-end")
    public HttpResult<Void> processEndTask(@Valid TaskBaseVO taskBaseVO) {
        mapTaskService.processEndTask(taskBaseVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "提交勘查线路", webUrl = "/mobile/exploration/task/submit")
    public HttpResult<Void> submitTask(@Valid TaskBaseVO taskBaseVO) {
        mapTaskService.submitTask(taskBaseVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "获取附近的站点和停靠点", webUrl = "/mobile/exploration/route/get-initial-points")
    public HttpResult<StationStopRangeSearchDTO> getInitialPoints(@Valid PositionVO positionVO) {
        return HttpResult.success(mapTaskService.getInitialPoints(positionVO));
    }

    @Override
    @ServiceInfo(name = "暂存勘查线路", webUrl = "/mobile/exploration/route/save")
    public HttpResult<Void> saveTaskRoute(@Valid TaskRouteSaveVO taskRouteSaveVO) {
        mapTaskService.saveTaskRoute(taskRouteSaveVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "获取勘查任务线路", webUrl = "/mobile/exploration/route/get")
    public HttpResult<TaskRouteDTO> getTaskRoute(@Valid TaskBaseVO taskBaseVO) {
        return HttpResult.success(mapTaskService.getTaskRoute(taskBaseVO));
    }

    @Override
    @ServiceInfo(name = "删除勘查任务线路", webUrl = "/mobile/exploration/route/delete")
    public HttpResult<Void> deleteTaskRoute(@Valid TaskBaseVO taskBaseVO) {
        mapTaskService.deleteTaskRoute(taskBaseVO);
        return HttpResult.success();
    }
}
