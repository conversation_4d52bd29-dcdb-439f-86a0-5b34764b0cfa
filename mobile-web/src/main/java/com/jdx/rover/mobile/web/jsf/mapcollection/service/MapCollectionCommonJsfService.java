/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mapcollection.CityStationDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskFilterDTO;
import com.jdx.rover.monitor.vo.mapcollection.TaskFilterVO;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:16
 * @description 地图采集公共服务
 */
public interface MapCollectionCommonJsfService {

    /**
     * 获取全部城市及站点
     */
    HttpResult<CityStationDTO> getAllCityAndStation();

    /**
     * 获取站点及任务创建人列表
     */
    HttpResult<TaskFilterDTO> getTaskFilter(TaskFilterVO taskFilterVO);
}
