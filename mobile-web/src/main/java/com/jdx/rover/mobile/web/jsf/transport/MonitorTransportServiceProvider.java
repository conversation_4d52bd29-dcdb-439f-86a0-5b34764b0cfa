package com.jdx.rover.mobile.web.jsf.transport;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.jsf.util.JsfLoginUtil;
import com.jdx.rover.mobile.web.jsf.transport.service.IMonitorTransportService;
import com.jdx.rover.monitor.dto.transport.GetCallCockpitVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetVehicleInfoDTO;
import com.jdx.rover.monitor.dto.transport.UserOperateCommandDTO;
import com.jdx.rover.monitor.dto.transport.VehicleRoutingDTO;
import com.jdx.rover.monitor.enums.JmqProducerTopicEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.service.jmq.produce.JmqProducerService;
import com.jdx.rover.monitor.vo.transport.RemotePowerOnOffVO;
import com.jdx.rover.monitor.vo.transport.VehicleRoutingVO;
import com.jdx.rover.monitor.vo.transport.CallCockpitVO;
import com.jdx.rover.monitor.vo.transport.CommandVO;
import com.jdx.rover.monitor.vo.transport.GetSelectVehicleListVO;
import com.jdx.rover.monitor.vo.transport.GetVehicleInfoVO;
import com.jdx.rover.monitor.vo.transport.RemoteControlCommandVO;
import com.jdx.rover.monitor.service.transport.MonitorTransportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: 接驳H5控车工具服务
 * @author: wangguotai
 * @create: 2025-02-06 15:12
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorTransportServiceProvider extends AbstractProvider<IMonitorTransportService> implements IMonitorTransportService {

    private final MonitorTransportService monitorTransportService;

    private final JmqProducerService jmqProducerService;

    @Override
    @ServiceInfo(name = "呼叫远驾获取车辆列表", webUrl = "/monitor/h5/getCallCockpitVehicleList")
    public HttpResult<GetCallCockpitVehicleListDTO> getCallCockpitVehicleList() {
        return JsfResponse.response(monitorTransportService::getCallCockpitVehicleList);
    }

    @Override
    @ServiceInfo(name = "呼叫远驾", webUrl = "/monitor/h5/callCockpit")
    public HttpResult<Void> callCockpit(CallCockpitVO callCockpitVO) {
        return JsfResponse.response(() -> monitorTransportService.callCockpit(callCockpitVO));
    }

    @Override
    @ServiceInfo(name = "获取车辆选择列表", webUrl = "/monitor/h5/getSelectVehicleList")
    public HttpResult<List<GetSelectVehicleListDTO>> getSelectVehicleList(GetSelectVehicleListVO getSelectVehicleListVO) {
        return JsfResponse.response(() -> monitorTransportService.getSelectVehicleList(getSelectVehicleListVO));
    }

    @Override
    @ServiceInfo(name = "获取车辆基本信息", webUrl = "/monitor/h5/getVehicleInfo")
    public HttpResult<GetVehicleInfoDTO> getVehicleInfo(GetVehicleInfoVO getVehicleInfoVO) {
        return JsfResponse.response(() -> monitorTransportService.getVehicleInfo(getVehicleInfoVO));
    }

    @Override
    @ServiceInfo(name = "开启遥控器", webUrl = "/monitor/h5/openRemoteControl")
    public HttpResult<Void> openRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        return JsfResponse.response(() -> monitorTransportService.openRemoteControl(remoteControlCommandVO));
    }

    @Override
    @ServiceInfo(name = "关闭遥控器", webUrl = "/monitor/h5/quitRemoteControl")
    public HttpResult<Void> quitRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        return JsfResponse.response(() -> monitorTransportService.quitRemoteControl(remoteControlCommandVO));
    }

    @Override
    @ServiceInfo(name = "急刹", webUrl = "/monitor/h5/brakeRemoteControl")
    public HttpResult<Void> brakeRemoteControl(RemoteControlCommandVO remoteControlCommandVO) {
        return JsfResponse.response(() -> monitorTransportService.brakeRemoteControl(remoteControlCommandVO));
    }

    @Override
    @ServiceInfo(name = "鸣笛", webUrl = "/monitor/h5/remoteVoiceWhistle")
    public HttpResult<Void> remoteVoiceWhistle(RemoteControlCommandVO remoteControlCommandVO) {
        return JsfResponse.response(() -> monitorTransportService.remoteVoiceWhistle(remoteControlCommandVO));
    }

    @Override
    @ServiceInfo(name = "指令操作", webUrl = "/monitor/h5/sendCommand")
    public HttpResult<Void> sendCommand(CommandVO commandVO) {
        return JsfResponse.response(() -> monitorTransportService.sendCommand(commandVO));
    }

    @Override
    @ServiceInfo(name = "开机、关机", webUrl = "/monitor/h5/remotePowerOnOff")
    public HttpResult<Void> remotePowerOnOff(RemotePowerOnOffVO remotePowerOnOffVO) {
        String username = JsfLoginUtil.getUsername();
        Date operateTime = new Date();
        MonitorErrorEnum monitorErrorEnum = monitorTransportService.remotePowerOnOff(remotePowerOnOffVO);
        // 发送mq消息
        try {
            UserOperateCommandDTO commandDTO = UserOperateCommandDTO.builder()
                    .operateUser(username)
                    .vehicleName(remotePowerOnOffVO.getVehicleName())
                    .operateTime(operateTime)
                    .source(RemoteCommandSourceEnum.WORKBENCH_MONITOR.getCommandSource())
                    .operateType(remotePowerOnOffVO.getOperateType())
                    .operateResult(MonitorErrorEnum.OK.equals(monitorErrorEnum) ? 1 : 0)
                    .build();
            jmqProducerService.sendMessage(JmqProducerTopicEnum.MONITOR_H5_TRANSPORT_COMMAND.getTopic(), commandDTO.getVehicleName(), commandDTO);
        } catch (Exception e) {
            log.error("发送mq消息失败", e);
        }
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "获取车辆规划路径以及已走里程信息", webUrl = "/monitor/h5/getVehicleRouting")
    public HttpResult<VehicleRoutingDTO> getVehicleRouting(VehicleRoutingVO vehicleRoutingVO) {
        return JsfResponse.response(() -> monitorTransportService.getVehicleRouting(vehicleRoutingVO));
    }

    @Override
    @ServiceInfo(name = "通用接驳获取车辆规划路径以及已走里程信息", webUrl = "/monitor/generalTransport/getVehicleRouting")
    public HttpResult<VehicleRoutingDTO> generalTransportGetVehicleRouting(VehicleRoutingVO vehicleRoutingVO) {
        return JsfResponse.response(() -> monitorTransportService.generalTransportGetVehicleRouting(vehicleRoutingVO));
    }
}