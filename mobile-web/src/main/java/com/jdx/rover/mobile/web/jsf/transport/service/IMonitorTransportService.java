package com.jdx.rover.mobile.web.jsf.transport.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.transport.GetCallCockpitVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.dto.transport.GetVehicleInfoDTO;
import com.jdx.rover.monitor.dto.transport.VehicleRoutingDTO;
import com.jdx.rover.monitor.vo.transport.RemotePowerOnOffVO;
import com.jdx.rover.monitor.vo.transport.VehicleRoutingVO;
import com.jdx.rover.monitor.vo.transport.CallCockpitVO;
import com.jdx.rover.monitor.vo.transport.CommandVO;
import com.jdx.rover.monitor.vo.transport.GetSelectVehicleListVO;
import com.jdx.rover.monitor.vo.transport.GetVehicleInfoVO;
import com.jdx.rover.monitor.vo.transport.RemoteControlCommandVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * @description: 接驳H5控车工具服务
 * @author: wangguotai
 * @create: 2025-02-06 14:43
 **/
public interface IMonitorTransportService {

    /**
     * 呼叫远驾-获取车辆列表
     *
     * @return GetCallCockpitVehicleListDTO
     */
    HttpResult<GetCallCockpitVehicleListDTO> getCallCockpitVehicleList();

    /**
     * 呼叫远驾
     *
     * @param callCockpitVO callCockpitVO
     * @return 处理结果
     */
    HttpResult<Void> callCockpit(@NotNull(message = "请求参数不能为空") @Valid CallCockpitVO callCockpitVO);

    /**
     * 获取车辆选择列表
     *
     * @param getSelectVehicleListVO getSelectVehicleListVO
     * @return List<GetSelectVehicleListDTO>
     */
    HttpResult<List<GetSelectVehicleListDTO>> getSelectVehicleList(@NotNull(message = "请求参数不能为空") @Valid GetSelectVehicleListVO getSelectVehicleListVO);

    /**
     * 获取车辆基本信息
     *
     * @param getVehicleInfoVO getVehicleInfoVO
     * @return GetVehicleInfoDTO
     */
    HttpResult<GetVehicleInfoDTO> getVehicleInfo(@NotNull(message = "请求参数不能为空") @Valid GetVehicleInfoVO getVehicleInfoVO);

    /**
     * 开启遥控器
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     * @return 处理结果
     */
    HttpResult<Void> openRemoteControl(@NotNull(message = "请求参数不能为空") @Valid RemoteControlCommandVO remoteControlCommandVO);

    /**
     * 关闭遥控器
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     * @return 处理结果
     */
    HttpResult<Void> quitRemoteControl(@NotNull(message = "请求参数不能为空") @Valid RemoteControlCommandVO remoteControlCommandVO);

    /**
     * 急刹
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     * @return 处理结果
     */
    HttpResult<Void> brakeRemoteControl(@NotNull(message = "请求参数不能为空") @Valid RemoteControlCommandVO remoteControlCommandVO);

    /**
     * 鸣笛
     *
     * @param remoteControlCommandVO remoteControlCommandVO
     * @return 处理结果
     */
    HttpResult<Void> remoteVoiceWhistle(@NotNull(message = "请求参数不能为空") @Valid RemoteControlCommandVO remoteControlCommandVO);

    /**
     * 指令操作
     *
     * @param commandVO commandVO
     * @return 处理结果
     */
    HttpResult<Void> sendCommand(@NotNull(message = "请求参数不能为空") @Valid CommandVO commandVO);

    /**
     * 远程开关机
     */
    HttpResult<Void> remotePowerOnOff(@NotNull(message = "非法请求") @Valid RemotePowerOnOffVO remotePowerOnOffVO);

    /**
     * 获取车辆路径信息
     */
    HttpResult<VehicleRoutingDTO> getVehicleRouting(@NotNull(message = "非法请求") @Valid VehicleRoutingVO vehicleRoutingVO);

    /**
     * 通用接驳获取车辆路径信息
     */
    HttpResult<VehicleRoutingDTO> generalTransportGetVehicleRouting(@NotNull(message = "非法请求") @Valid VehicleRoutingVO vehicleRoutingVO);
}