package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.mobile.RemoteLoginService;
import com.jdx.rover.monitor.vo.mobile.login.RemoteLoginVO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/7/16 21:49
 * @description 远程登录车端Controller
 */
@RequestMapping(value = "/mobile/applet/login")
@RestController
@RequiredArgsConstructor
public class MobileRemoteLoginController {

    /**
     * 远程登录车端Service
     */
    private final RemoteLoginService remoteLoginService;

    /**
     * 1、远程登录车端
     *
     * @param remoteLoginVO remoteLoginVO
     * @return HttpResult
     */
    @PostMapping("/remote")
    public HttpResult openBox(@Validated @RequestBody RemoteLoginVO remoteLoginVO) {
        remoteLoginService.remoteLogin(remoteLoginVO);
        return HttpResult.success();
    }
}
