package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.CommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @description: CommandController
 * @author: wangguotai
 * @create: 2024-07-18 13:24
 **/
@RequestMapping(value = "/mobile/applet/command")
@RequiredArgsConstructor
@RestController
public class CommandController {

    private final CommandService commandService;

    /**
     * 指令操作
     *
     * @param sendVO sendVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/send")
    public MonitorErrorEnum send(@Valid @RequestBody SendVO sendVO) {
        return commandService.send(sendVO);
    }

    /**
     * <p>
     * 远程喊话
     * </p>
     */
    @PostMapping("/broadCast/{vehicleName}")
    public HttpResult remoteShoutWord(@PathVariable(value = "vehicleName") String vehicleName, @RequestBody MiniMonitorRemoteShoutVO remoteShoutVo) {
        ParameterCheckUtility.checkNotNull(remoteShoutVo, "miniMonitorRemoteShoutVO");
        ParameterCheckUtility.checkNotNull(remoteShoutVo.getVoiceMsg(), "miniMonitorRemoteShoutVO#msg");
        return commandService.remoteBroadCastWord(vehicleName, remoteShoutVo);
    }

    /**
     * <p>
     * 远程喊话
     * </p>
     */
    @PostMapping("/whistle/{vehicleName}")
    public HttpResult remoteVoiceWhistle(@PathVariable(value = "vehicleName") String vehicleName) {
        return commandService.remoteVoiceWhistle(vehicleName);
    }
}