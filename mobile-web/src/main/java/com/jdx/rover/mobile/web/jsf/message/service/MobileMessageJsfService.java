/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.message.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.message.AccidentOperateDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAbnormalVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetAccidentReportDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetRepairDetailDTO;
import com.jdx.rover.monitor.dto.mobile.message.GetUserTodoTaskListDTO;
import com.jdx.rover.monitor.vo.mobile.message.AccidentBaseVO;
import com.jdx.rover.monitor.vo.mobile.message.AccidentGenerateVO;
import com.jdx.rover.monitor.vo.mobile.message.MessageBaseVO;
import com.jdx.rover.monitor.vo.mobile.message.RepairOperateVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/18 11:22
 * @description 消息通知
 */
public interface MobileMessageJsfService {

    /**
     * 获取用户代办事项列表
     */
    HttpResult<GetUserTodoTaskListDTO> getUserTodoTaskList();

    /**
     * 获取异常车辆信息
     */
    HttpResult<GetAbnormalVehicleListDTO> getAbnormalVehicleList();

    /**
     * 获取事故卡片详情
     */
    HttpResult<GetAccidentDetailDTO> getAccidentDetail(@NotNull(message = "非法请求") @Valid MessageBaseVO messageBaseVO);

    /**
     * 获取维修卡片详情
     */
    HttpResult<GetRepairDetailDTO> getRepairDetail(@NotNull(message = "非法请求") @Valid MessageBaseVO messageBaseVO);

    /**
     * 操作维修卡片
     */
    HttpResult<Void> repairOperate(@NotNull(message = "非法请求") @Valid RepairOperateVO repairOperateVO);

    /**
     * 操作事故卡片
     */
    HttpResult<Void> accidentOperate(@NotNull(message = "非法请求") @Valid AccidentOperateDTO accidentOperateDTO);

    /**
     * 生成事故单
     */
    HttpResult<Void> accidentGenerate(@NotNull(message = "非法请求") @Valid AccidentGenerateVO accidentGenerateVO);

    /**
     * 获取事故单详情
     */
    HttpResult<GetAccidentReportDetailDTO> getAccidentReportDetail(@NotNull(message = "非法请求") @Valid AccidentBaseVO accidentBaseVO);
}
