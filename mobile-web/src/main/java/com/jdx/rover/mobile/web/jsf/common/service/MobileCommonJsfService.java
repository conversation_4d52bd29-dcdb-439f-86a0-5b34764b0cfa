/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.common.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import com.jdx.rover.monitor.vo.mobile.common.GetSelectVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.dto.mobile.common.CommonVehicleInfoDTO;
import com.jdx.rover.monitor.dto.mobile.common.GetSelectVehicleListDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/13 22:00
 * @description MobileCommonJsfService
 */
public interface MobileCommonJsfService {

    /**
     * 前后端枚举同步
     */
    HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@NotNull(message = "非法请求") @Valid CommonDrownListVO commonDrownListVO);

    /**
     * 获取选择车辆列表
     */
    HttpResult<List<GetSelectVehicleListDTO>> getSelectVehicleList(@NotNull(message = "非法请求") @Valid GetSelectVehicleListVO getSelectVehicleListVO);

    /**
     * 判断车辆能够提报维修单
     */
    HttpResult<Boolean> requireCheck(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取车辆基本信息（经纬度、系统状态、接管信息）
     */
    HttpResult<CommonVehicleInfoDTO> getVehicleInfo(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);
}
