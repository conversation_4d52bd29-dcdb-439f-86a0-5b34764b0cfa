/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.mobile.login.RemoteLoginVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/17 15:54
 * @description 远程登录车端Controller
 */
public interface MobileRemoteLoginJsfService {

    /**
     * 远程登录车端
     */
    HttpResult<Void> remoteLogin(@NotNull(message = "非法请求") @Valid RemoteLoginVO remoteLoginVO);
}
