/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.mapcollection.MarkCreateDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.vo.mapcollection.MarkCreateVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkDeleteVO;
import com.jdx.rover.monitor.vo.mapcollection.MarkUpdateVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.mobile.web.jsf.mapcollection.service.MapCollectionMarkJsfService;
import com.jdx.rover.monitor.service.mapcollection.MapMarkService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:54
 * @description 地图采集标记服务
 */
@Service
@RequiredArgsConstructor
public class MapCollectionMarkJsfServiceProvider extends AbstractProvider<MapCollectionMarkJsfService> implements MapCollectionMarkJsfService {

    /**
     * CollectionMarkService
     */
    private final MapMarkService mapMarkService;

    @Override
    @ServiceInfo(name = "获取附近的标记", webUrl = "/mobile/exploration/mark/list")
    public HttpResult<MarkSearchDTO> getMarkList(@Valid PositionVO positionVO) {
        return HttpResult.success(mapMarkService.getMarkList(positionVO));
    }

    @Override
    @ServiceInfo(name = "新增标记", webUrl = "/mobile/exploration/mark/create")
    public HttpResult<MarkCreateDTO> createMark(@Valid MarkCreateVO markCreateVO) {
        return HttpResult.success(mapMarkService.createMark(markCreateVO));
    }

    @Override
    @ServiceInfo(name = "修改标记", webUrl = "/mobile/exploration/mark/update")
    public HttpResult<Void> updateMark(@Valid MarkUpdateVO markUpdateVO) {
        mapMarkService.updateMark(markUpdateVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "删除标记", webUrl = "/mobile/exploration/mark/delete")
    public HttpResult<Void> deleteMark(@Valid MarkDeleteVO markDeleteVO) {
        mapMarkService.deleteMark(markDeleteVO);
        return HttpResult.success();
    }
}
