/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.call.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.call.MobileSafetyOfficerDTO;
import com.jdx.rover.monitor.dto.mobile.call.MobileAttentionPhoneDTO;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.mobile.common.UserBaseVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18 10:59
 * @description 运营端呼叫服务
 */
public interface MobileOperationCallService {

    /**
     * 获取一线安全员列表
     */
    HttpResult<List<MobileSafetyOfficerDTO>> getSiteUserList(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 获取用户手机号
     */
    HttpResult<UserExtendInfoDTO> getUserPhone(@NotNull(message = "非法请求") @Valid UserBaseVO userBaseVO);

    /**
     * 呼叫云端智驾
     */
    HttpResult<Void> callCockpit(@NotNull(message = "非法请求") @Valid MonitorManualAlarmReportVO monitorManualAlarmReportVO);

    /**
     * 根据车号查询对应站点负责人手机号
     */
    HttpResult<MobileAttentionPhoneDTO> getStationPersonPhone(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);
}
