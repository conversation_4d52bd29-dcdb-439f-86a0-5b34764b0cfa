/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.freedrive;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.freedrive.service.MobileFreeDriveJsfService;
import com.jdx.rover.monitor.dto.mobile.freeDrive.ArbitraryNavigationDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetMapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetStopListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.NavigationDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.FreeDriveService;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.ArbitraryNavigationVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetMapVehicleVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetStopListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.NavigationVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/18 10:41
 * @description 自由跑行
 */
@Service
@RequiredArgsConstructor
public class MobileFreeDriveJsfServiceProvider extends AbstractProvider<MobileFreeDriveJsfService> implements MobileFreeDriveJsfService{

    /**
     * FreeDriveService
     */
    private final FreeDriveService freeDriveService;

    @Override
    @ServiceInfo(name = "获取车辆列表", webUrl = "/mobile/applet/freeDrive/getVehicleList")
    public HttpResult<List<GetVehicleListDTO>> getVehicleList(GetVehicleListVO getVehicleListVO) {
        return JsfResponse.response(() -> freeDriveService.getVehicleList(getVehicleListVO));
    }

    @Override
    @ServiceInfo(name = "获取地图上车辆", webUrl = "/mobile/applet/freeDrive/getMapVehicle")
    public HttpResult<List<GetMapVehicleDTO>> getMapVehicle(GetMapVehicleVO getMapVehicleVO) {
        return JsfResponse.response(() -> freeDriveService.getMapVehicle(getMapVehicleVO));
    }

    @Override
    @ServiceInfo(name = "车辆选择校验", webUrl = "/mobile/applet/freeDrive/checkVehicle")
    public HttpResult<Void> checkVehicle(VehicleBaseVO vehicleBaseVO) {
        MonitorErrorEnum monitorErrorEnum = freeDriveService.checkVehicle(vehicleBaseVO.getVehicleName());
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "获取去往停靠点列表", webUrl = "/mobile/applet/freeDrive/getStopList")
    public HttpResult<List<GetStopListDTO>> getStopList(GetStopListVO getStopListVO) {
        return JsfResponse.response(() -> freeDriveService.getStopList(getStopListVO));
    }

    @Override
    @ServiceInfo(name = "立即出发", webUrl = "/mobile/applet/freeDrive/navigation")
    public HttpResult<NavigationDTO> navigation(NavigationVO navigationVO) {
        return JsfResponse.response(() -> freeDriveService.navigation(navigationVO));
    }

    @Override
    @ServiceInfo(name = "立即出发前往任意点", webUrl = "/mobile/applet/freeDrive/arbitrary-navigation")
    public HttpResult<ArbitraryNavigationDTO> arbitraryNavigation(ArbitraryNavigationVO arbitraryNavigationVO) {
        return JsfResponse.response(() -> freeDriveService.arbitraryNavigation(arbitraryNavigationVO));
    }
}
