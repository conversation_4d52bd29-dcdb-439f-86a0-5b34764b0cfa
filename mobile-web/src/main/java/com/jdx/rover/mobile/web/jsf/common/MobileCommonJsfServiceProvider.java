/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.common;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.common.service.MobileCommonJsfService;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import com.jdx.rover.monitor.vo.mobile.common.GetSelectVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.dto.mobile.common.CommonVehicleInfoDTO;
import com.jdx.rover.monitor.dto.mobile.common.GetSelectVehicleListDTO;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.service.mobile.CommonService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/17 10:58
 * @description MobileCommonJsfServiceImpl
 */
@Service
@RequiredArgsConstructor
public class MobileCommonJsfServiceProvider extends AbstractProvider<MobileCommonJsfService> implements MobileCommonJsfService {

    /**
     * CommonService
     */
    private final CommonService commonService;

    /**
     * EnumService
     */
    private final EnumService enumService;

    @Override
    @ServiceInfo(name = "前后端枚举同步", webUrl = "/mobile/applet/common/enum_list")
    public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(CommonDrownListVO commonDrownListVO) {
        return JsfResponse.response(() -> enumService.getEnumListMap(commonDrownListVO.getKeyList()));
    }

    @Override
    @ServiceInfo(name = "获取选择车辆列表", webUrl = "/mobile/applet/common/getSelectVehicleList")
    public HttpResult<List<GetSelectVehicleListDTO>> getSelectVehicleList(GetSelectVehicleListVO getSelectVehicleListVO) {
        return JsfResponse.response(() -> commonService.getSelectVehicleList(getSelectVehicleListVO));
    }

    @Override
    @ServiceInfo(name = "判断车辆能够提报维修单", webUrl = "/mobile/applet/common/require_check")
    public HttpResult<Boolean> requireCheck(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> commonService.requireCheck(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "获取车辆基本信息（经纬度、系统状态、接管信息）", webUrl = "/mobile/applet/common/getVehicleInfo")
    public HttpResult<CommonVehicleInfoDTO> getVehicleInfo(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> commonService.getVehicleInfo(vehicleBaseVO.getVehicleName()));
    }
}
