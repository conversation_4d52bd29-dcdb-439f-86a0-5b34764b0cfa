package com.jdx.rover.mobile.web.controller;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.dto.mobile.freeDrive.ArbitraryNavigationDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetMapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetStopListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.GetVehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.freeDrive.NavigationDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.FreeDriveService;
import com.jdx.rover.monitor.vo.mobile.freeDrive.ArbitraryNavigationVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetMapVehicleVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetStopListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.GetVehicleListVO;
import com.jdx.rover.monitor.vo.mobile.freeDrive.NavigationVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: FreeDriveController
 * @author: wangguotai
 * @create: 2024-07-15 18:41
 **/
@RequestMapping(value = "/mobile/applet/freeDrive")
@RestController
@RequiredArgsConstructor
public class FreeDriveController {

    private final FreeDriveService freeDriveService;

    /**
     * 获取车辆列表
     *
     * @return List<GetVehicleListDTO>
     */
    @PostMapping(value = "/getVehicleList")
    public List<GetVehicleListDTO> getVehicleList(@Valid @RequestBody GetVehicleListVO getVehicleListVO) {
        return freeDriveService.getVehicleList(getVehicleListVO);
    }

    /**
     * 获取地图上车辆
     *
     * @return List<GetMapVehicleDTO>
     */
    @PostMapping(value = "/getMapVehicle")
    public List<GetMapVehicleDTO> getMapVehicle(@Valid @RequestBody GetMapVehicleVO getMapVehicleVO) {
        return freeDriveService.getMapVehicle(getMapVehicleVO);
    }

    /**
     * 车辆选择校验
     *
     * @param vehicleName vehicleName
     * @return MonitorErrorEnum
     */
    @GetMapping(value = "/checkVehicle")
    public MonitorErrorEnum checkVehicle(@RequestParam(value = "vehicleName") String vehicleName) {
        ParameterCheckUtility.checkNotEmptyAfterTrimming(vehicleName, "vehicleName");
        return freeDriveService.checkVehicle(vehicleName);
    }

    /**
     * 获取去往停靠点列表
     *
     * @param getStopListVO getStopListVO
     * @return List<GetStopListDTO>
     */
    @PostMapping(value = "/getStopList")
    public List<GetStopListDTO> getStopList(@Valid @RequestBody GetStopListVO getStopListVO) {
        return freeDriveService.getStopList(getStopListVO);
    }

    /**
     * 立即出发
     *
     * @param navigationVO navigationVO
     * @return NavigationDTO
     */
    @PostMapping(value = "/navigation")
    public NavigationDTO navigation(@Valid @RequestBody NavigationVO navigationVO) {
        return freeDriveService.navigation(navigationVO);
    }

    /**
     * 立即出发前往任意点
     *
     * @param arbitraryNavigationVO arbitraryNavigationVO
     * @return ArbitraryNavigationDTO
     */
    @PostMapping(value = "/arbitrary-navigation")
    public ArbitraryNavigationDTO arbitraryNavigation(@Valid @RequestBody ArbitraryNavigationVO arbitraryNavigationVO) {
        return freeDriveService.arbitraryNavigation(arbitraryNavigationVO);
    }
}