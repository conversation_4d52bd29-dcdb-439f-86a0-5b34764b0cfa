/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.common.service;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mobile.MobileUserConfigDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserMaskPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.MobileUserStationNoticeConfigDTO;
import com.jdx.rover.monitor.vo.mobile.MobileUserConfigVO;
import com.jdx.rover.monitor.vo.mobile.common.UserBaseVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17 16:05
 * @description 运营端用户配置服务
 */
public interface MobileUserJsfService {

    /**
     * 获取车辆搜索记录
     */
    HttpResult<List<String>> getVehicleSearchRecord();

    /**
     * 新增车辆搜索记录
     */
    HttpResult<Void> addVehicleSearchRecord(@NotNull(message = "非法请求") @Valid VehicleBaseVO vehicleBaseVO);

    /**
     * 清空车辆搜索记录
     */
    HttpResult<Void> clearVehicleSearchRecord();

    /**
     * 获取配置站点列表
     */
    HttpResult<List<MobileUserStationNoticeConfigDTO>> getConfigStationCrashList();

    /**
     * 更新配置站点
     */
    HttpResult<Void> updateConfig(@NotNull(message = "非法请求") MobileUserConfigVO mobileUserConfigVO);

    /**
     * 根据userName获取掩码手机号
     */
    HttpResult<MobileUserMaskPhoneDTO> getMaskPhone(@NotNull(message = "非法请求") @Valid UserBaseVO userBaseVO);
}
