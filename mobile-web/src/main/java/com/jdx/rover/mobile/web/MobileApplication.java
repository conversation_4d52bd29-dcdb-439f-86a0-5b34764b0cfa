package com.jdx.rover.mobile.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @description: MobileApplication
 * @author: wangguotai
 * @create: 2024-07-11 18:42
 **/
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan(basePackages = {"com.jdx.rover.monitor.domain", "com.jdx.rover.monitor.repository", "com.jdx.rover.monitor.manager", "com.jdx.rover.monitor.service", "com.jdx.rover.mobile.web"})
@EnableFeignClients(basePackages = {"com.jdx.rover.monitor.repository.feign"})
@MapperScan("com.jdx.rover.monitor.repository")
@ImportResource(value = {"classpath:jmq.xml", "classpath:spring-ducc.xml"})
public class MobileApplication {

    public static void main(String[] args) {
        SpringApplication.run(MobileApplication.class, args);
    }
}