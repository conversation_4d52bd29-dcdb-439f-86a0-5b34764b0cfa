/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapcollection;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.mapcollection.CityStationDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskFilterDTO;
import com.jdx.rover.monitor.vo.mapcollection.TaskFilterVO;
import com.jdx.rover.mobile.web.jsf.mapcollection.service.MapCollectionCommonJsfService;
import com.jdx.rover.monitor.service.mapcollection.MapCommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:54
 * @description 地图采集公共服务
 */
@Service
@RequiredArgsConstructor
public class MapCollectionCommonJsfServiceProvider extends AbstractProvider<MapCollectionCommonJsfService> implements MapCollectionCommonJsfService {

    /**
     * CollectionCommonService
     */
    private final MapCommonService mapCommonService;

    @Override
    @ServiceInfo(name = "获取全部城市及站点", webUrl = "/mobile/exploration/common/get-city-and-station")
    public HttpResult<CityStationDTO> getAllCityAndStation() {
        return HttpResult.success(mapCommonService.getAllCityAndStation());
    }

    @Override
    @ServiceInfo(name = "获取站点及任务创建人列表", webUrl = "/mobile/exploration/common/station-user-filter")
    public HttpResult<TaskFilterDTO> getTaskFilter(TaskFilterVO taskFilterVO) {
        return HttpResult.success(mapCommonService.getTaskFilter(taskFilterVO));
    }
}
