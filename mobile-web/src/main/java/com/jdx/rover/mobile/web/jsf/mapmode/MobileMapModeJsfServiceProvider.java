/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.mapmode;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.mapmode.service.MobileMapModeJsfService;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleBasicDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehiclePathDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleRealtimeDTO;
import com.jdx.rover.monitor.dto.mobile.map.MapVehicleVersionDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleIssueDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleListDTO;
import com.jdx.rover.monitor.dto.mobile.map.InsuranceAttachmentDTO;
import com.jdx.rover.monitor.dto.mobile.map.VehicleInsuranceDTO;
import com.jdx.rover.monitor.service.mobile.MapModeService;
import com.jdx.rover.monitor.vo.mobile.common.PositionVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.monitor.vo.mobile.mapmode.VehicleInsuranceVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/11 19:22
 * @description 车辆地图模式
 */
@Service
@RequiredArgsConstructor
public class MobileMapModeJsfServiceProvider extends AbstractProvider<MobileMapModeJsfService> implements MobileMapModeJsfService {

    /**
     * 车辆地图模式
     */
    private final MapModeService mapModeService;

    @Override
    @ServiceInfo(name = "获取用户权限下车辆列表", webUrl = "/mobile/applet/map/getVehicleList")
    public HttpResult<List<VehicleListDTO>> getVehicleList() {
        String username = UserUtils.getAndCheckLoginUser();
        return JsfResponse.response(() ->  mapModeService.getVehicleList(username));
    }

    @Override
    @ServiceInfo(name = "获取地图上车辆", webUrl = "/mobile/applet/map/getMapVehicle")
    public HttpResult<List<MapVehicleDTO>> getMapVehicle(PositionVO positionVO) {
        return JsfResponse.response(() -> mapModeService.getMapVehicle(positionVO));
    }

    @Override
    @ServiceInfo(name = "获取车辆基础信息（用户是否有权限）", webUrl = "/mobile/applet/map/getVehicleBasic")
    public HttpResult<MapVehicleBasicDTO> getVehicleBasic(VehicleBaseVO vehicleBaseVO) {
        String username = UserUtils.getAndCheckLoginUser();
        return JsfResponse.response(() -> mapModeService.getVehicleBasic(vehicleBaseVO.getVehicleName(), username));
    }

    @Override
    @ServiceInfo(name = "获取工单信息（运维工单）", webUrl = "/mobile/applet/map/getVehicleIssue")
    public HttpResult<VehicleIssueDTO> getVehicleIssue(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() ->  mapModeService.getVehicleIssue(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "获取车辆版本信息", webUrl = "/mobile/applet/map/getVehicleVersion")
    public HttpResult<MapVehicleVersionDTO> getVehicleVersion(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> mapModeService.getVehicleVersion(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "获取实时信息", webUrl = "/mobile/applet/map/getVehicleRealTime")
    public HttpResult<MapVehicleRealtimeDTO> getVehicleRealTime(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> mapModeService.getVehicleRealTime(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "获取规划路径", webUrl = "/mobile/applet/map/getVehiclePath")
    public HttpResult<MapVehiclePathDTO> getVehiclePath(VehicleBaseVO vehicleBaseVO) {
        return JsfResponse.response(() -> mapModeService.getVehiclePath(vehicleBaseVO.getVehicleName()));
    }

    @Override
    @ServiceInfo(name = "获取车辆生效中保单信息列表", webUrl = "/mobile/applet/map/getVehicleInsurance")
    public HttpResult<List<VehicleInsuranceDTO>> getVehicleEffectiveInsuranceList(VehicleInsuranceVO vehicleInsuranceVO) {
        return JsfResponse.response(() -> mapModeService.getVehicleEffectiveInsuranceList(vehicleInsuranceVO));
    }

    @Override
    @ServiceInfo(name = "获取保单文件外链", webUrl = "/mobile/applet/map/getInsuranceAttachmentFileUrl")
    public HttpResult<InsuranceAttachmentDTO> getPolicyAttachmentFileUrl(VehicleInsuranceVO vehicleInsuranceVO) {
        return JsfResponse.response(() -> mapModeService.getPolicyAttachmentFileUrl(vehicleInsuranceVO));
    }
}
