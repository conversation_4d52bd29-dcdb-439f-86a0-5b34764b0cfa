/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.mobile.web.jsf.control.service.MobileRemoteLoginJsfService;
import com.jdx.rover.monitor.service.mobile.RemoteLoginService;
import com.jdx.rover.monitor.vo.mobile.login.RemoteLoginVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/17 15:55
 * @description 远程登录车端
 */
@Service
@RequiredArgsConstructor
public class MobileRemoteLoginJsfServiceProvider extends AbstractProvider<MobileRemoteLoginJsfService> implements MobileRemoteLoginJsfService {

    /**
     * 远程登录车端Service
     */
    private final RemoteLoginService remoteLoginService;

    @Override
    @ServiceInfo(name = "远程登录车端", webUrl = "/mobile/applet/login/remote")
    public HttpResult<Void> remoteLogin(RemoteLoginVO remoteLoginVO) {
        return JsfResponse.response(() -> remoteLoginService.remoteLogin(remoteLoginVO));
    }
}
