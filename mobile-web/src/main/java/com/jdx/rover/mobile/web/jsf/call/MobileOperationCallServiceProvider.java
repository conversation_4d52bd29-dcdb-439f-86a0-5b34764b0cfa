/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.call;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.mobile.web.jsf.call.service.MobileOperationCallService;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.dto.mobile.call.MobileAttentionPhoneDTO;
import com.jdx.rover.monitor.dto.mobile.call.MobileSafetyOfficerDTO;
import com.jdx.rover.monitor.service.mobile.MobileUserService;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.mobile.common.UserBaseVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import com.jdx.rover.permission.domain.dto.basic.UserExtendInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/18 10:59
 * @description 运营端呼叫服务
 */
@Service
@RequiredArgsConstructor
public class MobileOperationCallServiceProvider extends AbstractProvider<MobileOperationCallService> implements MobileOperationCallService {

    /**
     * 告警服务接口
     */
    private final MonitorAlarmService monitorAlarmService;

    /**
     * 用户服务接口
     */
    private final MobileUserService mobileUserService;

    @Override
    @ServiceInfo(name = "获取一线安全员列表", webUrl = "/mobile/applet/call/getSiteUserList")
    public HttpResult<List<MobileSafetyOfficerDTO>> getSiteUserList(VehicleBaseVO vehicleBaseVO) {
        return mobileUserService.getSiteUserList(vehicleBaseVO.getVehicleName());
    }

    @Override
    @ServiceInfo(name = "获取用户手机号", webUrl = "/mobile/applet/call/getUserPhone")
    public HttpResult<UserExtendInfoDTO> getUserPhone(UserBaseVO userBaseVO) {
        return mobileUserService.getUserInfo(userBaseVO.getUserName());
    }

    @Override
    @ServiceInfo(name = "呼叫云端智驾", webUrl = "/mobile/applet/call/callCockpit")
    public HttpResult<Void> callCockpit(MonitorManualAlarmReportVO monitorManualAlarmReportVO) {
        return monitorAlarmService.reportManualAlarm(monitorManualAlarmReportVO, ManualAlarmSourceEnum.MINI_MONITOR);
    }

    @Override
    @ServiceInfo(name = "根据车号查询对应站点负责人手机号", webUrl = "/mobile/applet/call/getStationPersonPhone")
    public HttpResult<MobileAttentionPhoneDTO> getStationPersonPhone(VehicleBaseVO vehicleBaseVO) {
        return mobileUserService.getStationPersonPhone(vehicleBaseVO);
    }
}
