server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: redis-xvmv718wpfnw-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
impassable-area:
  addUrl: http://xmapvis.jd.local/AddTnta
  delUrl: http://xmapvis.jd.local/DelTnta
  searchUrl: http://xmapvis.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs.jd.local/url/play/
  multiUrl: http://rover-video-lbs.jd.local/video/lbs/url/play/quantity
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
rover-map:
  rover-map-bucket-name: rover-map
  attachment-bucket-name: rover-jira
ducc:
  config:
    name: mobile_config
    uri: ucc://jdos_rover-mobile-web:<EMAIL>/v1/namespace/mobile_namespace_prod/config/mobile_config/profiles/product?longPolling=60000&necessary=true
jmq:
  address: nameserver.jmq.jd.local:80
  app: rovermobileweb
  password: cb31f1f02aca4727a7d1a3480400e921
monitor:
  jmq:
    provider:
      topic:
        monitor_accident_flow_event: monitor_accident_flow_event
        monitor_remote_command_log: monitor_remote_command_log
        shadow_tracking_event: rover_shadow_tracking_event
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm
        monitor_h5_transport_command: monitor_h5_transport_command
neolix:
  baseurl: https://scapi.neolix.net