spring:
  application:
    name: rover-mobile-web
  profiles:
    active: "@activatedProperties@"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driverClassName: com.mysql.cj.jdbc.Driver
          druid:
            initialSize: 1
            minIdle: 1
            maxActive: 20
            maxWait: 60000
            validationQuery: SELECT 1
        postgresql:
          driverClassName: org.postgresql.Driver
          druid:
            initialSize: 1
            minIdle: 1
            maxActive: 20
            maxWait: 60000
            validationQuery: SELECT 1
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  kafka:
    producer:
      retries: 5
    consumer:
      enable-auto-commit: true
      auto-commit-interval: 1000
      group-id: ${spring.application.name}
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:sqlmap/*.xml
  type-handlers-package: com.jdx.rover.monitor.domain.handler
  configuration:
    auto-mapping-behavior: FULL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
assist-jira-record:
  projectKey: JDXAD
  issueTypeId: 1
  priorityId: 3
  defectTypeId: 11505
  defectTypeValue: 用户体验问题/优化提升
  severityId: 11511
  severityValue: Major（一般缺陷）
  discoveryPhaseId: 12005
  discoveryPhaseValue: 用户反馈
  discoverSituationId: 11529
  discoverSituationValue: 否
  componentId: 27791
  scenario: 社会道路
  useCase: 运营
  versionId: 22084
  versionValue: unkown
logging:
  config: classpath:log4j2.xml
s3:
  access-key: 85C76AB2AB89077E8500CB6021A81EC7
  secret-key: EEDD3436DF9B9ED075A39DEE468F66A2
  endpoint: https://s3-internal.cn-north-1.jdcloud-oss.com
  out-endpoint: https://s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  signer-override: AWSS3V4SignerType
jira-service:
  userName: org.jdx.monitor1
  passWord: Zhenxinaini(521)
project:
  local:
    cache:
      localCacheEvictTopic: local:cache:evict
liteflow:
  ruleSource: config/flow.xml
rover-map:
  rover-map-bucket-name: rover-map-test
  attachment-bucket-name: rover-jira
jsf:
  provider:
    validation: true
#jmq
jmq:
  sendTimeout: 300
monitor:
  jmq:
    provider:
      topic:
        monitor_accident_flow_event: monitor_accident_flow_event_${spring.profiles.active}
        monitor_remote_command_log: monitor_remote_command_log_${spring.profiles.active}
        shadow_tracking_event: rover_shadow_tracking_event_${spring.profiles.active}
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm_${spring.profiles.active}
        monitor_h5_transport_command: monitor_h5_transport_command_${spring.profiles.active}
neolix:
  baseurl: https://scapi.test.neolix.net