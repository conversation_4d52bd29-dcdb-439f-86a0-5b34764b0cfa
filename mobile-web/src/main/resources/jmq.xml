<?xml version="1.0" encoding="UTF-8"?>
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:jmq="http://code.jd.com/schema/jmq" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.jd.com/schema/jmq http://code.jd.com/schema/jmq/jmq-1.1.xsd">
    <!--配置transport，每个transport实例对应一个APP，同一APP如果生产和消费多个主题，只需要配置一个transport实例即可-->
    <jmq:transport address="${jmq.address}" password="${jmq.password}" app="${jmq.app}" user="${jmq.app}"/>
    <!--配置producer-->
    <jmq:producer id="producer" retryTimes="2" transport="jmq.transport"/>
    <!--配置Consumer，messageListener bean需要实现com.jd.jmq.client.consumer.MessageListener接口，在onMessage方法中接收消息。-->
</beans>