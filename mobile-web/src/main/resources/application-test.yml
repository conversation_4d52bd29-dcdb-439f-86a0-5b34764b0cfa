server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************
          username: root
          password: 123456
        postgresql:
          url: *******************************************************
          username: postgres
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
  kafka:
    bootstrap-servers: *************:9092
impassable-area:
  addUrl: http://xmapvis-cloud-test.jd.local/AddTnta
  delUrl: http://xmapvis-cloud-test.jd.local/DelTnta
  searchUrl: http://xmapvis-cloud-test.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis-cloud-test.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs-staging.jdl.cn/url/play/
  multiUrl: http://rover-video-lbs-staging.jd.local/video/lbs/url/play/quantity
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
jmq:
  address: test-nameserver.jmq.jd.local:50088
  app: mobilewebtest
  password: d1c68e001a394e9f8dadc47001c54646
ducc:
  config:
    name: mobile_config
    uri: ucc://jdos_rover-mobile-web-test:<EMAIL>/v1/namespace/mobile_namespace/config/mobile_config/profiles/beta?longPolling=60000&necessary=true