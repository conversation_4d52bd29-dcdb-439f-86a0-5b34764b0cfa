server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-hjhb8wlo35rc-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: Zndh2018
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-klv9p39t1j-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-klv9p39t1j-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-klv9p39t1j-az1-2.jvessel-open-hb.jdcloud.com:9092
impassable-area:
  addUrl: http://xmapvis-cloud-test.jd.local/AddTnta
  delUrl: http://xmapvis-cloud-test.jd.local/DelTnta
  searchUrl: http://xmapvis-cloud-test.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis-cloud-test.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs-staging.jd.local/url/play/
  multiUrl: http://rover-video-lbs-staging.jd.local/video/lbs/url/play/quantity
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449

ducc:
  config:
    name: mobile_config
    uri: ucc://jdos_rover-mobile-web-test:<EMAIL>/v1/namespace/mobile_namespace/config/mobile_config/profiles/test1?longPolling=60000&necessary=true
jmq:
  address: nameserver.jmq.jd.local:80
  app: mobilewebtest
  password: d1c68e001a394e9f8dadc47001c54646