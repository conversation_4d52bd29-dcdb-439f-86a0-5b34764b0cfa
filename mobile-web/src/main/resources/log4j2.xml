<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="OFF">
	<Properties>
		<Property name="APP_NAME">rover-mobile-web</Property>
		<Property name="LOG_PATH">/export/Logs/${APP_NAME}</Property>
		<Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|V1|%X{PFTID}|%X{REQID}|${APP_NAME}|${ENV}|${REGION}|${GROUP}|${IP}|%X{JLOG_SOURCE_IP}|%pid|[%t]|%X{JLOG_LOAD_PRESS}|%X{JLOG_SWIM_LANE}|%X{PIN}|%X{IDENTITY_ID}|%X{UUID}|%c - %m%n</Property>
	</Properties>
	<Appenders>
		<!-- 1、日志输出到控制台 -->
		<Console name="Console" target="SYSTEM_OUT">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
		</Console>

		<!-- 2、日志输出到文件 INFO级别 每小时产生一个新的日志文件-->
		<RollingFile name="InfoHourRollingFile" fileName="${LOG_PATH}/app_info.log"
					 filePattern="${LOG_PATH}/info/$${date:yyyy-MM-dd}/app-%d{yyyy-MM-dd-HH}.log.gz">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<!-- 设置日志备份频率 -->
			<Policies>
				<TimeBasedTriggeringPolicy interval="3"/>
			</Policies>
		</RollingFile>

		<!-- 3、日志输出到文件 ERROR级别 每小时产生一个新的日志文件-->
		<RollingFile name="ErrorHourRollingFile" fileName="${LOG_PATH}/app_error.log"
					 filePattern="${LOG_PATH}/error/$${date:yyyy-MM-dd}/app-%d{yyyy-MM-dd-HH}.log.gz">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<!-- 设置日志备份频率 -->
			<Policies>
				<TimeBasedTriggeringPolicy interval="3"/>
			</Policies>
		</RollingFile>

		<!-- 4、日志输出到文件 每小时产生一个新的日志文件-->
		<RollingFile name="DailyRollingFile" fileName="${LOG_PATH}/app_daily.log"
					 filePattern="${LOG_PATH}/daily/$${date:yyyy-MM-dd}/app-%d{yyyy-MM-dd-HH}.log.gz">
			<!--过滤器设置输出的级别-->
			<ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
			<!-- 日志输出格式 -->
			<PatternLayout pattern="${LOG_PATTERN}"/>
			<!-- 设置日志备份频率 -->
			<Policies>
				<TimeBasedTriggeringPolicy interval="3"/>
			</Policies>
		</RollingFile>

	</Appenders>
	<Loggers>
		<!--
          1. 指定logger的设置，additivity是否遵循缺省的继承机制
          2. 当additivity="false"时，root中的配置就失灵了，不遵循缺省的继承机制
          3. 代码中使用Logger.getLogger("RealtimeLogger")获得此输出器，且不会使用根输出器
        -->
		<Logger name="RealtimeLogger" additivity="false" level="INFO">
			<appender-ref ref="DailyRollingFile"/>
		</Logger>

		<!--
          关闭kafka非业务日志，例如来自kafka-client包
        -->
		<Logger name="org.apache.kafka" level="OFF">
		</Logger>

        <!--
  			JSF日志级别改为 WARN 减少心跳日志打印
		-->
        <Logger name="com.jd.jsf" level="WARN">
        </Logger>

		<!-- 根logger的设置，若代码中未找到指定的logger，则会根据继承机制，使用根logger-->
		<Root level="INFO">
			<appender-ref ref="InfoHourRollingFile"/>
			<appender-ref ref="ErrorHourRollingFile"/>
			<appender-ref ref="Console"/>
		</Root>
	</Loggers>
</Configuration>