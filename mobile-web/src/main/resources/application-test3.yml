server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: ********************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-yh7thvwgyt4s-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-pqxxtsd6av-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-pqxxtsd6av-az1-2.jvessel-open-hb.jdcloud.com:9092
impassable-area:
  addUrl: http://xmapvis-cloud-test.jd.local/AddTnta
  delUrl: http://xmapvis-cloud-test.jd.local/DelTnta
  searchUrl: http://xmapvis-cloud-test.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis-cloud-test.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs-staging.jd.local/url/play/
  multiUrl: http://rover-video-lbs-staging.jd.local/video/lbs/url/play/quantity
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
jmq:
  address: nameserver.jmq.jd.local:80
  app: mobilewebtest
  password: d1c68e001a394e9f8dadc47001c54646