/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import com.jdx.rover.monitor.entity.MonitorUserOperationEntity;
import com.jdx.rover.monitor.entity.user.UserStatusDO;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.vehicle.command.RemoteOperationTypeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.command.RemoteCommandRecordManager;
import com.jdx.rover.monitor.manager.vehicle.SingleVehicleManager;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.user.UserStatusRepository;
import com.jdx.rover.monitor.service.event.TrackingEventCollectService;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.worker.service.kafka.shadow.AbstractEventListener;
import com.jdx.rover.monitor.worker.service.vehicle.VehicleOperateEventService;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import com.jdx.rover.ticket.api.api.IssueQueryApi;
import com.jdx.rover.ticket.api.dto.query.CockpitIssuesDTO;
import com.jdx.rover.ticket.api.dto.query.CockpitIssuesDTO.CockpitIssue;
import com.jdx.rover.ticket.api.vo.query.QueryCockpitVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 车辆指令日志监听
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MonitorRemoteCommandLogJmqConsumer extends AbstractEventListener implements MessageListener {

    private final RemoteCommandRecordManager remoteCommandRecordManager;

    private final SingleVehicleManager singleVehicleManager;

    private final TrackingEventCollectService trackingEventService;

    private final VehicleOperateEventService vehicleOperateEventService;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final UserStatusRepository userStatusRepository;

    private final RemoteCommandService remoteCommandJsfService;

    private final IssueQueryApi issueQueryApi;

    private final MonitorRemoteCommandService monitorRemoteCommandService;

    /**
     * 车辆遥控指令监听
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理下发指令消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息，解析并执行相应的远程控制命令。
     */
    private void handleOneMessage(String message) {
        MonitorUserOperationEntity monitorUserOperationEntity =
                JsonUtils.readValue(message, MonitorUserOperationEntity.class);
        if (monitorUserOperationEntity == null) {
            return;
        }
        try {
            remoteCommandRecordManager.addRecordLog(monitorUserOperationEntity);
            trackingEventService.pushUserEvent(monitorUserOperationEntity);
            vehicleOperateEventService.pushVehicleEvent(monitorUserOperationEntity);
        } catch (Exception e) {
            log.error("Add command record log exception", e);
        }

        handleRemoteJoystick(monitorUserOperationEntity);

        singleVehicleManager.pushSingleVehicleOperation(monitorUserOperationEntity.getVehicleName());
        if (StringUtils.equalsAny(monitorUserOperationEntity.getOperationType()
                , WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue()
                , WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), RemoteOperationTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.getValue()
                , RemoteOperationTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.getValue())) {
            singleVehicleManager.pushSingleVehicleTakeOverAndIssue(monitorUserOperationEntity.getVehicleName());
            singleVehicleManager.pushPncTaskAndTrafficLight(monitorUserOperationEntity.getVehicleName());
            singleVehicleManager.pushNoSignalIntersectionDTO(monitorUserOperationEntity.getVehicleName());
            singleVehicleManager.sendVehicleControlMqttMsg(monitorUserOperationEntity.getOperationType(), monitorUserOperationEntity.getTimestamp(), monitorUserOperationEntity.getVehicleName());
        }

        // 停靠困难事件工单所属用户急停/接管后，自动下发视同到达指令
        if (StringUtils.equalsAny(monitorUserOperationEntity.getOperationType(),
                WebsocketEventTypeEnum.REMOTE_REQUEST_EMERGENCY_STOP.getValue(), RemoteOperationTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.getValue())) {
            asArriveAfterTakeoverAndEmergencyStop(monitorUserOperationEntity.getVehicleName(), monitorUserOperationEntity.getUserName());
        }

    }

    /**
     * 处理平行驾驶接管
     *
     * @param userOperation
     */
    private void handleRemoteJoystick(MonitorUserOperationEntity userOperation) {
        if (!StringUtils.equalsAny(userOperation.getOperationType()
                , RemoteOperationTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.getValue()
                , RemoteOperationTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.getValue())) {
            return;
        }
//        String vehicleName = userOperation.getVehicleName();
//        Optional<VehicleTakeOverEntity> op = vehicleTakeOverRepository.getByKey(vehicleName);
//        if (Objects.equals(userOperation.getOperationType(), RemoteOperationTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.getValue())) {
//            VehicleTakeOverEntity vehicleTakeOverEntity = new VehicleTakeOverEntity();
//            vehicleTakeOverEntity.setOperationStatus(VehicleRemoteOperationStatusEnum.TAKEOVER.getOperationStatus());
//            vehicleTakeOverEntity.setUserName(userOperation.getUserName());
//            vehicleTakeOverEntity.setCommandSource(RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource());
//            UserStatusDO userStatusDo = userStatusRepository.get(userOperation.getUserName());
//            if (!Objects.isNull(userStatusDo)) {
//                vehicleTakeOverEntity.setCockpitNumber(java.util.Optional.ofNullable(userStatusDo.getCockpitNumber()).orElse(userStatusDo.getLastCockpitNumber()));
//            }
//            vehicleTakeOverRepository.save(vehicleName, vehicleTakeOverEntity);
//        }
//
//        if (op.isPresent() && Objects.equals(userOperation.getOperationType(), RemoteOperationTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.getValue())) {
//            VehicleTakeOverEntity vehicleTakeOverEntityDb = op.get();
//            if (Objects.equals(vehicleTakeOverEntityDb.getCommandSource(), RemoteCommandSourceEnum.REMOTE_JOYSTICK.getCommandSource())) {
//                vehicleTakeOverRepository.remove(userOperation.getVehicleName());
//            }
//        }
        singleVehicleManager.pushSingleVehicleTakeOverAndIssue(userOperation.getVehicleName());
    }

    /**
     * 停靠困难事件工单所属用户急停/接管后，自动下发视同到达指令
     *
     * @param vehicleName vehicleName
     * @param username    username
     */
    private void asArriveAfterTakeoverAndEmergencyStop(String vehicleName, String username) {
        if (StrUtil.isBlank(vehicleName) || StrUtil.isBlank(username)) {
            return;
        }

        // 查询当前用户是否入座座席
        java.util.Optional.ofNullable(userStatusRepository.get(username)).map(UserStatusDO::getCockpitNumber).ifPresent(cockpitNumber -> {
            // 查询座席当前工单列表
            QueryCockpitVO queryCockpitVO = new QueryCockpitVO();
            queryCockpitVO.setCockpitNumber(cockpitNumber);
            HttpResult<CockpitIssuesDTO> result = issueQueryApi.queryCockpit(queryCockpitVO);
            if (!HttpResult.isSuccess(result)) {
                return;
            }

            CockpitIssuesDTO cockpitIssuesDTO = result.getData();
            if (Objects.isNull(cockpitIssuesDTO)) {
                return;
            }

            List<CockpitIssue> issueList = cockpitIssuesDTO.getIssueList();
            if (CollUtil.isEmpty(issueList)) {
                return;
            }
            issueList.forEach(issueInfo -> {
                if (!StrUtil.equals(vehicleName, issueInfo.getVehicleName())) {
                    return;
                }

                // 若存在停靠困难事件，下发视同到达指令
                if (issueInfo.getEventList().contains(VehicleAlarmEnum.PARK_HARD.getName())) {
                    RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
                    remoteCommandVo.setVehicleName(vehicleName);
                    remoteCommandVo.setReceiveTimeStamp(new Date());
                    remoteCommandVo.setTransitTimeStamp(new Date());
                    remoteCommandJsfService.publishAsArrivedCommand(remoteCommandVo);
                    monitorRemoteCommandService.sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
                            WebsocketEventTypeEnum.REMOTE_REQUEST_AS_ARRIVED.getValue(), username, RemoteCommandSourceEnum.MONITOR.getCommandSource());
                }
            });
        });
    }
}
