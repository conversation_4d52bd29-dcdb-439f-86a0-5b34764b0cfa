server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************
          username: root
          password: 123456
        postgresql:
          url: *******************************************************
          username: postgres
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: *************
      password: jdlX2022@redis-test
  kafka:
    bootstrap-servers: *************:9092
impassable-area:
  addUrl: http://xmapvis-cloud-test.jd.local/AddTnta
  delUrl: http://xmapvis-cloud-test.jd.local/DelTnta
  searchUrl: http://xmapvis-cloud-test.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis-cloud-test.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: https://rover-video-lbs-dev.jdl.cn/url/play/
  multiUrl: http://rover-video-lbs-dev.jd.local/video/lbs/url/play/quantity
  snapshotUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/realtime
  historyPicUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/history
  historySearchUrl: http://rover-video-process-staging.jd.local/video/process/history/contain
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
jmq:
  password: 23eaac26e98b4af48f556af72f7c029f
  app: monitorwebtest
  address: test-nameserver.jmq.jd.local:50088
jsf:
  appId: 1048566
  appName: jdos_rover-monitor-web
  provider:
    validation: true
ducc:
  config:
    name: monitor_web_config
    uri: ucc://jdos_rover-monitor-web:<EMAIL>/v1/namespace/rover_monitor_web/config/monitor_web_config/profiles/test?longPolling=60000&necessary=true