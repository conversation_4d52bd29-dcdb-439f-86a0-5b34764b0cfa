server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos-beta.jd.local/
        username: nacos
        password: nacos-beta@jdlX2022
  data:
    redis:
      host: redis-hb3hpfz16cbi-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
      database: 8
  kafka:
    bootstrap-servers: broker-kafka-poiybufu79-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-poiybufu79-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
impassable-area:
  addUrl: http://xmapvis-cloud-test.jd.local/AddTnta
  delUrl: http://xmapvis-cloud-test.jd.local/DelTnta
  searchUrl: http://xmapvis-cloud-test.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis-cloud-test.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs-staging.jd.local/url/play/
  multiUrl: http://rover-video-lbs-staging.jd.local/video/lbs/url/play/quantity
  snapshotUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/realtime
  historyPicUrl: http://rover-video-process-staging.jd.local/video/process/snapshot/history
  historySearchUrl: http://rover-video-process-staging.jd.local/video/process/history/contain
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
jsf:
  provider:
    enableLog: false
  consumer:
    enableLog: false
ducc:
  config:
    name: monitor_web_config
    uri: ucc://jdos_rover-auto-monitor-web:<EMAIL>/v1/namespace/rover_monitor_web/config/monitor_web_config/profiles/beta?longPolling=60000&necessary=true