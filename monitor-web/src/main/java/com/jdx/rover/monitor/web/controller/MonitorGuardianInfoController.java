/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.google.common.base.Preconditions;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmDetailInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleExceptionInfoDTO;
import com.jdx.rover.monitor.search.MonitorGuardianInfoSearch;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.monitor.vo.GuardianVehicleAlarmInfoVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * This is a api interface for monitor data info under user.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/guardian")
@RestController
public class MonitorGuardianInfoController {

  @Autowired
  private MonitorGuardianInfoService monitorGuardianInfoService;

  /**
   * <p>
   * Search guardian alarm info by search entity.
   * </p>
   *
   * @param guardianInfoSearch The search entity for monitor alarm info.
   */
  @PostMapping ("/alarm/list")
  public HttpResult<PageDTO<GuardianVehicleAlarmInfoDTO>> getAlarmInfoList(@RequestBody MonitorGuardianInfoSearch guardianInfoSearch) {
    Preconditions.checkNotNull(guardianInfoSearch, "查询条件不能为空");
    Preconditions.checkNotNull(guardianInfoSearch.getStartTime(), "查询条件开始时间不能为空");
    Preconditions.checkNotNull(guardianInfoSearch.getEndTime(), "查询条件结束时间不能为空");
    return monitorGuardianInfoService.getAlarmInfoList(guardianInfoSearch, guardianInfoSearch);
  }

  /**
   * <p>
   * Search guardian alarm info by id.
   * </p>
   *
   */
  @GetMapping("/alarm/{id}")
  public HttpResult<GuardianVehicleAlarmDetailInfoDTO> getAlarmDetailInfoById(@PathVariable("id") Integer id) {
    return monitorGuardianInfoService.getAlarmDetailInfoById(id);
  }

  /**
   * <p>
   * Search guardian exception info by search entity.
   * </p>
   *
   * @param guardianInfoSearch The search entity for monitor exception info.
   */
  @PostMapping("/exception/list")
  public HttpResult<PageDTO<GuardianVehicleExceptionInfoDTO>> getExceptionInfoList(@RequestBody MonitorGuardianInfoSearch guardianInfoSearch) {
    Preconditions.checkNotNull(guardianInfoSearch, "查询条件不能为空");
    Preconditions.checkNotNull(guardianInfoSearch.getStartTime(), "查询条件开始时间不能为空");
    Preconditions.checkNotNull(guardianInfoSearch.getEndTime(), "查询条件结束时间不能为空");
    return monitorGuardianInfoService.getExceptionInfoList(guardianInfoSearch, guardianInfoSearch);
  }

  /**
   * <p>
   * Search guardian exception info by key.
   * </p>
   *
   */
  @GetMapping("/exception/{id}")
  public HttpResult<GuardianVehicleExceptionInfoDTO> getExceptionInfoById(@PathVariable("id") Integer id) {
    return monitorGuardianInfoService.getExceptionInfoById(id);
  }

  /**
   * <p>
   * Add guardian alarm jiraNo.
   * </p>
   *
   */
  @PostMapping("/alarm/update")
  public HttpResult<GuardianVehicleExceptionInfoDTO> updateExceptionInfoById(@Valid @RequestBody GuardianVehicleAlarmInfoVO vehicleAlarmInfoVo) {
    return monitorGuardianInfoService.update(vehicleAlarmInfoVo);
  }

  /**
   * <p>
   * Search guardian wheel info by key.
   * </p>
   *
   */
  @GetMapping("/wheel_info/{vehicleName}")
  public HttpResult<List<VehicleRealtimeWheelInfoDTO>> getVehicleWheelInfo(@PathVariable(value = "vehicleName") String vehicleName) {
    return monitorGuardianInfoService.getVehicleWheelInfo(vehicleName);
  }

    /**
   * <p>
   * Search guardian lauch state info by key.
   * </p>
   *
   */
  @GetMapping("/lauch_state/{vehicleName}")
  public HttpResult<VehicleLauchStateInfoDTO> getVehiclelauchStateInfo(@PathVariable(value = "vehicleName") String vehicleName) {
    return monitorGuardianInfoService.getVehiclelauchStateInfo(vehicleName);
  }

}