/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.schedule;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDetailDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleTravelDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleManager;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.web.MonitorScheduleStopService;
import com.jdx.rover.monitor.vo.MonitorScheduleStopRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.schedule.MonitorScheduleWebJsfService;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 调度接口
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorScheduleWebJsfServiceImpl extends AbstractProvider<MonitorScheduleWebJsfService> implements MonitorScheduleWebJsfService{

  private final VehicleScheduleManager vehicleScheduleManager;

  private final FlowExecutor flowExecutor;

  private final VehicleScheduleRepository vehicleScheduleRepository;

  private final MonitorScheduleStopService monitorScheduleStopService;

  @ServiceInfo(name = "测试增加信息", webUrl = "/monitor/web/schedule/add")
  public HttpResult<Boolean> addSchedule(ScheduleTask scheduleTask) {
    LiteflowResponse response = flowExecutor.execute2Resp(LiteFlowTaskEnum.SCHEDULECHAIN.getType(), scheduleTask, ScheduleTask.class);
    if (!response.isSuccess()) {
      return HttpResult.error();
    }
    MonitorScheduleEntity scheduleEntity = response.getSlot().getResponseData();
    if (scheduleEntity != null) {
      vehicleScheduleRepository.set(scheduleEntity);
    }
    return HttpResult.success(response.isSuccess());
  }

  @ServiceInfo(name = "调度信息", webUrl = "/monitor/web/schedule/detail")
  public HttpResult<MonitorScheduleDTO> getScheduleDetail(VehicleNameBasicVO vehicleNameBasicVo) {
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleNameBasicVo.getVehicleName()));
    return HttpResult.success(scheduleDtoMap.get(vehicleNameBasicVo.getVehicleName()));
  }

  @ServiceInfo(name = "调度信息", webUrl = "/monitor/web/schedule/realtime_schedule")
  public HttpResult<MonitorScheduleTravelDTO> getVehicleScheduleRealtimeInfo(VehicleNameBasicVO vehicleNameBasicVo) {
    MonitorScheduleTravelDTO scheduleDto = vehicleScheduleManager.getScheduleTravelInfo(vehicleNameBasicVo.getVehicleName());
    return HttpResult.success(scheduleDto);
  }

  @ServiceInfo(name = "调度停靠点信息", webUrl = "/monitor/web/schedule/stop/detail")
  public HttpResult<MonitorScheduleStopDetailDTO> getScheduleStopRealtimeInfo(MonitorScheduleStopRequestVO monitorScheduleStopRequestVo) {
    MonitorScheduleStopDetailDTO scheduleStopDto = monitorScheduleStopService.getScheduleStopState(monitorScheduleStopRequestVo);
    return HttpResult.success(scheduleStopDto);
  }

}
