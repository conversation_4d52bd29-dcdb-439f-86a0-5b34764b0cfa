/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.pda;

import com.google.common.base.Preconditions;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.*;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaBasicSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.PdaPageSearchVO;
import com.jdx.rover.monitor.api.web.jsf.service.MonitorPdaJsfService;
import com.jdx.rover.monitor.service.pda.PdaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 监控PDA设备服务接口
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorPdaJsfServiceImpl extends AbstractProvider<MonitorPdaJsfService> implements MonitorPdaJsfService {

  /**
   * PDA设备服务接口。
   */
  public final PdaService padService;

  /**
   * <p>
   * 获取模型下设备统计信息
   * </p>
   *
   */
  public HttpResult<List<PdaModuleDeviceStatisticDTO>> getModuleDeviceStatisticList() {
    return HttpResult.success(padService.getModuleDeviceStatisticList());
  }

  /**
   * <p>
   * 获取设备树
   * </p>
   *
   */
  public HttpResult<List<PdaGroupBasicInfoDTO>> getDeviceGroupTree() {
    return HttpResult.success(padService.getGroupTree());
  }

  /**
   * <p>
   * 分页请求Pda实时状态
   * </p>
   *
   * @param pdaPageSearchVo 分页请求入参
   */
  public HttpResult<PdaPageSearchDTO> pageSearchPdaDevice(PdaPageSearchVO pdaPageSearchVo) {
    Preconditions.checkNotNull(pdaPageSearchVo, "查询条件不能为空");
    Preconditions.checkNotNull(pdaPageSearchVo.getPageNum(), "查询条件分页数不能为空");
    Preconditions.checkNotNull(pdaPageSearchVo.getPageSize(), "查询条件分页条数不能为空");
    return HttpResult.success(padService.pageSearchList(pdaPageSearchVo));
  }

  /**
   * <p>
   * 获取设备基础详情
   * </p>
   *
   */
  public HttpResult<PdaBasicDetailInfoDTO> getDeviceBasic(PdaBasicSearchVO pdaBasicSearchVO) {
    return HttpResult.success(padService.getDeviceBasicInfo(pdaBasicSearchVO));
  }

  /**
   * <p>
   * 获取设备实时状态
   * </p>
   *
   */
  public HttpResult<PdaRealtimeStatusInfoDTO> getRealtimeStatus(PdaBasicSearchVO pdaBasicSearchVO) {
     return HttpResult.success(padService.getRealtimeStatus(pdaBasicSearchVO));
  }


}