/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.guardian;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmDetailInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleExceptionInfoDTO;
import com.jdx.rover.monitor.search.MonitorGuardianInfoSearch;
import com.jdx.rover.monitor.vo.BasicKeyVO;
import com.jdx.rover.monitor.vo.GuardianVehicleAlarmInfoVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor data info under guardian.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/02/20
 */
public interface MonitorGuardianInfoWebJsfService {

  public HttpResult<PageDTO<GuardianVehicleAlarmInfoDTO>> getAlarmInfoList(MonitorGuardianInfoSearch guardianInfoSearch);

  public HttpResult<GuardianVehicleAlarmDetailInfoDTO> getAlarmDetailInfoById(BasicKeyVO basicKeyVo);

  public HttpResult<PageDTO<GuardianVehicleExceptionInfoDTO>> getExceptionInfoList(MonitorGuardianInfoSearch guardianInfoSearch);

  public HttpResult<GuardianVehicleExceptionInfoDTO> getExceptionInfoById(BasicKeyVO basicKeyVo);

  public HttpResult<GuardianVehicleExceptionInfoDTO> updateExceptionInfoById(@Valid GuardianVehicleAlarmInfoVO vehicleAlarmInfoVo);

  public HttpResult<List<VehicleRealtimeWheelInfoDTO>> getVehicleWheelInfo(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult<VehicleLauchStateInfoDTO> getVehiclelauchStateInfo(VehicleNameBasicVO vehicleNameBasicVo);

}