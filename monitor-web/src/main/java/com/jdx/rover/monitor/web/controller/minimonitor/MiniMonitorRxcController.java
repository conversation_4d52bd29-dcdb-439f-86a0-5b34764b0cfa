/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MiniMonitorRxcService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.MiniMonitorWordAddVO;
import com.jdx.rover.monitor.vo.MiniMonitorWordDeleteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * This is a controller class for mini monitor remote xin call function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/rxc")
public class MiniMonitorRxcController {

  @Autowired
  private MiniMonitorRxcService miniMonitorRxcService;

  /**
   * <p>
   * This method helps to return word list.
   * </p>
   *
   * @return mini monitor word DTO list.
   */
  @GetMapping("/word_list")
  public HttpResult getWordList() {
    return miniMonitorRxcService.getWordList();
  }

  /**
   * <p>
   * This method helps to add word detail.
   * </p>
   *
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @PostMapping("/add")
  public HttpResult addWord(@RequestBody MiniMonitorWordAddVO miniMonitorWordAddVo) {
    ParameterCheckUtility.checkNotNull(miniMonitorWordAddVo, "miniMonitorWordAddVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(miniMonitorWordAddVo.getContent(), "miniMonitorWordAddVo#content");
    return miniMonitorRxcService.addWord(miniMonitorWordAddVo);
  }

  /**
   * <p>
   * This method helps to del word detail.
   * </p>
   *
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @PostMapping("/delete")
  public HttpResult deleteWord(@RequestBody MiniMonitorWordDeleteVO miniMonitorWordDeleteVo) {
    ParameterCheckUtility.checkNotNull(miniMonitorWordDeleteVo, "miniMonitorWordDeleteVo");
    ParameterCheckUtility.checkNotNull(miniMonitorWordDeleteVo.getId(), "miniMonitorWordAddVo#id");
    return miniMonitorRxcService.deleteWord(miniMonitorWordDeleteVo);
  }

  /**
   * <p>
   * 远程喊话
   * </p>
   *
   */
  @PostMapping("/broadCast/{vehicleName}")
  public HttpResult remoteShoutWord(@PathVariable(value = "vehicleName") String vehicleName, @RequestBody MiniMonitorRemoteShoutVO remoteShoutVo) {
    ParameterCheckUtility.checkNotNull(remoteShoutVo, "miniMonitorRemoteShoutVO");
    ParameterCheckUtility.checkNotNull(remoteShoutVo.getVoiceMsg(), "miniMonitorRemoteShoutVO#msg");
    return miniMonitorRxcService.remoteBroadCastWord(vehicleName, remoteShoutVo);
  }

  /**
   * <p>
   * 远程喊话
   * </p>
   *
   */
  @PostMapping("/whistle/{vehicleName}")
  public HttpResult remoteVoiceWhistle(@PathVariable(value = "vehicleName") String vehicleName) {
    return miniMonitorRxcService.remoteVoiceWhistle(vehicleName);
  }
}
