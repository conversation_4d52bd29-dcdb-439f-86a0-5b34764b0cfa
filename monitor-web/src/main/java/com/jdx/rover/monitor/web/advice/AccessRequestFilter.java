package com.jdx.rover.monitor.web.advice;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import com.jdx.rover.common.constant.HttpHeaderConstant;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.login.LoginUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.BufferedReader;
import java.io.IOException;

/**
 * AccessRequestFilter
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class AccessRequestFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        long receiveTime = System.currentTimeMillis();
        CustomContentCachingRequestWrapper requestWrapper = new CustomContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        String requestURI = request.getRequestURI();
        try {
            // 生成traceId
            MDC.put(HttpHeaderConstant.TRACE_ID, UUID.fastUUID().toString(true));

            // 打印请求日志
            String username = LoginUtils.getUsername("system");
            String requestParam = logRequest(requestWrapper);
            log.info("HttpRequest [{}] [{}::{}] receive:[param:{} body:{}]", username, request.getMethod(), requestURI, request.getQueryString(), requestParam);

            // 请求处理
            filterChain.doFilter(requestWrapper, responseWrapper);

            // 打印响应日志
            Long timeConsuming = System.currentTimeMillis() - receiveTime;
            log.info("HttpResponse [{}] [{}::{}ms] response:[{}]", username, requestURI, timeConsuming, logResponse(responseWrapper));
        } finally {
            responseWrapper.copyBodyToResponse();
            MDC.clear();
        }
    }

    /**
     * 请求报文获取
     *
     * @param request http 请求数据
     */
    private String logRequest(CustomContentCachingRequestWrapper request) {
        try {
            if (request.isJsonRequest()) {
                String body = getBody(request);
                if (StringUtils.isNotBlank(body)) {
                    return body;
                }
                return JsonUtils.writeValueAsString(request.getParameterMap());
            }
            return JsonUtils.writeValueAsString(request.getParameterMap());
        } catch (Exception e) {
            log.error("Get request param error - {}", e.getMessage(), e);
            return null;
        }
    }

    private String logResponse(ContentCachingResponseWrapper responseWrapper) {
        try {
            return new String(responseWrapper.getContentAsByteArray());
        } catch (Exception e) {
            log.error("响应数据获取失败", e);
        }
        return null;
    }


    /**
     * hutool的是jdk8版本,重写jdk17版本
     * 获取请求体<br>
     * 调用该方法后，getParam方法将失效
     *
     * @param request {@link ServletRequest}
     * @return 获得请求体
     * @since 4.0.2
     */
    private static String getBody(ServletRequest request) {
        try (final BufferedReader reader = request.getReader()) {
            return IoUtil.read(reader);
        } catch (IOException e) {
            throw new IORuntimeException(e);
        }
    }
}