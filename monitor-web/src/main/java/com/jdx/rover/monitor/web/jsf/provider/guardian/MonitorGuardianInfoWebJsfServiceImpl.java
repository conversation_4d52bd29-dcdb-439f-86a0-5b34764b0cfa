/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.guardian;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmDetailInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleAlarmInfoDTO;
import com.jdx.rover.monitor.dto.GuardianVehicleExceptionInfoDTO;
import com.jdx.rover.monitor.search.MonitorGuardianInfoSearch;
import com.jdx.rover.monitor.service.web.MonitorGuardianInfoService;
import com.jdx.rover.monitor.vo.BasicKeyVO;
import com.jdx.rover.monitor.vo.GuardianVehicleAlarmInfoVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.guardian.MonitorGuardianInfoWebJsfService;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleLauchStateInfoDTO;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleRealtimeWheelInfoDTO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor data info under guardian.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorGuardianInfoWebJsfServiceImpl extends AbstractProvider<MonitorGuardianInfoWebJsfService> implements MonitorGuardianInfoWebJsfService {

  private final MonitorGuardianInfoService monitorGuardianInfoService;

  @ServiceInfo(name = "告警列表", webUrl = "/monitor/web/guardian/alarm/list")
  public HttpResult<PageDTO<GuardianVehicleAlarmInfoDTO>> getAlarmInfoList(MonitorGuardianInfoSearch guardianInfoSearch) {
      return  monitorGuardianInfoService.getAlarmInfoList(guardianInfoSearch, guardianInfoSearch);
  }

  @ServiceInfo(name = "告警详情", webUrl = "/monitor/web/guardian/alarm")
  public HttpResult<GuardianVehicleAlarmDetailInfoDTO> getAlarmDetailInfoById(BasicKeyVO basicKeyVo) {
    return monitorGuardianInfoService.getAlarmDetailInfoById(basicKeyVo.getId());
  }

  @ServiceInfo(name = "异常列表", webUrl = "/monitor/web/guardian/exception/list")
  public HttpResult<PageDTO<GuardianVehicleExceptionInfoDTO>> getExceptionInfoList(MonitorGuardianInfoSearch guardianInfoSearch) {
    return monitorGuardianInfoService.getExceptionInfoList(guardianInfoSearch, guardianInfoSearch);
  }

  @ServiceInfo(name = "异常详情", webUrl = "/monitor/web/guardian/exception")
  public HttpResult<GuardianVehicleExceptionInfoDTO> getExceptionInfoById(BasicKeyVO basicKeyVo) {
    return monitorGuardianInfoService.getExceptionInfoById(basicKeyVo.getId());
  }

  @ServiceInfo(name = "告警更新", webUrl = "/monitor/web/guardian/alarm/update")
  public HttpResult<GuardianVehicleExceptionInfoDTO> updateExceptionInfoById(@Valid GuardianVehicleAlarmInfoVO vehicleAlarmInfoVo) {
    return monitorGuardianInfoService.update(vehicleAlarmInfoVo);
  }

  @ServiceInfo(name = "胎压", webUrl = "/monitor/web/guardian/wheel_info")
  public HttpResult<List<VehicleRealtimeWheelInfoDTO>> getVehicleWheelInfo(VehicleNameBasicVO vehicleNameBasicVo) {
    return monitorGuardianInfoService.getVehicleWheelInfo(vehicleNameBasicVo.getVehicleName());
  }

  @ServiceInfo(name = "启动信息", webUrl = "/monitor/web/guardian/lauch_state")
  public HttpResult<VehicleLauchStateInfoDTO> getVehiclelauchStateInfo(VehicleNameBasicVO vehicleNameBasicVo) {
    return monitorGuardianInfoService.getVehiclelauchStateInfo(vehicleNameBasicVo.getVehicleName());
  }

}