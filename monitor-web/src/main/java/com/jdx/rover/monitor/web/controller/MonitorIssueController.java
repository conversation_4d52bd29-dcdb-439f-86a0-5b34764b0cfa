/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.repository.mapper.IssueAlarmRecordMapper;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.service.web.MonitorIssueService;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.IssueVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a api interface for monitor issue.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/issue")
@RestController
public class MonitorIssueController {

  @Autowired
  private MonitorIssueService monitorIssueService;

  @Autowired
  private IssueAlarmRecordMapper recordMapper;

  /**
   * <p>
   * Get the issue detail of the issue no.
   * </p>
   *
   * @return The corresponding issue detail data transform object.
   */
  @GetMapping("/detail")
  public HttpResult getIssueDetail(@RequestParam(value = "vehicleName") String vehicleName, @RequestParam(value = "issueNo") String issueNo) {
    return HttpResult.success(monitorIssueService.getIssueDetail(vehicleName, issueNo));
  }

  /**
   * <p>
   * Get the issue detail of the issue no.
   * </p>
   *
   * @param vehicleName The identity for getting the issue detail.
   * @return The corresponding issue detail data transform object.
   */
  @GetMapping("/availible/{vehicleName}")
  public HttpResult getAvailibleIssue(@PathVariable(value = "vehicleName") String vehicleName) {
    return HttpResult.success(monitorIssueService.getAvailibleIssue(vehicleName));
  }

  /**
   * <p>
   * monitor issue add.
   * </p>
   *
   * @param monitorIssueAddVo The monitor issue request view object.
   */
  @PostMapping("/add")
  public HttpResult add(IssueVO monitorIssueAddVo) {
    return monitorIssueService.operateAddIssue(monitorIssueAddVo, UserUtils.getLoginUser());
  }

  /**
   * <p>
   * monitor issue update.
   * </p>
   *
   * @param issueVo The monitor issue request view object.
   * @return The remote request response dto.
   */
  @PostMapping("/update")
  public HttpResult update(@Valid @RequestBody IssueVO issueVo) {
    ParameterCheckUtility.checkNotNull(issueVo, "issueVo");
    return monitorIssueService.update(issueVo);
  }

  /**
   * <p>
   * Search issueDetail by search entity.
   * </p>
   *
   * @param issueRecordSearch The search entity for MonitorIssueDetailDto.
   * @return The correspond MonitorIssueDetailDto data transform objects.
   * @throws IllegalArgumentException If the argument doesn't meet the requirement.
   */
  @PostMapping("/list")
  public HttpResult search(@Valid @RequestBody IssueRecordSearch issueRecordSearch) {
    return HttpResult.success(monitorIssueService.search(issueRecordSearch));
  }

  /**
   * <p>
   * Get the issue alarm list.
   * </p>
   *
   * @param issueNo The monitor issue request view object.
   * @return The remote request response dto.
   */
  @GetMapping("/alarm/{issueNo}")
  public HttpResult listAlarmByIssueNo(@PathVariable(value = "issueNo") String issueNo) {
    return HttpResult.success(monitorIssueService.listAlarmByIssueNo(issueNo, true));
  }

  /**
   * <p>
   * Get the issue list.
   * </p>
   *
   * @param issueRecordListRequestVO The issue request view object.
   * @return The remote request response dto.
   */
  @PostMapping("/issue-list")
  public HttpResult listIssueAndAlarm(@RequestBody IssueRecordListRequestVO issueRecordListRequestVO) {
    ParameterCheckUtility.checkNotNull(issueRecordListRequestVO, "issueRecordListRequestVO");
    return monitorIssueService.listIssueRecord(issueRecordListRequestVO);
  }
}
