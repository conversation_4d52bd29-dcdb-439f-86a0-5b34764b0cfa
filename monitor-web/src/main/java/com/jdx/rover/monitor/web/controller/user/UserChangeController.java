package com.jdx.rover.monitor.web.controller.user;

import com.jdx.rover.monitor.dto.user.GetCockpitStatusDTO;
import com.jdx.rover.monitor.service.user.UserChangeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 用户页面切换Controller
 * @author: wangguotai
 * @create: 2024-06-12 17:14
 **/
@RequiredArgsConstructor
@RequestMapping(value = "/monitor/web/user")
@RestController
public class UserChangeController {

    private final UserChangeService userChangeService;

    /**
     * 1、获取用户座席状态
     *
     * @return GetCockpitStatusDTO
     */
    @GetMapping(value = "/getCockpitStatus")
    public GetCockpitStatusDTO getCockpitStatus() {
        return userChangeService.getCockpitStatus();
    }
}