/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller.report;

import com.jdx.rover.monitor.service.report.ReportBootService;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 启动信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/monitor/web/report/boot")
public class ReportBootController {
    private final ReportBootService reportBootService;

    /**
     * 获取启动信息
     */
    @GetMapping(value = "/{vehicleName}")
    public VehicleBootDTO getReportBoot(@PathVariable(value = "vehicleName") String vehicleName) {
        return reportBootService.get(vehicleName);
    }
}
