/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.robot;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.service.robot.RobotIssueService;
import com.jdx.rover.monitor.vo.robot.RobotIssueAdoptVO;
import com.jdx.rover.monitor.web.jsf.api.robot.MonitorRobotIssueJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 监控机器人工单服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
public class MonitorRobotIssueWebJsfServiceImpl extends AbstractProvider<MonitorRobotIssueJsfService> implements MonitorRobotIssueJsfService {

    private final RobotIssueService robotIssueService;

    @ServiceInfo(name = "处理机器人告警", webUrl = "/monitor/web/robot/issue/adopt")
    @Override
    public HttpResult<String> adoptIssue(RobotIssueAdoptVO robotIssueAdoptVo) {
        return robotIssueService.adoptIssue(robotIssueAdoptVo);
    }
}
