/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.pda;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.MonitorChangeVideoModeVO;
import com.jdx.rover.monitor.api.web.jsf.service.MonitorVideoJsfService;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 监控视频JSF接口
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/12
 */
@Service
@RequiredArgsConstructor
public class MonitorVideoJsfServiceImpl extends AbstractProvider<MonitorVideoJsfService> implements MonitorVideoJsfService{

    /**
     * 提供视频相关服务的实例。
     */
    private final MonitorVideoService videoService;

    /**
     * 更改视频模式
     * @return 更改结果
     */
    @Override
    public HttpResult<Void> changeVideoMode(MonitorChangeVideoModeVO changeVideoModeVo) {
        return videoService.changeVideoMode(changeVideoModeVo);
    }
}