package com.jdx.rover.monitor.web.controller.common;

import com.jdx.rover.monitor.dto.mobile.common.FileDTO;
import com.jdx.rover.monitor.service.mobile.CommonService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping(value = "/monitor/web/common")
@RestController
@RequiredArgsConstructor
public class MonitorCommonController {

    private final CommonService commonService;

    /**
     * 文件上传
     *
     * @param file MultipartFile
     * @return FileDTO
     */
    @PostMapping(value = "/fileUpload")
    public FileDTO fileUpload(@RequestPart("file") MultipartFile file) {
        return commonService.fileUpload(file);
    }
}
