package com.jdx.rover.monitor.web.provider;

import cn.hutool.core.bean.BeanUtil;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleRealtimeDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleTakeOverDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.jsf.service.MonitorShadowVehicleRealtimeInfoJsfService;
import com.jdx.rover.monitor.service.web.MonitorBasicInfoService;
import com.jdx.rover.monitor.service.web.MonitorVehicleRealtimeInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is api interface for vehicle realtime info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorShadowVehicleRealtimeInfoServiceImpl extends AbstractProvider<MonitorShadowVehicleRealtimeInfoJsfService> implements MonitorShadowVehicleRealtimeInfoJsfService {

    /**
     * 提供车辆实时信息服务的接口。
     */
    private final MonitorVehicleRealtimeInfoService vehicleRealtimeInfoService;

    /**
     * 提供车辆基本信息服务的接口。
     */
    private final MonitorBasicInfoService monitorBasicInfoService;

    /**
     * 获取指定车辆的实时信息。
     * @param vehicleName 车辆名称。
     * @return 实时信息的 HttpResult 对象。
     */
    @Override
    public HttpResult<MonitorShadowVehicleRealtimeDTO> getVehicleRealtimeInfo(String vehicleName) {
        return vehicleRealtimeInfoService.getVehicleRealtimeInfo(vehicleName);
    }

    /**
     * 获取车辆基本信息。
     * @param vehicleName 车辆名称。
     * @return 车辆基本信息DTO。
     */
    @Override
    public HttpResult<MonitorShadowVehicleBasicInfoDTO> getVehicleBasicInfo(String vehicleName) {
        MonitorVehiceBasicInfoDTO vehiceBasicInfoDto = monitorBasicInfoService.getVehicleBasicInfo(vehicleName);
        MonitorShadowVehicleBasicInfoDTO shadowVehicleBasicInfoDto = new MonitorShadowVehicleBasicInfoDTO();
        BeanUtil.copyProperties(vehiceBasicInfoDto, shadowVehicleBasicInfoDto);
        return HttpResult.success(shadowVehicleBasicInfoDto);
    }

    /**
     * 获取指定车辆的接管信息。
     * @param vehicleName 车辆名称。
     * @return 接管信息DTO。
     */
    @Override
    public HttpResult<MonitorShadowVehicleTakeOverDTO> getVehicleTakeOverInfo(String vehicleName) {
        MonitorShadowVehicleTakeOverDTO vehicleTakeOverInfoDto = vehicleRealtimeInfoService.getVehicleTakeOverInfo(vehicleName);
        return HttpResult.success(vehicleTakeOverInfoDto);
    }

    /**
     * 获取指定车辆的实时报警信息。
     * @param vehicleName 车辆名称。
     * @return 包含车辆实时报警信息的 HttpResult 对象。
     */
    @Override
    public HttpResult<MonitorShadowVehicleAlarmDTO> getVehicleRealtimeAlarmInfo(String vehicleName) {
        MonitorShadowVehicleAlarmDTO vehicleAlarmInfoDto = vehicleRealtimeInfoService.getVehicleRealtimeAlarmInfo(vehicleName);
        return HttpResult.success(vehicleAlarmInfoDto);
    }
}
