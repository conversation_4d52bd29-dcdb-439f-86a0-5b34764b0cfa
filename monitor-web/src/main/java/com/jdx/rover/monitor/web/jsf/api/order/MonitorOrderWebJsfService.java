/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.order;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorOrderDTO;
import com.jdx.rover.monitor.vo.MonitorOrderRequestVO;
import com.jdx.rover.monitor.vo.MonitorVerifyCodeRequestVO;
import com.jdx.rover.monitor.vo.MonitorViewOrderRequestVO;

/**
 * <p>
 * This is a controller class for supervisor order.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorOrderWebJsfService {

  public HttpResult<MonitorOrderDTO> search(MonitorOrderRequestVO monitorOrderRequestVo);

  public HttpResult<Void> sendVerifyCode(MonitorVerifyCodeRequestVO verifyCodeVo);

  public HttpResult<Void> viewOrderDetail(MonitorViewOrderRequestVO viewOrderVo);

}
