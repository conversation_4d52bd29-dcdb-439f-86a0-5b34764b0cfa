/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.s3;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.s3.S3PreSignDTO;
import com.jdx.rover.monitor.vo.s3.S3PreSignVO;

/**
 * oss对象存储操作
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface S3PresignUrlWebJsfService {
    /**
     * 预签名get url,用于下载
     *
     * @param s3PreSignVO
     * @return
     */
    public HttpResult<String> preSignGetUrl(S3PreSignVO s3PreSignVO);

    /**
     * 预签名put url,用于上传
     *
     * @param s3PreSignVO
     * @return
     */
    public HttpResult<String> preSignPutUrl(S3PreSignVO s3PreSignVO);

    /**
     * 预签名get和put url,用于下载上传
     *
     * @param s3PreSignVO
     * @return
     */
    public HttpResult<S3PreSignDTO> preSignGetAndPutUrl(S3PreSignVO s3PreSignVO);
}
