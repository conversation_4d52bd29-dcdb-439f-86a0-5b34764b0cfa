/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.web.MiniMonitorStationService;
import com.jdx.rover.monitor.vo.MiniMonitorUserInfoRequestVO;
import com.jdx.rover.monitor.vo.station.StationVehicleSearchVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for mini monitor station info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/station")
public class MiniMonitorStationController {

  @Autowired
  private MiniMonitorStationService miniMonitorStationService;

  /**
   * <p>
   * This method helps to return station list.
   * </p>
   *
   * @param userInfo the mini monitor request VO.
   * @return mini monitor station DTO list.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @GetMapping("/user_station")
  public HttpResult getUserStationList(MiniMonitorUserInfoRequestVO userInfo) {
    ParameterCheckUtility.checkNotNull(userInfo, "miniMonitorUserInfoRequestVO");
    return miniMonitorStationService.getUserStationList(userInfo);
  }

  /**
   * <p>
   * This method helps to return station detail.
   * </p>
   *
   * @return mini monitor station DTO.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @GetMapping("/station_detail/{id}")
  public HttpResult getStationInfoById(@PathVariable(value = "id") Integer id) {
    ParameterCheckUtility.checkNotNull(id, "id");
    ParameterCheckUtility.checkNotNegative(id, "id");
    return miniMonitorStationService.getStationDetailById(id);
  }

  /**
   * <p>
   * This method helps to return stop list.
   * </p>
   *
   * @param stationId the mini monitor request station id.
   * @return mini monitor stop DTO list.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @GetMapping("/station_stop/{stationId}")
  public HttpResult getStationStopList(@PathVariable(value = "stationId") Integer stationId) {
    ParameterCheckUtility.checkNotNull(stationId, "stationId");
    ParameterCheckUtility.checkNotNegative(stationId, "stationId");
    return miniMonitorStationService.getStationStopList(stationId);
  }

  /**
   * 根据输入项模糊查找站点车辆列表
   */
  @PostMapping("/station_vehicle_list")
  public HttpResult<?> getStationVehicleList(@Valid @RequestBody StationVehicleSearchVO stationVehicleSearchVO) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    return miniMonitorStationService.getStationVehicleList(stationVehicleSearchVO, userName);
  }
}

