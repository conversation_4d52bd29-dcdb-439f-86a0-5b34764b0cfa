package com.jdx.rover.monitor.web.jsf.provider.data;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.data.GetCockpitDataPageDTO;
import com.jdx.rover.monitor.dto.data.GetCockpitTeamDataListDTO;
import com.jdx.rover.monitor.dto.data.GetSupportDataListDTO;
import com.jdx.rover.monitor.service.data.DataService;
import com.jdx.rover.monitor.vo.data.GetCockpitDataPageVO;
import com.jdx.rover.monitor.vo.data.GetCockpitTeamDataListVO;
import com.jdx.rover.monitor.vo.data.GetSupportDataListVO;
import com.jdx.rover.monitor.web.jsf.api.data.MonitorDataWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

/**
 * 驾舱数据Controller
 */
@Service
@RequiredArgsConstructor
public class MonitorDataWebJsfServiceImpl extends AbstractProvider<MonitorDataWebJsfService> implements MonitorDataWebJsfService {

    private final DataService dataService;

    @ServiceInfo(name = "获取坐席数据列表", webUrl = "/monitor/web/data/get_cockpit_data_list")
    public HttpResult<PageDTO<GetCockpitDataPageDTO>> getCockpitDataPage(@Valid GetCockpitDataPageVO getCockpitDataPageVO) {
        return HttpResult.success(dataService.getCockpitDataPage(getCockpitDataPageVO));
    }

    @ServiceInfo(name = "获取远驾团队数据列表", webUrl = "/monitor/web/data/get_cockpit_team_data_list")
    public HttpResult<PageDTO<GetCockpitTeamDataListDTO>> getCockpitTeamDataList(GetCockpitTeamDataListVO getCockpitTeamDataListVo) {
        return HttpResult.success(dataService.getCockpitTeamDataList(getCockpitTeamDataListVo));
    }

    @ServiceInfo(name = "获取技术支持数据列表", webUrl = "/monitor/web/data/get_support_data_list")
    public HttpResult<PageDTO<GetSupportDataListDTO>> getSupportDataList(@Valid GetSupportDataListVO getSupportDataListVO) {
        return HttpResult.success(dataService.getSupportDataList(getSupportDataListVO));
    }
}