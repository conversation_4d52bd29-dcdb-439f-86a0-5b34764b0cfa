/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.web.jsf.api.accident;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.accident.AccidentBasicInfoDTO;
import com.jdx.rover.monitor.dto.accident.AccidentTagDTO;
import com.jdx.rover.monitor.dto.accident.GetAccidentPageListDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentDetailDTO;
import com.jdx.rover.monitor.vo.accident.GetAccidentDetailVO;
import com.jdx.rover.monitor.vo.accident.GetAccidentPageListVO;
import com.jdx.rover.monitor.vo.accident.ManualCreateAccidentVO;
import com.jdx.rover.monitor.vo.accident.SafetyGroupEditAccidentVO;
import com.jdx.rover.monitor.vo.accident.TechnicalSupportEditAccidentVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 监控事故接口
 *
 * <AUTHOR>
 * @date 2025/2/20
 */
public interface MonitorAccidentWebJsfService {

    /**
     * 分页获取事故列表
     * @param getAccidentPageListVO getAccidentPageListVO
     * @return PageDTO<GetAccidentPageListDTO>
     */
    public HttpResult<PageDTO<GetAccidentPageListDTO>> getAccidentPageList(@Valid GetAccidentPageListVO getAccidentPageListVO);

    /**
     * 获取事故基础信息
     * @param getAccidentDetailVo getAccidentDetailVo
     * @return AccidentBasicInfoDTO
     */
    public HttpResult<AccidentBasicInfoDTO> getAccidentBasicInfo(@Valid GetAccidentDetailVO getAccidentDetailVo);

    /**
     * 获取事故详情
     * @param getAccidentDetailVo getAccidentDetailVo
     * @return MonitorAccidentDetailDTO
     */
    public HttpResult<MonitorAccidentDetailDTO> getAccidentDetail(@Valid GetAccidentDetailVO getAccidentDetailVo);

    /**
     * 技术支持编辑事故
     * @param technicalSupportEditAccidentVO technicalSupportEditAccidentVO
     * @return MonitorErrorEnum
     */
    public HttpResult<String> technicalSupportEditAccident(@Valid TechnicalSupportEditAccidentVO technicalSupportEditAccidentVO);

    /**
     * 安全组编辑事故
     * @param safetyGroupEditAccidentVO safetyGroupEditAccidentVO
     * @return MonitorErrorEnum
     */
    public HttpResult<String> safetyGroupEditAccident(@Valid SafetyGroupEditAccidentVO safetyGroupEditAccidentVO);

    /**
     * 手动创建事故
     * @param manualCreateAccidentVO manualCreateAccidentVO
     * @return MonitorErrorEnum
     */
    public HttpResult<String> manualCreateAccident(@Valid ManualCreateAccidentVO manualCreateAccidentVO);

    /**
     * 获取事故标签&模块
     * @return
     */
    public HttpResult<List<AccidentTagDTO>> getTagList();

}
