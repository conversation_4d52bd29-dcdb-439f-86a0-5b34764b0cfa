package com.jdx.rover.monitor.web.jsf.accident;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentAnalysisDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentBoardDetailDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.MonthlyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.RecentAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.WeeklyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.AccidentBoardDetailVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.MonthlyAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.RecentAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.WeeklyAccidentTrendsVO;
import com.jdx.rover.monitor.api.web.jsf.service.MonitorAccidentBoardJsfService;
import com.jdx.rover.monitor.service.web.MonitorAccidentBoardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事故看板jsf接口
 */
@Service
@RequiredArgsConstructor
public class MonitorAccidentBoardJsfServiceImpl extends AbstractProvider<MonitorAccidentBoardJsfService> implements MonitorAccidentBoardJsfService{

    /**
     * 事故看板Service
     */
    public final MonitorAccidentBoardService monitorAccidentBoardService;

    /**
     * 获取事故分析
     * @return
     */
    @Override
    public HttpResult<AccidentAnalysisDTO> getAccidentAnalysis() {
        return HttpResult.success(monitorAccidentBoardService.getAccidentAnalysis());
    }

    /**
     * 最近一段时间事故趋势
     * @param recentAccidentTrendsVO
     * @return
     */
    @Override
    public HttpResult<List<RecentAccidentTrendsDTO>> recentAccidentTrends(RecentAccidentTrendsVO recentAccidentTrendsVO) {
        return HttpResult.success(monitorAccidentBoardService.recentAccidentTrends(recentAccidentTrendsVO));
    }

    /**
     * 周度事故统计趋势图
     * @param weeklyAccidentTrendsVO
     * @return
     */
    @Override
    public HttpResult<List<WeeklyAccidentTrendsDTO>> weeklyAccidentTrends(WeeklyAccidentTrendsVO weeklyAccidentTrendsVO) {
        return HttpResult.success(monitorAccidentBoardService.weeklyAccidentTrends(weeklyAccidentTrendsVO));
    }

    /**
     * 月度事故趋势图
     * @param monthlyAccidentTrendsVO
     * @return
     */
    @Override
    public HttpResult<List<MonthlyAccidentTrendsDTO>> monthlyAccidentTrends(MonthlyAccidentTrendsVO monthlyAccidentTrendsVO) {
        return HttpResult.success(monitorAccidentBoardService.monthlyAccidentTrends(monthlyAccidentTrendsVO));
    }

    /**
     * 获取事故明细
     * @param accidentBoardDetailVO
     * @return
     */
    @Override
    public HttpResult<List<AccidentBoardDetailDTO>> getAccidentDetail(AccidentBoardDetailVO accidentBoardDetailVO) {
        return HttpResult.success(monitorAccidentBoardService.getAccidentDetail(accidentBoardDetailVO));
    }
}
