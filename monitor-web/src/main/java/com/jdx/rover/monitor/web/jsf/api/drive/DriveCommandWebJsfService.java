/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.web.jsf.api.drive;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;

/**
 * 发送指令服务
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface DriveCommandWebJsfService {
    /**
     * 发送进入接管命令
     */
    HttpResult<?> sendEnterTakeOver(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送退出接管命令
     */
    HttpResult<?> sendExitTakeOver(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送视同到达命令
     */
    HttpResult<?> sendAsArrived(DriveRemoteCommandVO driveRemoteCommandVO);


    /**
     * 发送解除按钮停车命令
     */
    HttpResult<?> sendRelieveButtonStop(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送软件重启命令
     */
    HttpResult<?> sendRemoteRestart(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送断电重启命令
     */
    HttpResult<?> sendPowerReboot(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送远程下电命令
     */
    HttpResult<?> sendPowerOff(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 发送命令
     */
    HttpResult<?> sendCommand(DriveRemoteCommandVO driveRemoteCommandVO);


    /**
     * 切换无图模式
     */
    HttpResult<?> runNoMap(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 切换有图模式
     */
    HttpResult<?> runHaveMap(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 控制油门
     */
    HttpResult<?> sendTorqueControl(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 控制速度
     */
    HttpResult<?> sendVelocityControl(DriveRemoteCommandVO driveRemoteCommandVO);

    /**
     * 按钮命令
     */
    HttpResult<?> buttonCommand(DriveRemoteCommandVO driveRemoteCommandVO);
}
