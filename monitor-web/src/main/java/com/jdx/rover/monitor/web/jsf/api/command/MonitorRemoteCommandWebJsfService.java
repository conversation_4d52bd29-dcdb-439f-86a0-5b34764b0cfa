/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleTakeOverDTO;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.vo.MonitorPostTrafficLightCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MonitorSetSpeedLimitVO;
import com.jdx.rover.monitor.vo.MonitorVehiclePowManageVO;
import com.jdx.rover.monitor.vo.MonitorVehiclePowerOnVO;
import com.jdx.rover.monitor.vo.NoSignalIntersectionCommandVO;
import com.jdx.rover.monitor.vo.UserNameBasicVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 远程指令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorRemoteCommandWebJsfService {
  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishEmergencyStopCommand(MonitorRemoteCommandVO emergencyStopCommandVO);

  /**
   * <p>
   * 急刹
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishEmergencyBrakeCommand(MonitorRemoteCommandVO emergencyBrakeCommandVO);

  /**
   * <p>
   * Post as arrived command.
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishAsArrivedCommand(MonitorRemoteCommandVO monitorAsArrivedCommandVo);

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishRecoveryCommand(MonitorRemoteCommandVO monitorRecoveryCommandVo);

  /**
   * <p>
   * Post restart command.
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishRestartCommand(MonitorRemoteCommandVO monitorRestartCommandVo);

  /**
   * <p>
   * Publish control traffic light command.
   * </p>
   */
  public HttpResult<MonitorRemoteCommandDTO> publishControlTrafficLightCommand(MonitorPostTrafficLightCommandVO monitorPostTrafficLightCommandVo);

  /**
   * 远程电源管理
   */
  public HttpResult<MonitorRemoteCommandDTO> powerStop(MonitorVehiclePowManageVO vehiclePowManageVo);
  /**
   * 远程电源管理
   */
  public HttpResult<MonitorRemoteCommandDTO> publishRelieveButtonStopCommand(MonitorRemoteCommandVO monitorRecoveryCommandVo);

  /**
   * 通过无信号路口
   */
  public HttpResult<MonitorRemoteCommandDTO> passNoSignalIntersection(@Valid NoSignalIntersectionCommandVO noSignalIntersectionCommandVO);

  /**
   * 临时停车
   */
  public HttpResult<MonitorRemoteCommandDTO> publishTemporaryStopCommand(@Valid MonitorRemoteCommandVO temporaryStopVo);

  /**
   * <p>
   *  获取用户接管的车辆列表
   * </p>
   */
  public HttpResult<List<VehicleTakeOverDTO>> getTakeOverVehicle(UserNameBasicVO userNameBasicVo);

  /**
   * 远驾远程开机
   */
  HttpResult<Void> powerOn(@Valid MonitorVehiclePowerOnVO monitorVehiclePowerOnVO);

    /**
     *  下发车辆最大限速
     */
    HttpResult<Void> postMaxVelocityRequest(@Valid MonitorSetSpeedLimitVO monitorSetSpeedLimitVO);
}
