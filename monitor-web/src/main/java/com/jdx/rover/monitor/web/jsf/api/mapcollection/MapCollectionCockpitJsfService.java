/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.web.jsf.api.mapcollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.mapcollection.FuzzySearchVehicleDTO;
import com.jdx.rover.monitor.dto.mapcollection.GetRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapVehiclePageDTO;
import com.jdx.rover.monitor.dto.mapcollection.MonitorTaskSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MultiVideoUrlDTO;
import com.jdx.rover.monitor.dto.mapcollection.VehicleSolidifiedTaskDTO;
import com.jdx.rover.monitor.vo.mapcollection.FinishCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.AssociateTaskVO;
import com.jdx.rover.monitor.vo.mapcollection.FuzzySearchVehicleVO;
import com.jdx.rover.monitor.vo.mapcollection.GetRouteVO;
import com.jdx.rover.monitor.vo.mapcollection.MapVehiclePageVO;
import com.jdx.rover.monitor.vo.mapcollection.MonitorTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.MultiVideoUrlVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskOperateVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchModeVO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 采图座席相关接口
 */
public interface MapCollectionCockpitJsfService {

    /**
     * 多车页获取采图车辆列表
     *
     * @param mapVehiclePageVO mapVehiclePageVO
     * @return 车辆分页列表
     */
    HttpResult<PageDTO<MapVehiclePageDTO>> getVehiclePage(MapVehiclePageVO mapVehiclePageVO);

    /**
     * 模糊搜索车牌号列表
     *
     * @param fuzzySearchVehicleVO fuzzySearchVehicleVO
     * @return 车辆列表
     */
    HttpResult<List<FuzzySearchVehicleDTO>> fuzzySearchVehicle(FuzzySearchVehicleVO fuzzySearchVehicleVO);

    /**
     * 认领/取消任务
     *
     * @param associateTaskVO associateTaskVO
     * @return 操作结果
     */
    HttpResult<Void> associateTask(@NotNull(message = "请求参数不能为空") @Valid AssociateTaskVO associateTaskVO);

    /**
     * 获取采图路线
     *
     * @param getRouteVO getRouteVO
     * @return GetRouteDTO
     */
    HttpResult<GetRouteDTO> getRoute(@NotNull(message = "请求参数不能为空") @Valid GetRouteVO getRouteVO);

    /**
     * 开启/关闭 地图采集模式
     *
     * @param switchModeVO switchModeVO
     * @return 操作结果
     */
    HttpResult<Void> switchMode(@NotNull(message = "请求参数不能为空") @Valid SwitchModeVO switchModeVO);

    /**
     * 开始/暂停采集
     *
     * @param switchCollectionVO switchCollectionVO
     * @return 操作结果
     */
    HttpResult<Void> switchCollection(@NotNull(message = "请求参数不能为空") @Valid SwitchCollectionVO switchCollectionVO);

    /**
     * 完成采集
     *
     * @param finishCollectionVO finishCollectionVO
     * @return 操作结果
     */
    HttpResult<Void> finishCollection(@NotNull(message = "请求参数不能为空") @Valid FinishCollectionVO finishCollectionVO);

    /**
     * 获取采集任务列表
     *
     * @param monitorTaskSearchVO monitorTaskSearchVO
     * @return PageDTO<MonitorTaskSearchDTO>
     */
    HttpResult<PageDTO<MonitorTaskSearchDTO>> getExplorationTaskList(@NotNull(message = "请求参数不能为空") MonitorTaskSearchVO monitorTaskSearchVO);

    /**
     * 获取任务关联线路（GCJ02）
     *
     * @param taskBaseVO taskBaseVO
     * @return 操作结果
     */
    HttpResult<TaskRouteDTO> getExplorationTaskDetail(@Valid TaskBaseVO taskBaseVO);

    /**
     * 获取标记列表（GCJ02）
     *
     * @param positionVO positionVO
     * @return 操作结果
     */
    HttpResult<MarkSearchDTO> getNearbyMarks(@Valid PositionVO positionVO);

    /**
     * 获取视频地址
     *
     * @param multiVideoUrlVO multiVideoUrlVO
     * @return 多车视频链接
     */
    HttpResult<List<MultiVideoUrlDTO>> getVideoUrlList(@Valid MultiVideoUrlVO multiVideoUrlVO);

    /**
     * 查看车辆固化数据
     *
     * @param solidifiedTaskSearchVO solidifiedTaskSearchVO
     * @return 车辆固化数据列表
     */
    HttpResult<List<VehicleSolidifiedTaskDTO>> getSolidifiedTaskList(@Valid @NotNull(message = "请求参数不能为空") SolidifiedTaskSearchVO solidifiedTaskSearchVO);

    /**
     * 取消固化数据
     *
     * @param solidifiedTaskOperateVO solidifiedTaskOperateVO
     */
    HttpResult<Void> cancelSolidifiedTask(@Valid @NotNull(message = "请求参数不能为空") SolidifiedTaskOperateVO solidifiedTaskOperateVO);

    /**
     * 获取任务已采集线路，WGS84
     *
     * @param getRouteVO getRouteVO
     * @return 车辆已采集线路点位
     */
    HttpResult<GetRouteDTO> getFinishedRoute(@Valid @NotNull(message = "请求参数不能为空") GetRouteVO getRouteVO);

    /**
     * 获取全量站点
     *
     * @return List<MonitorBasicDataInfoUnderUseDTO>
     */
    HttpResult<List<MonitorBasicDataInfoUnderUseDTO>> getUserStation();
}