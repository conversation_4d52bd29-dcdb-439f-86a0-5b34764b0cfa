/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorImpassableAreaDTO;
import com.jdx.rover.monitor.search.MonitorImpassableAreaSearch;
import com.jdx.rover.monitor.service.web.MonitorImpassableAreaService;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaAddVO;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaUpdateVO;
import com.jdx.rover.monitor.vo.MonitorImpassableAreaVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a api interface for monitor impassable area.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/tnta")
@RestController
public class MonitorImpassableAreaController {

  @Autowired
  private MonitorImpassableAreaService monitorImpassableAreaService;

  /**
   * <p>
   * Search supervisor jira record by assist jira record search entity.
   * </p>
   * 
   * @param vehicleName             The view object for page
   */
  @GetMapping("/position/{vehicleName}")
  public HttpResult getVehiclePosition(@PathVariable(value = "vehicleName") String vehicleName){
    return monitorImpassableAreaService.getVehiclePosition(vehicleName);
  }

  /**
   * <p>
   * Search supervisor jira record by assist jira record search entity.
   * </p>
   * 
   * @param impassableAreaSearch The impassable area search entity.
   */
  @PostMapping("/list")
  public HttpResult<PageDTO<MonitorImpassableAreaDTO>> search(@Valid @RequestBody MonitorImpassableAreaSearch impassableAreaSearch) {
    return monitorImpassableAreaService.search(impassableAreaSearch,impassableAreaSearch);
  }

  /**
   * <p>
   * Insert a impassable area.
   * </p>
   * 
   * @param monitorImpassableAreaAddVo The impassable area object need to be inserted.
   */
  @PostMapping("/add")
  public HttpResult<MonitorImpassableAreaDTO> add(@Valid @RequestBody MonitorImpassableAreaAddVO monitorImpassableAreaAddVo) {
    return monitorImpassableAreaService.add(monitorImpassableAreaAddVo);
  }

  /**
   * <p>
   * Delete impassable area.
   * </p>
   * 
   * @param monitorImpassableAreaVo The impassable area object need to be delete.
   */
  @PostMapping("/delete")
  public HttpResult<Void> delete(@Valid @RequestBody MonitorImpassableAreaVO monitorImpassableAreaVo) {
    monitorImpassableAreaVo.setUserName(LoginUtils.getUsername());
    return monitorImpassableAreaService.delete(monitorImpassableAreaVo);
  }

  /**
   * <p>
   * Update impassable area object.
   * </p>
   *
   * @param impassableAreaUpdateVo the update entity of impassable area.
   * @return The impassable area data transform object.
   */
  @PostMapping("/update")
  public HttpResult update(@Valid @RequestBody MonitorImpassableAreaUpdateVO impassableAreaUpdateVo) {
    impassableAreaUpdateVo.setUserName(LoginUtils.getUsername());
    return monitorImpassableAreaService.update(impassableAreaUpdateVo);
  }

}
