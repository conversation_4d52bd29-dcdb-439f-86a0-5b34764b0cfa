/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.realtime;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.service.web.MonitorVehicleRealtimeInfoService;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.realtime.MonitorVehicleRealtimeWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 车辆基础信息jsf接口实现
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorVehicleRealtimeWebJsfServiceImpl extends AbstractProvider<MonitorVehicleRealtimeWebJsfService> implements MonitorVehicleRealtimeWebJsfService {

  private final MonitorVehicleRealtimeInfoService vehicleRealtimeInfoService;


  /**
   * 获取多辆车的基本信息和地图位置。
   * @param vehicleNameVo 包含车辆名称的VO对象。
   * @return 返回一个HttpResult对象，包含多辆车的基本信息和地图位置。
   */
  @Override
  public HttpResult getMultiVehicleMapInfo(VehicleNameBasicVO vehicleNameVo) {
    return vehicleRealtimeInfoService.getMultiVehicleRealtimeInfo(vehicleNameVo.getVehicleName());
  }

  /**
   * 获取车辆的行驶里程信息。
   * @param vehicleNameVo 车辆基本信息对象，包含车辆名称等信息。
   * @return HttpResult 对象，封装了车辆行驶里程的相关信息。
   */
  @Override
  public HttpResult<Double> getVehicleScheduleMileage(VehicleNameBasicVO vehicleNameVo) {
    return vehicleRealtimeInfoService.getVehicleScheduleMileage(vehicleNameVo.getVehicleName());
  }
}