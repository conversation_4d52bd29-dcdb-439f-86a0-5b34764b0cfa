/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.MonitorXataChangeModeCommandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * xata接口服务
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/monitor/web/xata")
public class MonitorXataController {

  @Autowired
  private MonitorRemoteCommandService remoteCommandService;

  /**
   * <p>
   * 切换车辆模式
   * </p>
   */
  @PostMapping("/changeVehicleMode")
  public HttpResult<MonitorRemoteCommandDTO> changeVehicleMode(@RequestBody MonitorXataChangeModeCommandVO xataChangeModeCommandVo) {
    return remoteCommandService.postChangeVehicleMode(xataChangeModeCommandVo);
  }

}
