/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.issue;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.issue.IssueAlarmDTO;
import com.jdx.rover.monitor.dto.issue.IssueDetailDTO;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.vo.IssueDetailRequestVO;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.IssueVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor issue.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorIssueWebJsfService {

  /**
   * <p>
   * Get the issue detail of the issue no.
   * </p>
   *
   * @return The corresponding issue detail data transform object.
   */
  public HttpResult<IssueDetailDTO> getIssueDetail(IssueDetailRequestVO issueDetailRequestVo);

  /**
   * <p>
   * Get the issue detail of the issue no.
   * </p>
   *
   * @return The corresponding issue detail data transform object.
   */
  public HttpResult<IssueDetailDTO> getAvailibleIssue(VehicleNameBasicVO vehicleNameBasicVo);

  /**
   * <p>
   * monitor issue add.
   * </p>
   *
   * @param monitorIssueAddVo The monitor issue request view object.
   */
  public HttpResult<String> add(IssueVO monitorIssueAddVo);

  /**
   * <p>
   * monitor issue update.
   * </p>
   *
   * @param issueVo The monitor issue request view object.
   * @return The remote request response dto.
   */
  public HttpResult<String> update(IssueVO issueVo);

  /**
   * <p>
   * Search issueDetail by search entity.
   * </p>
   *
   * @param issueRecordSearch The search entity for MonitorIssueDetailDto.
   * @return The correspond MonitorIssueDetailDto data transform objects.
   * @throws IllegalArgumentException If the argument doesn't meet the requirement.
   */
  public HttpResult<PageDTO<IssueRecordDTO>> search(@Valid IssueRecordSearch issueRecordSearch);

  /**
   * <p>
   * Get the issue alarm list.
   * </p>
   *
   * @return The remote request response dto.
   */
  public HttpResult<List<IssueAlarmDTO>> listAlarmByIssueNo(IssueDetailRequestVO issueDetailRequestVo);

  /**
   * <p>
   * Get the issue list.
   * </p>
   *
   * @param issueRecordListRequestVO The issue request view object.
   * @return The remote request response dto.
   */
  public HttpResult<List<IssueDetailDTO>> listIssueAndAlarm(IssueRecordListRequestVO issueRecordListRequestVO);
}
