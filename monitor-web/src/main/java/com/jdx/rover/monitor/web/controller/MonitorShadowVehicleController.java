/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import cn.hutool.core.bean.BeanUtil;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleAlarmDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.dto.MonitorShadowVehicleTakeOverDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.service.web.MonitorBasicInfoService;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.service.web.MonitorVehicleRealtimeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor data info under user.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/shadow")
@RestController
public class MonitorShadowVehicleController {

  @Autowired
  private MonitorVehicleRealtimeInfoService vehicleRealtimeInfoService;
  @Autowired
  private MonitorBasicInfoService monitorBasicInfoService;
  @Autowired
  private MonitorMapInfoService mapInfoService;

  /**
   * <p>
   * Search vehicle realtime info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/vehicle/realtime/{vehicleName}")
  public HttpResult getVehicleRealtimeInfo(@PathVariable("vehicleName") String vehicleName) {
    return vehicleRealtimeInfoService.getVehicleRealtimeInfo(vehicleName);
  }

  /**
   * <p>
   * Search vehicle basic info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/vehicle/basic/{vehicleName}")
  public HttpResult getVehicleBasicInfo(@PathVariable("vehicleName") String vehicleName) {
    MonitorVehiceBasicInfoDTO vehiceBasicInfoDto = monitorBasicInfoService.getVehicleBasicInfo(vehicleName);
    MonitorShadowVehicleBasicInfoDTO shadowVehicleBasicInfoDto = new MonitorShadowVehicleBasicInfoDTO();
    BeanUtil.copyProperties(vehiceBasicInfoDto,shadowVehicleBasicInfoDto);
    return HttpResult.success(shadowVehicleBasicInfoDto);
  }

  /**
   * <p>
   * Search vehicle basic info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/vehicle/take/{vehicleName}")
  public HttpResult getVehicleTakeOverInfo(@PathVariable("vehicleName") String vehicleName) {
    MonitorShadowVehicleTakeOverDTO vehicleTakeOverInfoDto = vehicleRealtimeInfoService.getVehicleTakeOverInfo(vehicleName);
    return HttpResult.success(vehicleTakeOverInfoDto);
  }

  /**
   * <p>
   * Search vehicle alarm info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/vehicle/alarm/{vehicleName}")
  public HttpResult getVehicleRealtimeAlarmInfo(@PathVariable("vehicleName") String vehicleName) {
    MonitorShadowVehicleAlarmDTO vehicleAlarmInfoDto = vehicleRealtimeInfoService.getVehicleRealtimeAlarmInfo(vehicleName);
    return HttpResult.success(vehicleAlarmInfoDto);
  }

  /**
   * <p>
   * Search vehicle schedule pnc route by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/vehicle/planning/route/{vehicleName}")
  public HttpResult getVehiclePncRoutingInfo(@PathVariable("vehicleName") String vehicleName) {
    List pncRoutingInfo = mapInfoService.getVehiclePncRoutingInfo(vehicleName);
    return HttpResult.success(pncRoutingInfo);
  }

}