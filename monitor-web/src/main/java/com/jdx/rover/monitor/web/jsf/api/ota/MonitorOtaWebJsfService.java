/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web.jsf.api.ota;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;

/**
 * 监控ota升级操作
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
public interface MonitorOtaWebJsfService {
    /**
     * 地图转为静默下载
     */
    public HttpResult<Void> mapSilentDownload(VehicleNameBasicVO vehicleNameBasicVo);
}
