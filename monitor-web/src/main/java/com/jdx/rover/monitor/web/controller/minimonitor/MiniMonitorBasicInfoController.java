/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * This is a controller class for mini monitor basic info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/basic")
public class MiniMonitorBasicInfoController {

  @Autowired
  private EnumService enumService;

  /**
   * <p>
   * This method helps to return enum list.
   * </p>
   *
   */
  @PostMapping("/enum_list")
  public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@Valid @RequestBody CommonDrownListVO commonDrownListVO) {
    Map<String, List<Map<String, Object>>> result = enumService.getEnumListMap(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }

}

