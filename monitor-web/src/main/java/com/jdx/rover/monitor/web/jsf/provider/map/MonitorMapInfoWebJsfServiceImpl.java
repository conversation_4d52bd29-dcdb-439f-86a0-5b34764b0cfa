/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.map;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorMetadataPositionDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleMapInfoDTO;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.vo.MonitorMapVehiclePositionRequestVO;
import com.jdx.rover.monitor.vo.MonitorStationVehicleMapInfoRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.map.MonitorMapInfoWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 车辆地图信息jsf接口实现
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorMapInfoWebJsfServiceImpl extends AbstractProvider<MonitorMapInfoWebJsfService> implements MonitorMapInfoWebJsfService {

  private final MonitorMapInfoService monitorMapInfoService;

  @Override
  @ServiceInfo(name = "获取车辆实时信息", webUrl = "/monitor/web/map/vehicle/realtime")
  public HttpResult getVehicleRealtimeInfo(VehicleNameBasicVO vehicleNameBasicVo) {
    return monitorMapInfoService.getVehicleMapRealtimeInfo(vehicleNameBasicVo.getVehicleName());
  }


  @Override
  @ServiceInfo(name = "获取监控站点和车辆地图信息", webUrl = "/monitor/web/map/vehicle/list")
  public HttpResult getStationAndVehicleMapInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    return monitorMapInfoService.getStationAndVehicleMapInfo(stationVehicleMapInfoRequestVo);
  }

  @Override
  @ServiceInfo(name = "获取车辆地图信息", webUrl = "/monitor/web/map/vehicle")
  public HttpResult getVehicleMapInfo(VehicleNameBasicVO vehicleNameBasicVo) {
    return HttpResult.success(monitorMapInfoService.getSingleVehicleRouting(vehicleNameBasicVo.getVehicleName()));
  }


  @Override
  @ServiceInfo(name = "获取车辆实时位置信息", webUrl = "/monitor/web/map/position/list")
  public HttpResult getVehicleRealtimePositionInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    Map<String, MonitorVehicleMapInfoDTO> result = monitorMapInfoService.getVehicleRealtimePositionInfo(stationVehicleMapInfoRequestVo);
    return HttpResult.success(result);
  }

  @Override
  @ServiceInfo(name = "获取监控地图车辆位置信息", webUrl = "/monitor/web/map/position")
  public HttpResult getMetaDataPositionInfo(MonitorMapVehiclePositionRequestVO vehiclePositionRequestVo) {
    MonitorMetadataPositionDTO metadataPositionDto = monitorMapInfoService.getMetaDataPositionInfo(vehiclePositionRequestVo.getMetaType(),
            vehiclePositionRequestVo.getKey(), vehiclePositionRequestVo.getPositionType());
    return HttpResult.success(metadataPositionDto);
  }
}