/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDetailDTO;
import com.jdx.rover.monitor.service.web.MonitorScheduleStopService;
import com.jdx.rover.monitor.vo.MonitorScheduleStopRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * This is a controller class for supervisor order.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value="/monitor/web/schedule/stop")
public class MonitorScheduleStopController {
  
  /**
   * <p>
   * The service for schedule stop.
   * </p>
   */
  @Autowired
  private MonitorScheduleStopService monitorScheduleStopService;

  /**
   * <p>
   * Search schedule stop realtime info by search entity.
   * </p>
   * 
   * @param monitorScheduleStopRequestVo The search entity for schedule stop.
   * @throws IllegalArgumentException If the argument doesn't meet the
   *                                  requirement.
   */
  @PostMapping("/detail")
  public HttpResult getRealtimeInfo(@RequestBody(required = true) MonitorScheduleStopRequestVO monitorScheduleStopRequestVo) {
    MonitorScheduleStopDetailDTO scheduleStopDto = monitorScheduleStopService.getScheduleStopState(monitorScheduleStopRequestVo);
    return HttpResult.success(scheduleStopDto);
  }

}
