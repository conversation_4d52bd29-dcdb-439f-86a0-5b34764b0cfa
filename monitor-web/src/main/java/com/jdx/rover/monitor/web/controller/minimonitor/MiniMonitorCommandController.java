/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MiniMonitorCommandService;
import com.jdx.rover.monitor.vo.MiniMonitorAsArrivedRequestVO;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteCommandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for mini monitor command info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/command")
public class MiniMonitorCommandController {

  @Autowired
  private MiniMonitorCommandService miniMonitorCommandService;

  /**
   * <p>
   * Post as arrived request.
   * </p>
   *
   * @param miniMonitorAsArrivedRequestVo The as arrived request view object.
   * @return The remote request response dto.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  @PostMapping("/as_arrived")
  public HttpResult postAsArrivedRequest(
          @Valid @RequestBody MiniMonitorAsArrivedRequestVO miniMonitorAsArrivedRequestVo) {
    ParameterCheckUtility.checkNotNull(miniMonitorAsArrivedRequestVo,
            "miniMonitorAsArrivedRequestVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(miniMonitorAsArrivedRequestVo.getVehicleName(),
            "miniMonitorAsArrivedRequestVo#vehicleName");
    return miniMonitorCommandService.postAsArrivedRequest(miniMonitorAsArrivedRequestVo);
  }

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  @PostMapping("/emergency_stop")
  public HttpResult publishEmergencyStopCommand(@RequestBody MiniMonitorRemoteCommandVO emergencyStopCommandVO) {
    return miniMonitorCommandService.postEmergencyStopRequest(emergencyStopCommandVO);
  }

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  @PostMapping("/emergency_brake")
  public HttpResult publishEmergencyBrakeCommand(@RequestBody MiniMonitorRemoteCommandVO emergencyBrakeCommandVo) {
    return miniMonitorCommandService.postEmergencyBrakeRequest(emergencyBrakeCommandVo);
  }

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  @PostMapping("/recovery")
  public HttpResult publishRecoveryCommand(@RequestBody MiniMonitorRemoteCommandVO monitorRecoveryCommandVo) {
    return miniMonitorCommandService.postRecoveryRequest(monitorRecoveryCommandVo);
  }

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  @PostMapping("/lamp_control")
  public HttpResult publishControlLampCommand(@RequestBody MiniMonitorRemoteCommandVO monitorControlLampCommandVo) {
    return miniMonitorCommandService.postLampRequest(monitorControlLampCommandVo);
  }
}

