/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.MonitorPostTrafficLightCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.NoSignalIntersectionCommandVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is api interface for mini monitor vehicle command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/monitor/web/command")
public class MonitorRemoteCommandController {

  @Autowired
  private MonitorRemoteCommandService remoteCommandService;

  /**
   * <p>
   * Post emergency stop command.
   * </p>
   */
  @PostMapping("/emergency_stop")
  public HttpResult publishEmergencyStopCommand(@RequestBody MonitorRemoteCommandVO emergencyStopCommandVO) {
    return remoteCommandService.postEmergencyStopRequest(emergencyStopCommandVO);
  }

  /**
   * <p>
   * 急刹
   * </p>
   */
  @PostMapping("/emergency_brake")
  public HttpResult publishEmergencyBrakeCommand(@RequestBody MonitorRemoteCommandVO emergencyBrakeCommandVO) {
    return remoteCommandService.postEmergencyBrakeRequest(emergencyBrakeCommandVO);
  }

  /**
   * <p>
   * Post as arrived command.
   * </p>
   */
  @PostMapping("/as_arrived")
  public HttpResult publishAsArrivedCommand(@RequestBody MonitorRemoteCommandVO monitorAsArrivedCommandVo) {
    return remoteCommandService.postAsArrivedRequest(monitorAsArrivedCommandVo);
  }

  /**
   * <p>
   * Post recovery command.
   * </p>
   */
  @PostMapping("/recovery")
  public HttpResult publishRecoveryCommand(@RequestBody MonitorRemoteCommandVO monitorRecoveryCommandVo) {
    return remoteCommandService.postRecoveryRequest(monitorRecoveryCommandVo);
  }

  /**
   * <p>
   * Post restart command.
   * </p>
   */
  @PostMapping("/restart")
  public HttpResult publishRestartCommand(@RequestBody MonitorRemoteCommandVO monitorRestartCommandVo) {
    return remoteCommandService.postRestartRequest(monitorRestartCommandVo);
  }

  /**
   * <p>
   * Publish control traffic light command.
   * </p>
   */
  @PostMapping("/control_traffic_light")
  public HttpResult publishControlTrafficLightCommand(@RequestBody MonitorPostTrafficLightCommandVO monitorPostTrafficLightCommandVo) {
    return remoteCommandService.postPassTrafficLightRequest(monitorPostTrafficLightCommandVo);
  }

  /**
   * 远程电源管理
   */
  @PostMapping("/power/manager/{powerManagerAction}/{vehicleName}")
  public HttpResult powerStop(@PathVariable("powerManagerAction") String powerManagerAction
      , @PathVariable("vehicleName") String vehicleName) {
    return remoteCommandService.powerManager(powerManagerAction, vehicleName, UserUtils.getLoginUser());
  }

  /**
   * 远程电源管理
   */
  @PostMapping("/estop_button")
  public HttpResult publishRelieveButtonStopCommand(@RequestBody MonitorRemoteCommandVO monitorRecoveryCommandVo) {
    return remoteCommandService.postRelieveButtonStopRequest(monitorRecoveryCommandVo);
  }

  /**
   * 通过无信号路口
   */
  @PostMapping("/pass_no_signal_intersection")
  public HttpResult passNoSignalIntersection(@RequestBody @Valid NoSignalIntersectionCommandVO noSignalIntersectionCommandVO) {
    return remoteCommandService.passNoSignalIntersection(noSignalIntersectionCommandVO);
  }

  /**
   * 临时停车
   */
  @PostMapping("/temporaryStop")
  public HttpResult publishTemporaryStopCommand(@RequestBody @Valid MonitorRemoteCommandVO temporaryStopVo) {
    return remoteCommandService.postTemporaryStopCommand(temporaryStopVo);
  }

  /**
   * <p>
   *  获取用户接管的车辆列表
   * </p>
   *
   */
  @GetMapping("/getTakeOverVehicle")
  public HttpResult getTakeOverVehicle(@RequestParam(value = "userName") String userName) {
    return remoteCommandService.getTakeOverVehicle(userName);
  }
}
