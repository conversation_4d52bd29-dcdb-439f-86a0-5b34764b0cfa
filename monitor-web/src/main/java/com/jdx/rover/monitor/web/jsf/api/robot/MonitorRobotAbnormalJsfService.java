/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.robot;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.robot.RobotAbnormalPageDTO;
import com.jdx.rover.monitor.vo.robot.RobotAbnormalPageSearchVO;

/**
 * <p>
 * 监控机器人异常服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
public interface MonitorRobotAbnormalJsfService {

    HttpResult<PageDTO<RobotAbnormalPageDTO>> pageSearch(RobotAbnormalPageSearchVO robotAbnormalPageSearchVo);

}
