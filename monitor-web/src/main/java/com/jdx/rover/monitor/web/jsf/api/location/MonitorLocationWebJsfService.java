/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.location;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorLocationCloudMapDTO;
import com.jdx.rover.monitor.vo.MonitorGetCloudMapVO;
import com.jdx.rover.monitor.vo.MonitorLocationCloudMapVO;

/**
 * 车辆人工定位服务
 *
 * <AUTHOR>
 */
public interface MonitorLocationWebJsfService {

  /**
   * 上报点云地图以及备选点位
   */
  public HttpResult<Void> uploadVehicleCloudMap(MonitorLocationCloudMapVO locationCloudMapVo);

  /**
   * 上报点云地图以及备选点位
   */
  public HttpResult<MonitorLocationCloudMapDTO> getVehicleCloudMap(MonitorGetCloudMapVO getCloudMapVo);

}
