/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.robot;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.dto.robot.RobotGroupMapInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.service.robot.RobotMapService;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;
import com.jdx.rover.monitor.web.jsf.api.robot.MonitorRobotMapJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 监控机器人工单服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
public class MonitorRobotMapWebJsfServiceImpl extends AbstractProvider<MonitorRobotMapJsfService> implements MonitorRobotMapJsfService {

    /**
     * 提供机器人地图相关服务的实例
     */
    private final RobotMapService robotMapService;

    /**
     * 根据机器人分组获取地图信息
     * @param mapInfoRequestVo 机器人分组地图信息请求对象
     * @return 机器人分组地图信息DTO对象
     */
    @Override
    @ServiceInfo(name = "机器人分组地图信息", webUrl = "/monitor/web/robot/map/getMapByGroup")
    public HttpResult<RobotGroupMapInfoDTO> getMapInfoByGroup(RobotMapInfoRequestVO mapInfoRequestVo) {
        return robotMapService.getMapInfoByGroup(mapInfoRequestVo);
    }

    /**
     * 获取机器人地图路径信息。
     * @param robotDeviceBasicSearchVo 机器人基础信息。
     * @return 机器人地图路径信息DTO。
     */
    @Override
    @ServiceInfo(name = "机器人地图路径信息", webUrl = "/monitor/web/robot/map/getDeviceRoute")
    public HttpResult<RobotMapRouteInfoDTO> getMapDeviceRoute(RobotDeviceBasicSearchVO robotDeviceBasicSearchVo) {
        return HttpResult.success(robotMapService.getMapDeviceRoute(robotDeviceBasicSearchVo));
    }

}
