package com.jdx.rover.monitor.web.jsf.api.user;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.user.GetCockpitStatusDTO;

/**
 * @description: 用户页面切换Controller
 * @author: wang<PERSON><PERSON><PERSON>
 * @create: 2024-06-12 17:14
 **/
public interface MonitorUserChangeWebJsfService {

    /**
     * 1、获取用户座席状态
     *
     */
    public HttpResult<GetCockpitStatusDTO> getCockpitStatus();
}