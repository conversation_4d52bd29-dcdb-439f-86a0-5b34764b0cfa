/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.s3;

import com.amazonaws.HttpMethod;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.s3.S3PreSignDTO;
import com.jdx.rover.monitor.service.s3.S3PreSignUrlService;
import com.jdx.rover.monitor.vo.s3.S3PreSignVO;
import com.jdx.rover.monitor.web.jsf.api.s3.S3PresignUrlWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * oss对象存储操作
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class S3PresignUrlWebJsfServiceImpl extends AbstractProvider<S3PresignUrlWebJsfService> implements S3PresignUrlWebJsfService{

    private final S3PreSignUrlService s3PreSignUrlService;

    /**
     * 预签名get url,用于下载
     *
     * @param s3PreSignVO
     * @return
     */
    @ServiceInfo(name = "获取签名地址", webUrl = "/monitor/web/s3/webTerminal/preSignGetUrl")
    public HttpResult<String> preSignGetUrl(S3PreSignVO s3PreSignVO) {
        String url = s3PreSignUrlService.preSignUrl(s3PreSignVO, HttpMethod.GET);
        return HttpResult.success(url);
    }

    /**
     * 预签名put url,用于上传
     *
     * @param s3PreSignVO
     * @return
     */
    @ServiceInfo(name = "获取签名地址", webUrl = "/monitor/web/s3/webTerminal/preSignPutUrl")
    public HttpResult<String> preSignPutUrl(S3PreSignVO s3PreSignVO) {
        String url = s3PreSignUrlService.preSignUrl(s3PreSignVO, HttpMethod.PUT);
        return HttpResult.success(url);
    }

    /**
     * 预签名get和put url,用于下载上传
     *
     * @param s3PreSignVO
     * @return
     */
    @ServiceInfo(name = "获取签名地址", webUrl = "/monitor/web/s3/webTerminal/preSignGetAndPutUrl")
    public HttpResult<S3PreSignDTO> preSignGetAndPutUrl(S3PreSignVO s3PreSignVO) {
        S3PreSignDTO dto = s3PreSignUrlService.preSignGetAndPutUrl(s3PreSignVO);
        return HttpResult.success(dto);
    }
}
