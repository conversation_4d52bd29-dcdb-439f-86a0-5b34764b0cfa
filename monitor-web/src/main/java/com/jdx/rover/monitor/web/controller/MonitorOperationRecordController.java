/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.google.common.base.Preconditions;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import com.jdx.rover.monitor.search.MonitorDataListSearch;
import com.jdx.rover.monitor.service.web.MonitorOperationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for monitor operation record.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value="/monitor/web/operation")
public class MonitorOperationRecordController {

  /**
   * <p>
   * The operation record service.
   * </p>
   */
  @Autowired
  private MonitorOperationRecordService monitorOperationRecordService;

  /**
   * <p>
   * Search operation record bby search entity.
   * </p>
   * 
   * @param monitorDataListSearch The search entity for monitor user
   *                                      operation log.
   * @return The correspond supervisor operation record dto data transform
   *         objects.
   * @throws IllegalArgumentException If the argument doesn't meet the
   *                                  requirement.
   */
  @PostMapping("/list")
  public HttpResult search(@Valid @RequestBody  MonitorDataListSearch monitorDataListSearch) {
    Preconditions.checkNotNull(monitorDataListSearch, "查询条件不能为空");
    Preconditions.checkNotNull(monitorDataListSearch.getStartTime(), "查询条件开始时间不能为空");
    Preconditions.checkNotNull(monitorDataListSearch.getEndTime(), "查询条件结束时间不能为空");
    PageDTO<MonitorOperationRecordDTO> operationRecordDtoList = monitorOperationRecordService
        .search(monitorDataListSearch);
    return HttpResult.success(operationRecordDtoList);
  }

}