/*
 * Copyright (c) 2023 JD.com, Inc. All Rights Reserved.
 */
package com.jdx.rover.monitor.web.controller.cockpit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作记录controller类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping(value = "/monitor/web/cockpit")
public class WorkRecordController {

}