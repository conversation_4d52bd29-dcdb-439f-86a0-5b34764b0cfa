/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.video;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.VehicleNameListVO;
import com.jdx.rover.monitor.vo.drive.MonitorHistoryVideoVO;
import com.jdx.rover.monitor.web.jsf.api.video.MonitorVideoWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 视频服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MonitorVideoWebJsfServiceImpl extends AbstractProvider<MonitorVideoWebJsfService> implements MonitorVideoWebJsfService {

  private final MonitorVideoService videoService;

  @Override
  @ServiceInfo(name = "获取播放地址", webUrl = "/monitor/web/video/url")
  public HttpResult<Object> getVideoAddress(VehicleNameBasicVO vehicleNameBasicVo) {
    return videoService.getVehicleVideoInfo(vehicleNameBasicVo.getVehicleName());
  }

  @Override
  @ServiceInfo(name = "获取播放地址", webUrl = "/monitor/web/video/url/getVideoUrlList")
  public HttpResult<Object> getVideoAddressList(VehicleNameListVO vehicleNameListVo) {
    return videoService.getVehicleVideoList(vehicleNameListVo.getVehicleNameList());
  }

  @Override
  @ServiceInfo(name = "获取历史播放地址", webUrl = "/monitor/web/video/process/history/contain")
  public HttpResult<Object> searchHistoryVideo(MonitorHistoryVideoVO historyVideoVo) {
    return videoService.getHistoryVideo(historyVideoVo);
  }


}
