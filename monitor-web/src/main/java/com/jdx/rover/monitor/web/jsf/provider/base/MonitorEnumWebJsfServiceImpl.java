/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.base;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.EnumDTO;
import com.jdx.rover.monitor.dto.MonitorDataCategoryDTO;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import com.jdx.rover.monitor.web.jsf.api.base.MonitorEnumWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 通用枚举请求
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorEnumWebJsfServiceImpl extends AbstractProvider<MonitorEnumWebJsfService> implements MonitorEnumWebJsfService {

  private final EnumService enumService;


  @ServiceInfo(name = "获取下拉列表数据", webUrl = "/monitor/web/enum/get_enum_list_map")
  public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@Valid CommonDrownListVO commonDrownListVO) {
    Map<String, List<Map<String, Object>>> result = enumService.getEnumListMap(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }

  @ServiceInfo(name = "获取多级分类枚举", webUrl = "/monitor/web/enum/get_category_list")
  public HttpResult<Map<String,List<MonitorDataCategoryDTO>>> getMultiCategoryList(@Valid CommonDrownListVO commonDrownListVO) {
    Map<String, List<MonitorDataCategoryDTO>> result = enumService.getMultiCategoryList(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }

  @ServiceInfo(name = "获取所属团队下拉列表", webUrl = "/monitor/web/enum/get_cockpit_team_list")
  public HttpResult<List<EnumDTO>> getCockpitTeamList() {
    return HttpResult.success(enumService.getCockpitTeamList());
  }

  @ServiceInfo(name = "获取工单受理人下拉列表", webUrl = "/monitor/web/enum/get_user_list")
  public HttpResult<List<EnumDTO>> getUserList() {
    return HttpResult.success(enumService.getUserList());
  }

  @ServiceInfo(name = "获取工单受理坐席下拉列表", webUrl = "/monitor/web/enum/get_cockpit_list")
  public HttpResult<List<EnumDTO>> getCockpitList() {
    return HttpResult.success(enumService.getCockpitList());
  }

  @ServiceInfo(name = "获取下拉列表数据", webUrl = "/monitor/web/enum/get_pda_enum_list_map")
  public HttpResult<Map<String, List<Map<String, Object>>>> getPdaEnumListMap(@Valid CommonDrownListVO commonDrownListVO) {
    Map<String, List<Map<String, Object>>> result = enumService.getPdaEnumListMap(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }
}