package com.jdx.rover.monitor.web.jsf.provider.command;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.api.domain.dto.VehicleTakeOverDTO;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.vehicle.VehicleSpeedLimitService;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.MonitorPostTrafficLightCommandVO;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.MonitorSetSpeedLimitVO;
import com.jdx.rover.monitor.vo.MonitorVehiclePowManageVO;
import com.jdx.rover.monitor.vo.MonitorVehiclePowerOnVO;
import com.jdx.rover.monitor.vo.NoSignalIntersectionCommandVO;
import com.jdx.rover.monitor.vo.UserNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.command.MonitorRemoteCommandWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监控远程指令操作
 * @author: gulin
 * @date: 2025/02/20
 **/
@Service
@RequiredArgsConstructor
public class MonitorRemoteCommandWebJsfServiceImpl extends AbstractProvider<MonitorRemoteCommandWebJsfService> implements MonitorRemoteCommandWebJsfService{

    private final MonitorRemoteCommandService remoteCommandService;
    private final VehicleSpeedLimitService vehicleSpeedLimitService;

    /**
     * 急停命令。
     * @param emergencyStopCommandVO 紧急停止命令信息。
     * @return 发布结果。
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishEmergencyStopCommand(MonitorRemoteCommandVO emergencyStopCommandVO) {
        return remoteCommandService.postEmergencyStopRequest(emergencyStopCommandVO);
    }

    /**
     * 发布紧急制动命令。
     * @param emergencyBrakeCommandVO 紧急制动命令对象。
     * @return HttpResult 对象，包含了远程命令服务的响应结果。
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishEmergencyBrakeCommand(MonitorRemoteCommandVO emergencyBrakeCommandVO) {
        return remoteCommandService.postEmergencyBrakeRequest(emergencyBrakeCommandVO);
    }

    /**
     * 发布到达命令
     * @param monitorAsArrivedCommandVo 到达命令的详细信息
     * @return 发布结果
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishAsArrivedCommand(MonitorRemoteCommandVO monitorAsArrivedCommandVo) {
        return remoteCommandService.postAsArrivedRequest(monitorAsArrivedCommandVo);
    }

    /**
     * 发布恢复命令。
     * @param monitorRecoveryCommandVo 监控恢复命令信息。
     * @return 发布结果。
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishRecoveryCommand(MonitorRemoteCommandVO monitorRecoveryCommandVo) {
        return remoteCommandService.postRecoveryRequest(monitorRecoveryCommandVo);
    }

    /**
     * 发布重启命令
     * @param monitorRestartCommandVo 重启命令信息
     * @return 发布结果
     */
    @Override
    @ServiceInfo(name = "监控软件重启", webUrl = "/monitor/web/command/restart")
    public HttpResult<MonitorRemoteCommandDTO> publishRestartCommand(MonitorRemoteCommandVO monitorRestartCommandVo) {
        return remoteCommandService.postRestartRequest(monitorRestartCommandVo);
    }

    /**
     * 发布通过交通灯指令。
     * @param monitorPostTrafficLightCommandVo 监控点交通灯指令VO对象，包含指令相关信息。
     * @return 发布指令的结果。
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishControlTrafficLightCommand(MonitorPostTrafficLightCommandVO monitorPostTrafficLightCommandVo) {
        return remoteCommandService.postPassTrafficLightRequest(monitorPostTrafficLightCommandVo);
    }

    /**
     * 发送远程命令以停止指定车辆的电源。
     * @param vehiclePowManageVo 包含操作类型和车辆名称的对象。
     * @return 远程命令执行结果。
     */
    @Override
    @ServiceInfo(name = "监控远程关机、断电重启", webUrl = "/monitor/web/command/power/manager")
    public HttpResult<MonitorRemoteCommandDTO> powerStop(MonitorVehiclePowManageVO vehiclePowManageVo) {
        return remoteCommandService.powerManager(vehiclePowManageVo.getPowerManagerAction(), vehiclePowManageVo.getVehicleName(), UserUtils.getLoginUser());
    }

    /**
     * 发布解除按钮停止命令
     * @param monitorRecoveryCommandVo 监控恢复命令VO对象
     * @return 发布命令的结果
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishRelieveButtonStopCommand(MonitorRemoteCommandVO monitorRecoveryCommandVo) {
        return remoteCommandService.postRelieveButtonStopRequest(monitorRecoveryCommandVo);
    }

    /**
     * 通过无信号交叉口命令VO执行远程命令服务的无信号交叉口通行操作。
     * @param noSignalIntersectionCommandVO 无信号交叉口命令VO，包含执行操作所需的信息。
     * @return HttpResult 对象，表示操作的结果。
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> passNoSignalIntersection(NoSignalIntersectionCommandVO noSignalIntersectionCommandVO) {
        return remoteCommandService.passNoSignalIntersection(noSignalIntersectionCommandVO);
    }

    /**
     * 发布临时停止命令
     * @param temporaryStopVo 临时停止命令信息
     * @return 发布结果
     */
    @Override
    public HttpResult<MonitorRemoteCommandDTO> publishTemporaryStopCommand(MonitorRemoteCommandVO temporaryStopVo) {
        return remoteCommandService.postTemporaryStopCommand(temporaryStopVo);
    }

    /**
     * 获取可接管的车辆信息。
     * @param userNameBasicVo 用户基本信息对象，包含用户名。
     * @return HttpResult 对象，封装了接管车辆的结果。
     */
    @Override
    public HttpResult<List<VehicleTakeOverDTO>> getTakeOverVehicle(UserNameBasicVO userNameBasicVo) {
        return remoteCommandService.getTakeOverVehicle(userNameBasicVo.getUserName());
    }

    /**
     * 远驾远程开机
     */
    @Override
    @ServiceInfo(name = "远驾远程开机", webUrl = "/monitor/web/command/powerOn")
    public HttpResult<Void> powerOn(MonitorVehiclePowerOnVO monitorVehiclePowerOnVO) {
        MonitorErrorEnum monitorErrorEnum = remoteCommandService.powerOn(monitorVehiclePowerOnVO);
        return new HttpResult<>(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @Override
    @ServiceInfo(name = "设置临时限速", webUrl = "/monitor/web/command/setTempSpeedLimit")
    public HttpResult<Void> postMaxVelocityRequest(MonitorSetSpeedLimitVO monitorSetSpeedLimitVO) {
        return JsfResponse.response(() -> vehicleSpeedLimitService.postMaxVelocityRequest(monitorSetSpeedLimitVO));
    }
}