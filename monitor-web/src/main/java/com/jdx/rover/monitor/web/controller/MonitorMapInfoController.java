/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorMetadataPositionDTO;
import com.jdx.rover.monitor.dto.MonitorVehicleMapInfoDTO;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import com.jdx.rover.monitor.vo.MonitorStationVehicleMapInfoRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * This is a controller for monitor vehicle map info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/map")
@RestController
public class MonitorMapInfoController {

  @Autowired
  private MonitorMapInfoService monitorMapInfoService;

  /**
   * <p>
   * Search vehicle map realtime info by vehicle name.
   * </p>
   */
  @GetMapping("/vehicle/realtime/{vehicleName}")
  public HttpResult getVehicleRealtimeInfo(@PathVariable(value = "vehicleName") String vehicleName) {
    return monitorMapInfoService.getVehicleMapRealtimeInfo(vehicleName);
  }

  /**
   * <p>
   * Search vehicle and station map realtime info.
   * </p>
   */
  @PostMapping("/vehicle/list")
  public HttpResult getStationAndVehicleMapInfo(@RequestBody MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    return monitorMapInfoService.getStationAndVehicleMapInfo(stationVehicleMapInfoRequestVo);
  }

  /**
   * <p>
   * Search vehicle map pnc info.
   * </p>
   */
  @GetMapping("/vehicle/{vehicleName}")
  public HttpResult getVehicleMapInfo(@PathVariable(value = "vehicleName") String vehicleName) {
    return HttpResult.success(monitorMapInfoService.getSingleVehicleRouting(vehicleName));
  }

  /**
   * <p>
   * Search vehicle position list.
   * </p>
   */
  @PostMapping("/position/list")
  public HttpResult getVehicleRealtimePositionInfo(@RequestBody MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo) {
    Map<String, MonitorVehicleMapInfoDTO> result = monitorMapInfoService.getVehicleRealtimePositionInfo(stationVehicleMapInfoRequestVo);
    return HttpResult.success(result);
  }

  @GetMapping("/position/{metaType}/{key}")
  public HttpResult getMetaDataPositionInfo(@PathVariable(value = "metaType") String metaType, @PathVariable(value = "key") String key,
                                            @RequestParam(value = "positionType", defaultValue = "WGS84") String positionType) {
    MonitorMetadataPositionDTO metadataPositionDto = monitorMapInfoService.getMetaDataPositionInfo(metaType, key, positionType);
    return HttpResult.success(metadataPositionDto);
  }

}