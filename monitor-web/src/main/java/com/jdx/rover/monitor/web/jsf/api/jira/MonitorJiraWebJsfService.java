/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.jira;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.vehicle.VehicleRealtimeJsfDTO;
import com.jdx.rover.monitor.dto.jira.JiraAlarmListDTO;
import com.jdx.rover.monitor.vo.BugAddVO;
import com.jdx.rover.monitor.vo.SemiJiraAddVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.VehicleRealtimeVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * This is a controller class for jira problem reporting.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorJiraWebJsfService {

  public HttpResult<String> reportJira(@Valid BugAddVO bugAddVO);

  public HttpResult<String> semiReportJira(@Valid SemiJiraAddVO jiraAddVo);

  public HttpResult<JiraAlarmListDTO> getAvailableReportAlarmList(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult<List<String>> getRoverList();

  public HttpResult<VehicleRealtimeJsfDTO> getVehicleRealtimeInfo(VehicleRealtimeVO vehicleRealtimeVO);
}
