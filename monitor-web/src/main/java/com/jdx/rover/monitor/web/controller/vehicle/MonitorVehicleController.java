/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.web.controller.vehicle;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.vehicle.CheckSpeedLimitDTO;
import com.jdx.rover.monitor.service.vehicle.VehicleSpeedLimitService;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/8/12 02:23
 * @description 车辆调用查询
 */
@RestController
@RequestMapping(value = "/monitor/web/vehicle")
@RequiredArgsConstructor
public class MonitorVehicleController {

    /**
     * 车辆限速服务
     */
    private final VehicleSpeedLimitService vehicleSpeedLimitService;

    /**
     * 查询车辆当前是否有限速配置
     *
     * @param vehicleNameBasicVO vehicleNameBasicVO
     * @return CheckSpeedLimitDTO
     */
    @PostMapping("/checkSpeedLimit")
    public HttpResult<CheckSpeedLimitDTO> checkSpeedLimit(@RequestBody VehicleNameBasicVO vehicleNameBasicVO) {
        return HttpResult.success(vehicleSpeedLimitService.checkSpeedLimit(vehicleNameBasicVO.getVehicleName()));
    }
}
