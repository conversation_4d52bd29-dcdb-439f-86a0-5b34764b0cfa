/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.base;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.EnumDTO;
import com.jdx.rover.monitor.dto.MonitorDataCategoryDTO;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 通用枚举请求
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
public interface MonitorEnumWebJsfService {

  /**
   * 获取下拉列表数据
   */
  public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@Valid CommonDrownListVO commonDrownListVO);

  /**
   * 获取多级分类枚举
   */
  public HttpResult<Map<String,List<MonitorDataCategoryDTO>>> getMultiCategoryList(@Valid CommonDrownListVO commonDrownListVO);

  /**
   * 获取所属团队下拉列表
   *
   * @return List<EnumDTO>
   */
  public HttpResult<List<EnumDTO>> getCockpitTeamList();

  /**
   * 获取工单受理人下拉列表
   *
   * @return List<EnumDTO>
   */
  public HttpResult<List<EnumDTO>> getUserList();

  /**
   * 获取工单受理坐席下拉列表
   *
   * @return List<EnumDTO>
   */
  public HttpResult<List<EnumDTO>> getCockpitList();

  /**
   * 获取下拉列表数据
   */
  public HttpResult<Map<String, List<Map<String, Object>>>> getPdaEnumListMap(@Valid CommonDrownListVO commonDrownListVO);
}