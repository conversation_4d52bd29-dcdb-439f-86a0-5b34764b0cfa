/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.web.jsf.api.alarm;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.ManualAlarmRecordDTO;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 监控告警接口
 *
 * <AUTHOR>
 */
public interface MonitorAlarmWebJsfService {

  /**
   * <p>
   *  巡查人工告警
   * </p>
   */
  public HttpResult<Void> reportManualAlarm(@Valid MonitorManualAlarmReportVO manualAlarmReportVo);

  /**
   * <p>
   *  当天巡查人工告警
   * </p>
   */
  public HttpResult<List<ManualAlarmRecordDTO>> listTodayManualAlarm(VehicleNameBasicVO vehicleNameBasicVo);


}
