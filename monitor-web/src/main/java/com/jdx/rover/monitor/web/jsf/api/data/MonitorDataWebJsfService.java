package com.jdx.rover.monitor.web.jsf.api.data;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.data.GetCockpitDataPageDTO;
import com.jdx.rover.monitor.dto.data.GetCockpitTeamDataListDTO;
import com.jdx.rover.monitor.dto.data.GetSupportDataListDTO;
import com.jdx.rover.monitor.vo.data.GetCockpitDataPageVO;
import com.jdx.rover.monitor.vo.data.GetCockpitTeamDataListVO;
import com.jdx.rover.monitor.vo.data.GetSupportDataListVO;

import javax.validation.Valid;

/**
 * 驾舱数据Controller
 */
public interface MonitorDataWebJsfService {

    /**
     * 获取坐席数据列表
     *
     * @param getCockpitDataPageVO getCockpitDataPageVO
     * @return PageDTO<GetCockpitDataPageDTO>
     */
    public HttpResult<PageDTO<GetCockpitDataPageDTO>> getCockpitDataPage(@Valid GetCockpitDataPageVO getCockpitDataPageVO);

    /**
     * 获取远驾团队数据列表
     *
     * @param getCockpitTeamDataListVO getCockpitTeamDataListVO
     * @return PageDTO<GetCockpitTeamDataListDTO>
     */
    public HttpResult<PageDTO<GetCockpitTeamDataListDTO>> getCockpitTeamDataList(GetCockpitTeamDataListVO getCockpitTeamDataListVO);

    /**
     * 获取技术支持数据列表
     * @param getSupportDataListVO getSupportDataListVO
     * @return PageDTO<GetSupportDataListDTO>
     */
    public HttpResult<PageDTO<GetSupportDataListDTO>> getSupportDataList(@Valid GetSupportDataListVO getSupportDataListVO);
}