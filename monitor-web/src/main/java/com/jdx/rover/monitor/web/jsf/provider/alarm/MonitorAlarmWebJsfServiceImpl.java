/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.web.jsf.provider.alarm;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.dto.ManualAlarmRecordDTO;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.alarm.MonitorAlarmWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监控告警接口
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MonitorAlarmWebJsfServiceImpl extends AbstractProvider<MonitorAlarmWebJsfService> implements MonitorAlarmWebJsfService {

  /**
   * 告警服务接口
   */
  private final MonitorAlarmService monitorAlarmService;

  @ServiceInfo(name = "巡查人工告警", webUrl = "/monitor/web/alarm/reportManualAlarm")
  public HttpResult<Void> reportManualAlarm(@Valid MonitorManualAlarmReportVO manualAlarmReportVo) {
    return monitorAlarmService.reportManualAlarm(manualAlarmReportVo, ManualAlarmSourceEnum.PATROL_MONITOR);
  }

  @ServiceInfo(name = "当天巡查人工告警", webUrl = "/monitor/web/alarm/listTodayManualAlarm")
  public HttpResult<List<ManualAlarmRecordDTO>> listTodayManualAlarm(VehicleNameBasicVO vehicleNameBasicVo) {
    return HttpResult.success(monitorAlarmService.listTodayManualAlarm(vehicleNameBasicVo.getVehicleName()));
  }

}
