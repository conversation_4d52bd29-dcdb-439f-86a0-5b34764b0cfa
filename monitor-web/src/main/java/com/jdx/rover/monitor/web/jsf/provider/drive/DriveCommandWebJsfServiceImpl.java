/*
 * Copyright (c) 2025 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web.jsf.provider.drive;

import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.enums.drive.command.DriveRemoteCommandTypeEnum;
import com.jdx.rover.monitor.service.drive.DriveRemoteCommandService;
import com.jdx.rover.monitor.vo.drive.DriveRemoteCommandVO;
import com.jdx.rover.monitor.web.jsf.api.drive.DriveCommandWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 平行驾驶控制命令
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class DriveCommandWebJsfServiceImpl extends AbstractProvider<DriveCommandWebJsfService> implements DriveCommandWebJsfService {
    /**
     * 远程指令服务
     */
    private final DriveRemoteCommandService driveRemoteCommandService;

    @Override
    @ServiceInfo(name = "发送进入接管命令", webUrl = "/monitor/web/drive/command/send/enter-take-over")
    public HttpResult<?> sendEnterTakeOver(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_ENTER_TAKE_OVER.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送退出接管命令", webUrl = "/monitor/web/drive/command/send/exit-take-over")
    public HttpResult<?> sendExitTakeOver(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_DRIVE_EXIT_TAKE_OVER.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送视同到达命令", webUrl = "/monitor/web/drive/command/send/as-arrived")
    public HttpResult<?> sendAsArrived(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_REQUEST_AS_ARRIVED.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送解除按钮停车命令", webUrl = "/monitor/web/drive/command/send/relieve-button-stop")
    public HttpResult<?> sendRelieveButtonStop(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_REQUEST_RELIEVE_BUTTON_STOP.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送软件重启命令", webUrl = "/monitor/web/drive/command/send/remote-restart")
    public HttpResult<?> sendRemoteRestart(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_REQUEST_REMOTE_RESTART.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送断电重启命令", webUrl = "/monitor/web/drive/command/send/power_reboot")
    public HttpResult<?> sendPowerReboot(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_CONTROL_POWER_REBOOT.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送远程下电命令", webUrl = "/monitor/web/drive/command/send/power_off")
    public HttpResult<?> sendPowerOff(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_CONTROL_POWER_OFF.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "发送命令", webUrl = "/monitor/web/drive/command/sendCommand")
    public HttpResult<?> sendCommand(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "切换无图模式", webUrl = "/monitor/web/drive/command/send/run-no-map")
    public HttpResult<?> runNoMap(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.RUN_NO_MAP.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "切换有图模式", webUrl = "/monitor/web/drive/command/send/run-have-map")
    public HttpResult<?> runHaveMap(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.RUN_HAVE_MAP.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "控制油门", webUrl = "/monitor/web/drive/command/send/torque-control")
    public HttpResult<?> sendTorqueControl(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.MAX_TORQUE.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "控制速度", webUrl = "/monitor/web/drive/command/send/velocity-control")
    public HttpResult<?> sendVelocityControl(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        driveRemoteCommandVO.setRemoteCommandType(DriveRemoteCommandTypeEnum.REMOTE_VELOCITY_CONTROL.name());
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }

    @Override
    @ServiceInfo(name = "按钮命令", webUrl = "/monitor/web/drive/command/send/buttonCommand")
    public HttpResult<?> buttonCommand(@Valid DriveRemoteCommandVO driveRemoteCommandVO) {
        if (StringUtils.isBlank(driveRemoteCommandVO.getRemoteCommandType())) {
            return HttpResult.error(HttpCodeEnum.BAD_REQUEST.getValue(), "远程指令类型不能为空");
        }
        return driveRemoteCommandService.sendCommand(driveRemoteCommandVO);
    }
}
