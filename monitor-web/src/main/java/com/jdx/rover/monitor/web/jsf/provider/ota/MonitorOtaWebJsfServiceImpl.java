package com.jdx.rover.monitor.web.jsf.provider.ota;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.service.web.MonitorOtaService;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.ota.MonitorOtaWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 监控ota升级操作
 * @author: gulin
 * @create: 2024-06-12 17:14
 **/
@Service
@RequiredArgsConstructor
public class MonitorOtaWebJsfServiceImpl extends AbstractProvider<MonitorOtaWebJsfService> implements MonitorOtaWebJsfService{

    private final MonitorOtaService monitorOtaService;
    
    /**
     * 执行静默下载车辆基本信息操作。
     * @param vehicleNameBasicVo 车辆基本信息VO对象。
     */
    @Override
    public HttpResult<Void> mapSilentDownload(VehicleNameBasicVO vehicleNameBasicVo) {
        return monitorOtaService.mapSilentDownload(vehicleNameBasicVo.getVehicleName());
    }
}