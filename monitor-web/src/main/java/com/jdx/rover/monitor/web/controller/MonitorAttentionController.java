package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorAttentionService;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionCancelVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionCancelVO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 关注相关 Controller
 * </p>
 *
 * <AUTHOR>
 * @date 2023/09/22
 */
@RestController
@RequestMapping(value = "/monitor/web/attention")
public class MonitorAttentionController {

    @Resource
    private MonitorAttentionService monitorAttentionService;

    /**
     * 添加车辆关注列表
     * @param monitorVehicleAttentionAddVO
     */
    @PostMapping("/add_vehicle")
    public HttpResult addVehicle(@Valid @RequestBody MonitorVehicleAttentionAddVO monitorVehicleAttentionAddVO) {
        return monitorAttentionService.addVehicle(monitorVehicleAttentionAddVO);
    }

    /**
     * 取消车辆关注列表
     * @param monitorVehicleAttentionCancelVO
     * @return
     */
    @PostMapping("/cancel_vehicle")
    public HttpResult cancelVehicle(@Valid @RequestBody MonitorVehicleAttentionCancelVO monitorVehicleAttentionCancelVO) {
        return monitorAttentionService.cancelVehicle(monitorVehicleAttentionCancelVO);
    }

    /**
     * 取消用户订阅所有车辆
     * @return
     */
    @GetMapping("/cancel_all")
    public HttpResult cancelAll() {
        return monitorAttentionService.cancelAll();
    }

    /**
     * 获取车辆关注用户列表
     * @param
     * @return
     */
    @GetMapping("/getAttentionUser/{vehicleName}")
    public HttpResult getVehicleAttentionUser(@PathVariable(value = "vehicleName") String vehicleName) {
        return monitorAttentionService.getVehicleAttentionUser(vehicleName);
    }

    /**
     * 新增事件关注用户列表
     * @param
     * @return
     */
    @PostMapping("/addAttentionEvent")
    public HttpResult<List<String>> addAttentionEvent(@RequestBody MonitorEventAttentionAddVO attentionAddVO) {
        return monitorAttentionService.addAttentionEvent(attentionAddVO);
    }

    /**
     * 删除事件关注用户列表
     * @param
     * @return
     */
    @PostMapping("/deleteAttentionEvent")
    public HttpResult<List<String>> deleteAttentionEvent(@RequestBody MonitorEventAttentionCancelVO attentionCancelVO) {
        return monitorAttentionService.deleteAttentionEvent(attentionCancelVO);
    }

    /**
     * 获取事件关注用户列表
     * @param
     * @return
     */
    @PostMapping("/getAttentionEvent/{attentionEvent}")
    public HttpResult<List<String>> getAttentionEvent(@PathVariable(value = "attentionEvent") String attentionEvent) {
        return monitorAttentionService.getAttentionEvent(attentionEvent);
    }

}
