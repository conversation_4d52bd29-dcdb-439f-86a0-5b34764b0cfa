/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.robot;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceAbnormalInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceBasicDetailInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDevicePageSearchDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceRealtimeStateDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotTreeGroupBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceAbnormalSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDevicePageSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotProductSearchVO;
import com.jdx.rover.monitor.api.web.jsf.service.MonitorRobotJsfService;
import com.jdx.rover.monitor.service.robot.MonitorRobotService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 多合一巡检机器人服务
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
public class MonitorRobotJsfServiceImpl extends AbstractProvider<MonitorRobotJsfService> implements MonitorRobotJsfService {

  /**
   * 机器人服务接口。
   */
  public final MonitorRobotService robotService;

  /**
   * <p>
   * 获取设备树
   * </p>
   *
   */
  @Override
  public HttpResult<List<RobotTreeGroupBasicInfoDTO>> getDeviceGroupTree(RobotProductSearchVO productSearchVo) {
    return HttpResult.success(robotService.getGroupTree(productSearchVo));
  }

  /**
   * <p>
   * 分页请求设备实时状态
   * </p>
   *
   */
  @Override
  public HttpResult<RobotDevicePageSearchDTO> pageSearchRobotDevice(RobotDevicePageSearchVO devicePageSearchVo) {
    return HttpResult.success(robotService.pageSearchList(devicePageSearchVo));
  }

  /**
   * <p>
   * 获取设备基础详情
   * </p>
   *
   */
  @Override
  public HttpResult<RobotDeviceBasicDetailInfoDTO> getDeviceBasic(RobotDeviceBasicSearchVO deviceBasicSearchVo) {
    return HttpResult.success(robotService.getDeviceBasicInfo(deviceBasicSearchVo.getProductKey(), deviceBasicSearchVo.getDeviceName()));
  }

  /**
   * <p>
   * 获取设备实时状态
   * </p>
   *
   */
  @Override
  public HttpResult<RobotDeviceRealtimeStateDTO> getDeviceRealtimeStatus(RobotDeviceBasicSearchVO deviceBasicSearchVo) {
    return HttpResult.success(robotService.getRealtimeStatus(deviceBasicSearchVo.getProductKey(), deviceBasicSearchVo.getDeviceName()));
  }

  /**
   * <p>
   * 获取设备异常错误码列表
   * </p>
   *
   */
  @Override
  public HttpResult<List<RobotDeviceAbnormalInfoDTO>> getDeviceAbnormalInfo(RobotDeviceAbnormalSearchVO deviceAbnormalSearchVo) {
    return HttpResult.success(robotService.getDeviceAbnormalInfo(deviceAbnormalSearchVo));
  }
}