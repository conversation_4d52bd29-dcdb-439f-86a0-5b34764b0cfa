/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频服务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/monitor/web/video")
public class MonitorVideoController {
  @Autowired
  private MonitorVideoService videoService;

  /**
   * 获取播放地址
   */
  @GetMapping(value = "/url/{vehicleName}")
  public HttpResult getVideoAddress(@PathVariable(value = "vehicleName") String vehicleName) {
    return videoService.getVehicleVideoInfo(vehicleName);
  }

  /**
   * 获取播放地址
   */
  @GetMapping(value = "/url/getVideoUrlList")
  public HttpResult getVideoAddressList(@RequestParam(value = "vehicleNameList") List<String> vehicleNameList) {
    return videoService.getVehicleVideoList(vehicleNameList);
  }

}
