package com.jdx.rover.monitor.web.advice;

import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * CustomContentCachingRequestWrapper
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CustomContentCachingRequestWrapper extends ContentCachingRequestWrapper {

    private final AtomicBoolean isFirst = new AtomicBoolean(true);

    public CustomContentCachingRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (isFirst.get()) {
            isFirst.set(false);
            return super.getInputStream();
        }
        return new CustomServletInputStream(super.getContentAsByteArray());
    }

    protected boolean isJsonRequest() {
        String contentType = getContentType();
        return (contentType != null && contentType.contains("application/json"));
    }

    static class CustomServletInputStream extends ServletInputStream {
        private final InputStream sourceStream;
        private boolean finished = false;

        public CustomServletInputStream(byte[] bytes) {
            this.sourceStream = new ByteArrayInputStream(bytes);
        }

        @Override
        public int read() throws IOException {
            int data = this.sourceStream.read();
            if (data == -1) {
                this.finished = true;
            }
            return data;
        }

        @Override
        public int available() throws IOException {
            return this.sourceStream.available();
        }

        @Override
        public void close() throws IOException {
            super.close();
            this.sourceStream.close();
        }

        @Override
        public boolean isFinished() {
            return this.finished;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {

        }
    }
}