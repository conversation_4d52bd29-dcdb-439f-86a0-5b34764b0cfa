/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.map;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.MonitorMapVehiclePositionRequestVO;
import com.jdx.rover.monitor.vo.MonitorStationVehicleMapInfoRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;

/**
 * <p>
 * This is a controller for monitor vehicle map info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorMapInfoWebJsfService {

  public HttpResult getVehicleRealtimeInfo(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult getStationAndVehicleMapInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo);

  public HttpResult getVehicleMapInfo(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult getVehicleRealtimePositionInfo(MonitorStationVehicleMapInfoRequestVO stationVehicleMapInfoRequestVo);

  public HttpResult getMetaDataPositionInfo(MonitorMapVehiclePositionRequestVO vehiclePositionRequestVo);

}