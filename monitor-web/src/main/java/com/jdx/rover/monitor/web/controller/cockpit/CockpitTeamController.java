/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller.cockpit;

import com.jdx.rover.monitor.dto.cockpit.CockpitTeamManagerStatisticDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitTransferIssueDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitPageDTO;
import com.jdx.rover.monitor.service.cockpit.CockpitTeamService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 座席团队接口
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RequestMapping(value = "/monitor/web/cockpitTeam")
@RestController
public class CockpitTeamController {

    private final CockpitTeamService cockpitTeamService;

    /**
     * 座席看板数据列表
     *
     * @param cockpitTeamNumber 座席团队编号
     * @return HttpResult
     */
    @GetMapping(value = "/getCockpitIssueStatistic")
    public MultiCockpitPageDTO getCockpitIssueStatistic(@Valid @RequestParam(value = "cockpitTeamNumber") String cockpitTeamNumber) {
        return cockpitTeamService.getMultiCockpitRealInfo(cockpitTeamNumber);
    }

    /**
     * 可转单座席列表
     *
     * @param cockpitTeamNumber 座席团队编号
     * @return HttpResult
     */
    @GetMapping(value = "/getAllowIssueCockpitList")
    public List<CockpitTransferIssueDTO> getAllowIssueCockpitList(@Valid @RequestParam(value = "cockpitTeamNumber") String cockpitTeamNumber, @RequestParam(value = "vehicleName", required = false) String vehicleName) {
        return cockpitTeamService.getAllowIssueCockpitList(cockpitTeamNumber, vehicleName);
    }

    /**
     * 座席看板团队管理员信息
     *
     * @param cockpitTeamNumber 座席团队编号
     * @return HttpResult
     */
    @GetMapping(value = "/getTeamManagerStatistic")
    public CockpitTeamManagerStatisticDTO getTeamManagerStatistic(@Valid @RequestParam(value = "cockpitTeamNumber") String cockpitTeamNumber) {
        return cockpitTeamService.getTeamManagerStatistic(cockpitTeamNumber);
    }

}