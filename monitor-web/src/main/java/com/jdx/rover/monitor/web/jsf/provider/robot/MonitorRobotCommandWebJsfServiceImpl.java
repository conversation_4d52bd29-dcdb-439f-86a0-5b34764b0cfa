/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.robot;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.service.robot.RobotCommandService;
import com.jdx.rover.monitor.vo.MonitorRemoteCommandVO;
import com.jdx.rover.monitor.vo.robot.RobotCommandBaseVO;
import com.jdx.rover.monitor.web.jsf.api.robot.MonitorRobotCommandJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 监控机器人指令服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
public class MonitorRobotCommandWebJsfServiceImpl extends AbstractProvider<MonitorRobotCommandJsfService> implements MonitorRobotCommandJsfService {

    /**
     * 提供机器人指令服务的实例。
     */
    private final RobotCommandService commandService;

    @Override
    @ServiceInfo(name = "视同到达", webUrl = "/monitor/web/robot/command/asArrived")
    public HttpResult<String> asArrived(RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.sendAsArrivedCommand(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_FORCE_ARRIVED);
    }

    @Override
    @ServiceInfo(name = "急停接管", webUrl = "/monitor/web/robot/command/emergency_stop")
    public HttpResult<String> emergencyStop(RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.emergencyStop(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }

    @Override
    @ServiceInfo(name = "释放接管", webUrl = "/monitor/web/robot/command/recovery")
    public HttpResult<String> recovery(RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.recovery(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }

    @Override
    @ServiceInfo(name = "远程重启", webUrl = "/monitor/web/robot/command/restart")
    public HttpResult<String> restart(RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.restart(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }

    @Override
    @ServiceInfo(name = "断电重启", webUrl = "/monitor/web/robot/command/reboot")
    public HttpResult<String> reboot(RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.reboot(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }
}
