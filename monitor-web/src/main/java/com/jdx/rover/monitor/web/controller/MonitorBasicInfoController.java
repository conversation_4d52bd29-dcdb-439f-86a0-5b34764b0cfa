/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorUserCityBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.service.web.MonitorBasicInfoService;
import com.jdx.rover.monitor.vo.CockpitVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserMetaDataInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserVehicleBasicInfoRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * This is a controller class for basic info.
 * </p>
 * 
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe when
 * properties parameters passed to it are used by the caller in thread safe
 * manner.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/monitor/web/basic")
public class MonitorBasicInfoController {

  /**
   * <p>
   * The supervisor basic info service.
   * </p>
   */
  @Autowired
  private MonitorBasicInfoService monitorBasicInfoService;

  /**
   * <p>
   * This method helps to return user's permissions.
   * </p>
   */
  @PostMapping("/user_vehicle")
  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getUserVehicleBasicInfo(@RequestBody MonitorUserVehicleBasicInfoRequestVO basicInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getUserVehicleBasicInfo(basicInfoRequestVo));
  }

  /**
   * <p>
   * 获取驾舱车辆左侧树
   * </p>
   */
  @PostMapping("/getCockpitVehicleTree")
  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getCockpitVehicleTree(@RequestBody CockpitVehicleBasicInfoRequestVO basicInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getCockpitVehicleStationTree(basicInfoRequestVo));
  }
  
  /**
   * <p>
   * This method helps to return user's permissions.
   * </p>
   */
  @GetMapping("/user_vehicle_type/{type}")
  public HttpResult getUserVehicleTypeSize(@PathVariable(value= "type") String type) {
    return HttpResult.success(monitorBasicInfoService.getUserVehicleTypeSize(type));
  }

  /**
   * <p>
   * This method helps to return vehicle basic info.
   * </p>
   * 
   */
  @GetMapping("/vehicle/{vehicleName}")
  public HttpResult<MonitorVehiceBasicInfoDTO> getVehicleBasicInfoByIdentity(@PathVariable(value = "vehicleName") String vehicleName) {
    return HttpResult.success(monitorBasicInfoService.getVehicleBasicInfo(vehicleName));
  }

  /**
   * <p>
   * This method helps to return user city station vehicle basic info.
   * </p>
   * 
   */
  @GetMapping("/user_metadata/{dataType}")
  public HttpResult getUserMetaDataByIdentity(@PathVariable(value = "dataType") String dataType) {
    return HttpResult.success(monitorBasicInfoService.getUserMetaData(dataType, UserUtils.getAndCheckLoginUser()));
  }

  /**
   * <p>
   * This method helps to return user metadata list info.
   * </p>
   *
   */
  @PostMapping("/user_metadata_list/{dataType}")
  public HttpResult getUserMetaDataList(@PathVariable(value = "dataType") String dataType, @RequestBody MonitorUserMetaDataInfoRequestVO metaDataInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getUserMetaDataList(dataType, metaDataInfoRequestVo));
  }

}
