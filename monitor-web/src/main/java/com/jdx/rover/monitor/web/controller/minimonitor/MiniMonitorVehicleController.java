/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MiniMonitorVehicleService;
import com.jdx.rover.monitor.vo.MiniMonitorVehicleStateRequestVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for mini monitor vehicle info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/vehicle")
public class MiniMonitorVehicleController {

  @Autowired
  private MiniMonitorVehicleService miniMonitorVehicleService;

  /**
   * <p>
   * This method helps to return vehicle list.
   * </p>
   *
   * @param vehicleStateRequestVo the mini monitor request VO.
   * @return mini monitor vehicle DTO list.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @PostMapping("/vehicle_list")
  public HttpResult getVehicleList(@Valid @RequestBody MiniMonitorVehicleStateRequestVO vehicleStateRequestVo) {
    ParameterCheckUtility.checkNotNull(vehicleStateRequestVo, "vehicleStateRequestVo");
    ParameterCheckUtility.checkNotNull(vehicleStateRequestVo.getStationId(), "vehicleStateRequestVo#stationId");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleStateRequestVo.getVehicleBusinessType(), "miniMonitorVehicleStateRequestVO#businessType");
    return miniMonitorVehicleService.getVehicleList(vehicleStateRequestVo);
  }

  /**
   * <p>
   * This method helps to return vehicle detail.
   * </p>
   *
   * @param vehicleName the mini monitor request vehicleName.
   * @throws IllegalArgumentException if the argument doesn't meet the requirement.
   */
  @GetMapping("/vehicle_state/{vehicleName}")
  public HttpResult getVehicleDetailInfo(@PathVariable(value = "vehicleName") String vehicleName, @RequestParam(value = "errorFlag", required = false, defaultValue = "false") Boolean errorFlag) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    return miniMonitorVehicleService.getVehicleDetailInfo(vehicleName, errorFlag);
  }

  /**
   * <p>
   * 获取车辆实时状态
   * </p>
   *
   */
  @GetMapping("/vehicleState/{stateType}/{vehicleName}")
  public HttpResult getVehicleStateInfoByType(@PathVariable(value = "vehicleName") String vehicleName, @PathVariable(value = "stateType") String stateType) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(stateType, "stateType");
    return miniMonitorVehicleService.getVehicleStateInfoByType(vehicleName, stateType);
  }

  /**
   * 添加车辆搜索记录
   * @param vehicleName
   * @return
   */
  @PostMapping("/add_search_record")
  public HttpResult addSearchRecord(@RequestParam(value = "vehicleName") String vehicleName) {
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(vehicleName, "vehicleName");
    return miniMonitorVehicleService.addSearchRecord(vehicleName);
  }

  /**
   * 获取车辆搜索记录
   * @return
   */
  @GetMapping("/get_search_record")
  public HttpResult getSearchRecord() {
    return miniMonitorVehicleService.getSearchRecord();
  }
}

