/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.location;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorLocationCloudMapDTO;
import com.jdx.rover.monitor.service.web.MonitorLocationService;
import com.jdx.rover.monitor.vo.MonitorGetCloudMapVO;
import com.jdx.rover.monitor.vo.MonitorLocationCloudMapVO;
import com.jdx.rover.monitor.web.jsf.api.location.MonitorLocationWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 车辆人工定位服务
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
public class MonitorLocationWebJsfServiceImpl extends AbstractProvider<MonitorLocationWebJsfService> implements MonitorLocationWebJsfService{

  private final MonitorLocationService locationService;

  @ServiceInfo(name = "上报点云地图以及备选点位", webUrl = "/monitor/web/location/uploadCloudMap")
  @Override
  public HttpResult<Void> uploadVehicleCloudMap(MonitorLocationCloudMapVO locationCloudMapVo) {
    return locationService.uploadVehicleMapPose(locationCloudMapVo);
  }

  @ServiceInfo(name = "获取车辆定位数据", webUrl = "/monitor/web/location/getCloudMap")
  @Override
  public HttpResult<MonitorLocationCloudMapDTO> getVehicleCloudMap(MonitorGetCloudMapVO getCloudMapVo) {
    return locationService.requestCloudMap(getCloudMapVo.getVehicleName(), getCloudMapVo.getDistance());
  }

}
