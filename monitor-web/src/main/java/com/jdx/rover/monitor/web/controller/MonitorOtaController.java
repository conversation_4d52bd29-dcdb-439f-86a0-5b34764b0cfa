/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorOtaService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控ota升级操作
 *
 * <AUTHOR>
 * @date 2024/1/24
 */
@RestController
@RequestMapping(value = "/monitor/web/ota")
@RequiredArgsConstructor
public class MonitorOtaController {

    /**
     * 监控ota升级服务
     */
    private final MonitorOtaService monitorOtaService;

    /**
     * 地图转为静默下载
     */
    @PostMapping("/mapSilentDownload/{vehicleName}")
    public HttpResult mapSilentDownload(@PathVariable(value = "vehicleName") String vehicleName) {
        return monitorOtaService.mapSilentDownload(vehicleName);
    }
}
