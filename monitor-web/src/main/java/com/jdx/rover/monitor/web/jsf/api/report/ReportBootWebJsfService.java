/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.report;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;

/**
 * 启动信息
 *
 * <AUTHOR>
 */
public interface ReportBootWebJsfService {
    /**
     * 获取启动信息
     */
    public HttpResult<VehicleBootDTO> getReportBoot(VehicleNameBasicVO vehicleNameBasicVo);
}
