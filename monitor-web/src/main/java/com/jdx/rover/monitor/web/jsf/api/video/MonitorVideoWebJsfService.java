/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.video;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.VehicleNameListVO;
import com.jdx.rover.monitor.vo.drive.MonitorHistoryVideoVO;

/**
 * 视频服务
 *
 * <AUTHOR>
 */
public interface MonitorVideoWebJsfService {

  public HttpResult<Object> getVideoAddress(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult<Object> getVideoAddressList(VehicleNameListVO vehicleNameListVo);

  public HttpResult<Object> searchHistoryVideo(MonitorHistoryVideoVO historyVideoVo);

}
