package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.drive.command.XataSwitchVehilcleModeEnum;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.MonitorXataChangeModeCommandVO;
import com.jdx.rover.monitor.web.jsf.api.xata.MonitorXataJsfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * xata接口服务
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorXataJsfServiceImpl extends AbstractProvider<MonitorXataJsfService> implements MonitorXataJsfService {

  /**
   * 提供远程命令服务的实例
   */
  private final MonitorRemoteCommandService remoteCommandService;

  /**
   * 用于获取和操作车辆调度信息的仓库接口。
   */
  private final VehicleScheduleRepository vehicleScheduleRepository;

  /**
   * <p>
   * 切换车辆模式
   * </p>
   */
  @Override
  @ServiceInfo(name = "切换车辆模式", webUrl = "/monitor/web/xata/changeVehicleMode")
  public HttpResult<MonitorRemoteCommandDTO> changeVehicleMode(MonitorXataChangeModeCommandVO xataChangeModeCommandVo) {
    ParameterCheckUtility.checkNotNull(xataChangeModeCommandVo, "xataChangeModeCommandVo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(xataChangeModeCommandVo.getVehicleName(),
            "xataChangeModeCommandVo#vehicleName");
    log.info("收到xata切换车辆模式{}", JsonUtils.writeValueAsString(xataChangeModeCommandVo));
    MonitorScheduleEntity monitorScheduleEntity = vehicleScheduleRepository.get(xataChangeModeCommandVo.getVehicleName());
    if (Objects.nonNull(monitorScheduleEntity)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_SCHEDULE_STATE.getCode(), MonitorErrorEnum.ERROR_SCHEDULE_STATE.getMessage());
    }
    if(!StringUtils.equalsAny(xataChangeModeCommandVo.getVehicleMode(), XataSwitchVehilcleModeEnum.RUN_HAVE_MAP.getValue(),XataSwitchVehilcleModeEnum.RUN_CALIBRATION.getValue())) {
      return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode(), MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getMessage());
    }
    return remoteCommandService.postChangeVehicleMode(xataChangeModeCommandVo);
  }
}
