/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorVehicleRealtimeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * This is a api interface for monitor data info under user.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/realtime")
@RestController
public class MonitorVehicleRealtimeInfoController {

  @Autowired
  private MonitorVehicleRealtimeInfoService vehicleRealtimeInfoService;

  /**
   * <p>
   * Search vehicle realtime info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/navibar/vehicle/{vehicleName}")
  public HttpResult getMultiVehicleMapInfo(@PathVariable("vehicleName") String vehicleName) {
    return vehicleRealtimeInfoService.getMultiVehicleRealtimeInfo(vehicleName);
  }

  /**
   * <p>
   * Search vehicle realtime info by search entity.
   * </p>
   *
   * @param vehicleName The search entity for monitor info.
   */
  @GetMapping("/mileage/finished/{vehicleName}")
  public HttpResult getVehicleScheduleMileage(@PathVariable("vehicleName") String vehicleName) {
    return vehicleRealtimeInfoService.getVehicleScheduleMileage(vehicleName);
  }

}