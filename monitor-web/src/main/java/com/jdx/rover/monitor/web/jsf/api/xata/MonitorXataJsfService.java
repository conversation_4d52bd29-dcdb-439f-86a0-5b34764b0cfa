package com.jdx.rover.monitor.web.jsf.api.xata;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.vo.MonitorXataChangeModeCommandVO;

/**
 * xata接口服务
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorXataJsfService {

    /**
     * <p>
     * 切换车辆模式
     * </p>
     */
    public HttpResult<MonitorRemoteCommandDTO> changeVehicleMode(MonitorXataChangeModeCommandVO xataChangeModeCommandVo);
}