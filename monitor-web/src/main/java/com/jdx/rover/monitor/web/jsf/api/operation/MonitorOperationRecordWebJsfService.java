/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.operation;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import com.jdx.rover.monitor.search.MonitorDataListSearch;
import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for monitor operation record.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorOperationRecordWebJsfService {

  public HttpResult<PageDTO<MonitorOperationRecordDTO>> search(@Valid MonitorDataListSearch monitorDataListSearch);

}