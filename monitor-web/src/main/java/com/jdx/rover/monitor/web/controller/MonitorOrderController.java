/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.service.web.MonitorOrderService;
import com.jdx.rover.monitor.vo.MonitorOrderRequestVO;
import com.jdx.rover.monitor.vo.MonitorVerifyCodeRequestVO;
import com.jdx.rover.monitor.vo.MonitorViewOrderRequestVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * This is a controller class for supervisor order.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value="/monitor/web/order")
public class MonitorOrderController {
  
  /**
   * <p>
   * The service for order.
   * </p>
   */
  @Autowired
  private MonitorOrderService monitorOrderService;

  /**
   * <p>
   * Search order record by search entity.
   * </p>
   * 
   * @param monitorOrderRequestVo The search entity for order record.
   * @return The correspond supervisor operation record dto data transform
   *         objects.
   * @throws IllegalArgumentException If the argument doesn't meet the
   *                                  requirement.
   */
  @PostMapping("/list")
  public HttpResult search(@Valid @RequestBody(required = true) MonitorOrderRequestVO monitorOrderRequestVo) {
    return monitorOrderService.search(monitorOrderRequestVo);
  }

  @PostMapping("/verify_code")
  public HttpResult sendVerifyCode(@Valid @RequestBody(required = true) MonitorVerifyCodeRequestVO verifyCodeVo) {
    return monitorOrderService.sendVerifyCode(verifyCodeVo);
  }

  @PostMapping("/detail")
  public HttpResult viewOrderDetail(@RequestBody(required = true) MonitorViewOrderRequestVO viewOrderVo) {
    String userName = UserUtils.getAndCheckLoginUser();
    return monitorOrderService.viewOrderInfo(userName, viewOrderVo);
  }

}
