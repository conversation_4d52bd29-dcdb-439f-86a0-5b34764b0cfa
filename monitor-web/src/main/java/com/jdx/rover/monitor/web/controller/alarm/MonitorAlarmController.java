/*
 * Copyright (c) 2021-2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.web.controller.alarm;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.enums.ManualAlarmSourceEnum;
import com.jdx.rover.monitor.service.web.MonitorAlarmService;
import com.jdx.rover.monitor.vo.MonitorManualAlarmReportVO;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控告警接口
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/monitor/web/alarm")
public class MonitorAlarmController {

  /**
   * 告警服务接口
   */
  private final MonitorAlarmService monitorAlarmService;

  /**
   * <p>
   *  巡查人工告警
   * </p>
   */
  @PostMapping("/reportManualAlarm")
  public HttpResult reportManualAlarm(@Valid @RequestBody MonitorManualAlarmReportVO manualAlarmReportVo) {
    return monitorAlarmService.reportManualAlarm(manualAlarmReportVo, ManualAlarmSourceEnum.PATROL_MONITOR);
  }

  /**
   * <p>
   *  当天巡查人工告警
   * </p>
   */
  @GetMapping("/listTodayManualAlarm")
  public HttpResult listTodayManualAlarm(@RequestParam(value = "vehicleName") String vehicleName) {
    return HttpResult.success(monitorAlarmService.listTodayManualAlarm(vehicleName));
  }




}
