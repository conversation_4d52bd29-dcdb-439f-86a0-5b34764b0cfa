package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.jsf.service.MonitorCockpitJsfService;
import com.jdx.rover.monitor.service.cockpit.CockpitVehicleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 云驾座席服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorCockpitServiceImpl extends AbstractProvider<MonitorCockpitJsfService> implements MonitorCockpitJsfService {

  /**
   * 提供云驾座席相关的车辆服务。
   */
  private final CockpitVehicleService cockpitVehicleService;

  /**
   * 获取座席状态
   */
  @Override
  public HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus() {
    List<CockpitRealStatusDTO> result = cockpitVehicleService.getAllCockpitRealStatus();
    return HttpResult.success(result);
  }
}
