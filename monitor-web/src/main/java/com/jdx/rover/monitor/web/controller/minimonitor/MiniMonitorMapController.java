/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorMetadataPositionDTO;
import com.jdx.rover.monitor.service.web.MonitorMapInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  运营端小程序地图接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/map")
public class MiniMonitorMapController {

  @Autowired
  private MonitorMapInfoService monitorMapInfoService;

  @GetMapping("/position/{metaType}/{key}")
  public HttpResult getMetaDataPositionInfo(@PathVariable(value = "metaType") String metaType, @PathVariable(value = "key") String key,
                                            @RequestParam(value = "positionType", defaultValue = "WGS84") String positionType) {
    MonitorMetadataPositionDTO metadataPositionDto = monitorMapInfoService.getMetaDataPositionInfo(metaType, key, positionType);
    return HttpResult.success(metadataPositionDto);
  }

}

