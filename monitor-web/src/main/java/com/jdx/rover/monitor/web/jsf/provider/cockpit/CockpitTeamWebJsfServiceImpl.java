/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.cockpit;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.cockpit.CockpitTeamManagerStatisticDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitTransferIssueDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitPageDTO;
import com.jdx.rover.monitor.service.cockpit.CockpitTeamService;
import com.jdx.rover.monitor.vo.CockpitTeamVehicleVO;
import com.jdx.rover.monitor.web.jsf.api.cockpit.CockpitTeamWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 座席团队接口
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class CockpitTeamWebJsfServiceImpl extends AbstractProvider<CockpitTeamWebJsfService> implements CockpitTeamWebJsfService{

    private final CockpitTeamService cockpitTeamService;

    @ServiceInfo(name = "座席看板数据列表", webUrl = "/monitor/web/cockpitTeam/getCockpitIssueStatistic")
    public HttpResult<MultiCockpitPageDTO> getCockpitIssueStatistic(CockpitTeamVehicleVO cockpitTeamVo) {
        return HttpResult.success(cockpitTeamService.getMultiCockpitRealInfo(cockpitTeamVo.getCockpitTeamNumber()));
    }

    @ServiceInfo(name = "可转单座席列表", webUrl = "/monitor/web/cockpitTeam/getAllowIssueCockpitList")
    public HttpResult<List<CockpitTransferIssueDTO>> getAllowIssueCockpitList(CockpitTeamVehicleVO cockpitTeamVo) {
        return HttpResult.success(cockpitTeamService.getAllowIssueCockpitList(cockpitTeamVo.getCockpitTeamNumber(), cockpitTeamVo.getVehicleName()));
    }

    @ServiceInfo(name = "座席看板团队管理员信息", webUrl = "/monitor/web/cockpitTeam/getTeamManagerStatistic")
    public HttpResult<CockpitTeamManagerStatisticDTO> getTeamManagerStatistic(CockpitTeamVehicleVO cockpitTeamVo) {
        return HttpResult.success(cockpitTeamService.getTeamManagerStatistic(cockpitTeamVo.getCockpitTeamNumber()));
    }

}