/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.domain.page.PageVO;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.service.web.MiniMonitorIssueService;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmAttentionAddVO;
import com.jdx.rover.monitor.vo.MiniMonitorAlarmSubmissionAddVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for mini monitor issue info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/issue")
public class MiniMonitorIssueController {

  @Autowired
  private MiniMonitorIssueService miniMonitorIssueService;

  /**
   * <p>
   * Post mini monitor alarm request.
   * </p>
   *
   * @param miniMonitorAlarmSubmissionAddVo The alarm request view object.
   * @return The remote request response dto.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  @PostMapping("/alarm_report")
  public HttpResult reportAlarm(@Valid @RequestBody MiniMonitorAlarmSubmissionAddVO miniMonitorAlarmSubmissionAddVo) {
    ParameterCheckUtility.checkNotNull(miniMonitorAlarmSubmissionAddVo,
            "miniMonitorAlarmSubmissionAddVo");
    ParameterCheckUtility.checkNotNullNorEmpty(miniMonitorAlarmSubmissionAddVo.getVehicleName(),
            "miniMonitorAlarmSubmissionAddVo#vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(miniMonitorAlarmSubmissionAddVo.getTitle(),
            "miniMonitorAlarmSubmissionAddVo#title");
    ParameterCheckUtility.checkNotNullNorEmpty(miniMonitorAlarmSubmissionAddVo.getMessage(),
            "miniMonitorAlarmSubmissionAddVo#message");
    return miniMonitorIssueService.reportAlarm(miniMonitorAlarmSubmissionAddVo);
  }

  /**
   * <p>
   * Post mini monitor alarm attention request.
   * </p>
   *
   * @param miniMonitorAlarmAttentionAddVo The alarm attention request view object.
   * @return The remote request response dto.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  @PostMapping("/alarm_attention")
  public HttpResult reportAlarmAttention(@Valid @RequestBody MiniMonitorAlarmAttentionAddVO miniMonitorAlarmAttentionAddVo) {
    ParameterCheckUtility.checkNotNull(miniMonitorAlarmAttentionAddVo,
            "miniMonitorAlarmAttentionAddVo");
    ParameterCheckUtility.checkNotNullNorEmpty(miniMonitorAlarmAttentionAddVo.getVehicleName(),
            "miniMonitorAlarmAttentionAddVo#vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(miniMonitorAlarmAttentionAddVo.getAlarmEventType(),
            "miniMonitorAlarmAttentionAddVo#alarmEventType");
    ParameterCheckUtility.checkNotNull(miniMonitorAlarmAttentionAddVo.getStartTimestamp(),
            "miniMonitorAlarmAttentionAddVo#startTimestamp");
    return miniMonitorIssueService.reportAlarmAttention(miniMonitorAlarmAttentionAddVo);
  }

  /**
   * <p>
   * Get mini monitor issue process.
   * </p>
   *
   * @param issueRecordSearch The attachement request view object.
   * @return The remote request response dto.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  @GetMapping("/issue_process")
  public HttpResult getByIssueNo(IssueRecordSearch issueRecordSearch) {
    ParameterCheckUtility.checkNotNull(issueRecordSearch, "issueRecordSearch");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(issueRecordSearch.getIssueNo(),
            "issueRecordSearch#issueNo");
    ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(issueRecordSearch.getVehicleName(),
            "issueRecordSearch#vehicleName");
    return miniMonitorIssueService.getByIssueNo(issueRecordSearch);
  }

  /**
   * <p>
   * Get mini monitor issue list.
   * </p>
   *
   * @param issueRecordSearch The issue request view object.
   * @return The remote request response dto.
   * @throws IllegalArgumentException If the argument does not meet the requirement.
   */
  @GetMapping("/issue_list")
  public HttpResult getList(PageVO pageableVo, IssueRecordSearch issueRecordSearch) {
    return miniMonitorIssueService.search(pageableVo, issueRecordSearch);
  }

}

