///***************************************************************************
// *
// * Copyright (c) 2022 www.jd.com, Inc. All Rights Reserved
// *
// **************************************************************************/
//package com.jdx.rover.monitor.web.controller;
//
//import com.jdx.rover.common.domain.page.PageDTO;
//import com.jdx.rover.common.utils.result.HttpResult;
//import com.jdx.rover.monitor.dto.accident.AccidentInfoDTO;
//import com.jdx.rover.monitor.service.web.MonitorAccidentService;
//import com.jdx.rover.monitor.vo.accident.MonitorAccidentAddVO;
//import com.jdx.rover.monitor.vo.accident.MonitorAccidentSearchVO;
//import com.jdx.rover.monitor.vo.accident.MonitorAccidentUpdateVO;
//import jakarta.validation.Valid;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <p>
// * 事故相关 Controller
// * </p>
// *
// * <AUTHOR>
// * @date 2023/06/15
// */
//@RestController
//@RequestMapping(value = "/monitor/web/accident")
//public class MonitorAccidentController {
//
//    /**
//     * 事故功能 Service
//     */
//    @Autowired
//    private MonitorAccidentService monitorAccidentService;
//
//    /**
//     * <p>
//     * 事故提报
//     * </p>
//     */
//    @PostMapping("/report")
//    public HttpResult<Void> addAccident(@RequestBody @Valid MonitorAccidentAddVO monitorAccidentAddVO){
//        return HttpResult.success(monitorAccidentService.addAccident(monitorAccidentAddVO));
//    }
//
//    /**
//     * <p>
//     * 事故更新
//     * </p>
//     */
//    @PostMapping("/update")
//    public HttpResult<Void> updateAccident(@RequestBody @Valid MonitorAccidentUpdateVO monitorAccidentUpdateVO){
//        return HttpResult.success(monitorAccidentService.updateAccident(monitorAccidentUpdateVO));
//    }
//
//    /**
//     * <p>
//     * 分页查询事故信息
//     * </p>
//     */
//    @GetMapping("/listByPage")
//    public HttpResult<PageDTO<AccidentInfoDTO>> listAccidentByPage(MonitorAccidentSearchVO searchVO){
//        return HttpResult.success(monitorAccidentService.listAccident(searchVO));
//    }
//
//    /**
//     * <p>
//     * 分页查询事故信息
//     * </p>
//     */
//    @PostMapping("/list")
//    public HttpResult<PageDTO<AccidentInfoDTO>> listAccident(@RequestBody MonitorAccidentSearchVO searchVO){
//        return HttpResult.success(monitorAccidentService.listAccident(searchVO));
//    }
//
//    /**
//     * <p>
//     * 根据事故编号获取事故信息
//     * </p>
//     */
//    @PostMapping("/getByAccidentNo")
//    public HttpResult<AccidentInfoDTO> getByAccidentNo(@RequestParam(value = "accidentNo") String accidentNo){
//        return HttpResult.success(monitorAccidentService.getByAccidentNo(accidentNo));
//    }
//}
