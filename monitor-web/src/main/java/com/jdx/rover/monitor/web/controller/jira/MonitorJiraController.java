/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.jira;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.vehicle.VehicleRealtimeJsfDTO;
import com.jdx.rover.monitor.dto.jira.JiraAlarmListDTO;
import com.jdx.rover.monitor.service.jira.MonitorJiraService;
import com.jdx.rover.monitor.vo.BugUserDeleteVO;
import com.jdx.rover.monitor.vo.BugUserVO;
import com.jdx.rover.monitor.vo.BugWithAttachmentAddVO;
import com.jdx.rover.monitor.vo.SemiJiraAddVO;
import com.jdx.rover.monitor.vo.VehicleRealtimeVO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * This is a controller class for jira problem reporting.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(value = "/monitor/web/jira")
public class MonitorJiraController {

  /**
   * <p>
   * The jira reporting service.
   * </p>
   */
  @Autowired
  private MonitorJiraService monitorJiraService;

  @PostMapping("/report")
  public HttpResult reportJira(@Valid BugWithAttachmentAddVO bugAddVO) {
    ParameterCheckUtility.checkNotNull(bugAddVO, "bugAddVO");
    return monitorJiraService.reportJiraWithAttachment(bugAddVO);
  }

  /**
   * 用户群入京ME群消息推送
   * @param servletRequest
   */
  @PostMapping("/user_join_group")
  public int userJoinGroup(HttpServletRequest servletRequest) {
    try {
        Map<String, Object> paramMap = new ObjectMapper().readValue(servletRequest.getInputStream(), Map.class);
        log.info("用户群入京ME群消息推送消息：{}", paramMap);
        monitorJiraService.sendJdmeCardMessage(paramMap);
    } catch (Exception e) {
      log.info("用户进群处理异常：", e);
    }
    return 200;
  }

  /**
   * <p>
   * Report jira.
   * </p>
   *
   */
  @PostMapping("/semi-report")
  public HttpResult semiReportJira(@Valid @RequestBody SemiJiraAddVO jiraAddVo) {
    ParameterCheckUtility.checkNotNull(jiraAddVo, "jiraAddVo");
    return monitorJiraService.semiReportJira(jiraAddVo);
  }

  @GetMapping("/getIssueAlarmList")
  public JiraAlarmListDTO getAvailableReportAlarmList(@RequestParam(value = "vehicleName") String vehicleName) {
    return monitorJiraService.getAvailableReportAlarmList(vehicleName);
  }

  @GetMapping("/get_rover_list")
  public HttpResult<List<String>> getRoverList() {
    return monitorJiraService.getRoverList();
  }

  /**
   * 添加人员映射关系
   * @param bugUserVO
   * @return
   */
  @PostMapping("/add_user_mapping")
  public HttpResult addUserMapping(@RequestBody BugUserVO bugUserVO) {
    return monitorJiraService.addUserMapping(bugUserVO);
  }

  /**
   * 删除人员映射关系
   * @param bugUserDeleteVO
   * @return
   */
  @PostMapping("/delete_user_mapping")
  public HttpResult deleteUserMapping(@RequestBody BugUserDeleteVO bugUserDeleteVO) {
    return monitorJiraService.deleteUserMapping(bugUserDeleteVO);
  }

  /**
   * 获取车辆历史实时信息
   * @param vehicleRealtimeVO vehicleRealtimeVO
   */
  @PostMapping("/get_vehicle_realtime_info")
  public VehicleRealtimeJsfDTO getVehicleRealtimeInfo(@RequestBody VehicleRealtimeVO vehicleRealtimeVO) {
    return monitorJiraService.getVehicleRealtimeInfo(vehicleRealtimeVO);
  }
}
