package com.jdx.rover.monitor.web.command;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;


/**
 * 静态文件加载
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class MonitorStaticFileLoadCommand implements ApplicationRunner {

  @Override
  public void run(ApplicationArguments args) throws Exception {
    if (isLocalEnv()) {
      return;
    }
    String filePath = "/natives/liboctree.so";
    InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
    if (inputStream == null) {
      throw new RuntimeException("资源文件未找到");
    }
    String targetPath = "/export/package/liboctree.so";
    try {
      FileUtils.copyInputStreamToFile(inputStream, new File(targetPath));
      System.load(targetPath);
    } catch (Exception e) {
    } finally {
      IOUtils.closeQuietly(inputStream);
    }
  }

  private boolean isLocalEnv() {
    String osName = System.getProperty("os.name");
    return StringUtils.isNotBlank(osName) && (osName.startsWith("Mac") || osName.startsWith("Windows"));
  }


}
