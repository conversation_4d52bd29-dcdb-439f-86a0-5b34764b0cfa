package com.jdx.rover.monitor.web.controller.accident;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.monitor.dto.accident.AccidentBasicInfoDTO;
import com.jdx.rover.monitor.dto.accident.AccidentTagDTO;
import com.jdx.rover.monitor.dto.accident.MonitorAccidentDetailDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.web.MonitorAccidentService;
import com.jdx.rover.monitor.dto.accident.GetAccidentPageListDTO;
import com.jdx.rover.monitor.vo.accident.AccidentExportDataVO;
import com.jdx.rover.monitor.vo.accident.GetAccidentPageListVO;
import com.jdx.rover.monitor.vo.accident.ManualCreateAccidentVO;
import com.jdx.rover.monitor.vo.accident.SafetyGroupEditAccidentVO;
import com.jdx.rover.monitor.vo.accident.TechnicalSupportEditAccidentVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 监控事故接口
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/monitor/web/accident")
public class MonitorAccidentController {

    @Autowired
    private MonitorAccidentService monitorAccidentService;

    /**
     * 分页获取事故列表
     * @param getAccidentPageListVO getAccidentPageListVO
     * @return PageDTO<GetAccidentPageListDTO>
     */
    @PostMapping("/get_accident_page_list")
    public PageDTO<GetAccidentPageListDTO> getAccidentPageList(@Valid @RequestBody GetAccidentPageListVO getAccidentPageListVO) {
        return monitorAccidentService.getAccidentPageList(getAccidentPageListVO);
    }

    /**
     * 获取事故基础信息
     * @param accidentNo accidentNo
     * @return AccidentBasicInfoDTO
     */
    @GetMapping("/get_accident_basic_info")
    public AccidentBasicInfoDTO getAccidentBasicInfo(@RequestParam(value = "accidentNo") String accidentNo) {
        return monitorAccidentService.getAccidentBasicInfo(accidentNo);
    }

    /**
     * 获取事故详情
     * @param accidentNo accidentNo
     * @return MonitorAccidentDetailDTO
     */
    @GetMapping("/get_accident_detail")
    public MonitorAccidentDetailDTO getAccidentDetail(@RequestParam(value = "accidentNo") String accidentNo) {
        return monitorAccidentService.getAccidentDetail(accidentNo);
    }

    /**
     * 技术支持编辑事故
     * @param technicalSupportEditAccidentVO technicalSupportEditAccidentVO
     * @return MonitorErrorEnum
     */
    @PostMapping("/technical_support_edit_accident")
    public MonitorErrorEnum technicalSupportEditAccident(@Valid @RequestBody TechnicalSupportEditAccidentVO technicalSupportEditAccidentVO) {
        return monitorAccidentService.technicalSupportEditAccident(technicalSupportEditAccidentVO);
    }

    /**
     * 安全组编辑事故
     * @param safetyGroupEditAccidentVO safetyGroupEditAccidentVO
     * @return MonitorErrorEnum
     */
    @PostMapping("/safety_group_edit_accident")
    public MonitorErrorEnum safetyGroupEditAccident(@Valid @RequestBody SafetyGroupEditAccidentVO safetyGroupEditAccidentVO) {
        return monitorAccidentService.safetyGroupEditAccident(safetyGroupEditAccidentVO);
    }

    /**
     * 手动创建事故
     * @param manualCreateAccidentVO manualCreateAccidentVO
     * @return MonitorErrorEnum
     */
    @PostMapping("/manual_create_accident")
    public MonitorErrorEnum manualCreateAccident(@Valid @RequestBody ManualCreateAccidentVO manualCreateAccidentVO) {
        return monitorAccidentService.manualCreateAccident(manualCreateAccidentVO);
    }

    /**
     * 获取事故标签&模块
     * @return
     */
    @GetMapping("/get_tag_list")
    public List<AccidentTagDTO> getTagList() {
        return monitorAccidentService.getTagList();
    }

    /**
     * 导出指定时间事故数据
     * @param accidentExportDataVO accidentExportDataVO
     * @param response response
     */
    @PostMapping("/export_accident_data")
    public void exportAccidentData(@Valid @RequestBody AccidentExportDataVO accidentExportDataVO, HttpServletResponse response) throws IOException {
        monitorAccidentService.exportAccidentData(accidentExportDataVO, response);
    }
}
