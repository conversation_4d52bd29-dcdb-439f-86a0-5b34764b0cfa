/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.EnumDTO;
import com.jdx.rover.monitor.dto.MonitorDataCategoryDTO;
import com.jdx.rover.monitor.service.enums.EnumService;
import com.jdx.rover.monitor.vo.CommonDrownListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 通用枚举请求
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/monitor/web/enum")
public class EnumController {
  @Autowired
  private EnumService enumService;

  /**
   * 获取下拉列表数据
   */
  @PostMapping(value = "/get_enum_list_map")
  public HttpResult<Map<String, List<Map<String, Object>>>> getEnumListMap(@Valid @RequestBody CommonDrownListVO commonDrownListVO) {
    Map<String, List<Map<String, Object>>> result = enumService.getEnumListMap(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }

  /**
   * 获取多级分类枚举
   */
  @PostMapping(value = "/get_category_list")
  public HttpResult<Map<String,List<MonitorDataCategoryDTO>>> getMultiCategoryList(@Valid @RequestBody CommonDrownListVO commonDrownListVO) {
    Map<String, List<MonitorDataCategoryDTO>> result = enumService.getMultiCategoryList(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }

  /**
   * 获取所属团队下拉列表
   *
   * @return List<EnumDTO>
   */
  @GetMapping(value = "/get_cockpit_team_list")
  public List<EnumDTO> getCockpitTeamList() {
    return enumService.getCockpitTeamList();
  }

  /**
   * 获取工单受理人下拉列表
   *
   * @return List<EnumDTO>
   */
  @GetMapping(value = "/get_user_list")
  public List<EnumDTO> getUserList() {
    return enumService.getUserList();
  }

  /**
   * 获取工单受理坐席下拉列表
   *
   * @return List<EnumDTO>
   */
  @GetMapping(value = "/get_cockpit_list")
  public List<EnumDTO> getCockpitList() {
    return enumService.getCockpitList();
  }


  /**
   * 获取下拉列表数据
   */
  @PostMapping(value = "/get_pda_enum_list_map")
  public HttpResult<Map<String, List<Map<String, Object>>>> getPdaEnumListMap(@Valid @RequestBody CommonDrownListVO commonDrownListVO) {
    Map<String, List<Map<String, Object>>> result = enumService.getPdaEnumListMap(commonDrownListVO.getKeyList());
    return HttpResult.success(result);
  }
}