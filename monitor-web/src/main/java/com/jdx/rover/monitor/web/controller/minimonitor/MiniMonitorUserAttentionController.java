package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MiniMonitorUserAttentionService;
import com.jdx.rover.monitor.vo.MiniMonitorAttentionVO;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * <p>
 * 用户关注接口
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/minimonitor/web/attention")
public class MiniMonitorUserAttentionController {

    @Resource
    private MiniMonitorUserAttentionService userAttentionService;

    /**
     * 关注事件
     * @param miniMonitorAttentionVo
     */
    @PostMapping("/operate/{type}/{event}")
    public HttpResult addAttentionEvent(@PathVariable(value = "type") String type,  @PathVariable(value = "event") String event, @Valid @RequestBody MiniMonitorAttentionVO miniMonitorAttentionVo) {
        return userAttentionService.operateAttentionEvent(type, event, miniMonitorAttentionVo);
    }

    /**
     * 获取用户关注列表
     * @return
     */
    @GetMapping("/list")
    public HttpResult listUserAttentionEvent() {
        return userAttentionService.listUserAttention();
    }

}
