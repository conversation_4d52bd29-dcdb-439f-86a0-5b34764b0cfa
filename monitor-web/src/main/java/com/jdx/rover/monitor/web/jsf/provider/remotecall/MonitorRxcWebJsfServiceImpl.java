package com.jdx.rover.monitor.web.jsf.provider.remotecall;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.metadata.domain.dto.word.WordMonitorListDto;
import com.jdx.rover.monitor.service.web.MonitorRxcService;
import com.jdx.rover.monitor.vo.MonitorWordAddVO;
import com.jdx.rover.monitor.vo.MonitorWordDeleteVO;
import com.jdx.rover.monitor.web.jsf.api.remotecall.MonitorRxcWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 远程喊话数据接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorRxcWebJsfServiceImpl extends AbstractProvider<MonitorRxcWebJsfService> implements MonitorRxcWebJsfService {

    private final MonitorRxcService monitorRxcService;

    @ServiceInfo(name = "获取列表", webUrl = "/monitor/web/rxc/word_list")
    public HttpResult<List<WordMonitorListDto>> getWordList() {
        return monitorRxcService.getWordList();
    }

    @ServiceInfo(name = "新增", webUrl = "/monitor/web/rxc/add")
    public HttpResult<Object> addWord(MonitorWordAddVO monitorWordAddVO) {
        ParameterCheckUtility.checkNotNull(monitorWordAddVO, "monitorWordAddVO");
        ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(monitorWordAddVO.getContent(), "monitorWordAddVO#content");
        return monitorRxcService.addWord(monitorWordAddVO);
    }

    @ServiceInfo(name = "删除", webUrl = "/monitor/web/rxc/delete")
    public HttpResult<Object> deleteWord(MonitorWordDeleteVO monitorWordDeleteVO) {
        ParameterCheckUtility.checkNotNull(monitorWordDeleteVO, "monitorWordDeleteVO");
        ParameterCheckUtility.checkNotNull(monitorWordDeleteVO.getId(), "monitorWordDeleteVO#id");
        return monitorRxcService.deleteWord(monitorWordDeleteVO);
    }
}
