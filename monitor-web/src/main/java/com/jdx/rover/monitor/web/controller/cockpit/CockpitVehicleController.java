/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.controller.cockpit;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitSingleVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitStationVehicleListDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitVehicleDTO;
import com.jdx.rover.monitor.service.cockpit.CockpitVehicleService;
import com.jdx.rover.monitor.vo.cockpit.BindVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitSingleVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehiclePageVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehicleSearchVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 座席车辆
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/monitor/web/cockpit")
public class CockpitVehicleController {

    private final CockpitVehicleService cockpitVehicleService;

    /**
     * 获取车辆列表
     */
    @GetMapping("/getVehicleList")
    public HttpResult<PageDTO<CockpitVehicleDTO>> getVehicleList(@Valid CockpitVehiclePageVO cockpitVehiclePageVO) {
        PageDTO<CockpitVehicleDTO> pageDTO = cockpitVehicleService.getVehicleList(cockpitVehiclePageVO.getCockpitNumber(), cockpitVehiclePageVO.getPageNum(), cockpitVehiclePageVO.getPageSize());
        return HttpResult.success(pageDTO);
    }

    /**
     * 搜索车辆列表
     */
    @GetMapping("/searchVehicleList")
    public HttpResult<List<CockpitVehicleDTO>> searchVehicleList(@Valid CockpitVehicleSearchVO cockpitVehicleSearchVO) {
        List<CockpitVehicleDTO> result = cockpitVehicleService.searchVehicleList(cockpitVehicleSearchVO);
        return HttpResult.success(result);
    }

    /**
     * 座席绑定/解绑车辆
     *
     * @param bindVehicleVO bindVehicleVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/bindVehicle")
    public HttpResult<Void> bindVehicle(@Valid @RequestBody BindVehicleVO bindVehicleVO) {
        return cockpitVehicleService.bindVehicle(bindVehicleVO);
    }

    /**
     * 获取站点车辆列表
     */
    @GetMapping("/getStationVehicleList")
    public HttpResult<List<CockpitStationVehicleListDTO>> getStationVehicleList(@RequestParam(value = "cockpitNumber") String cockpitNumber) {
        List<CockpitStationVehicleListDTO> result = cockpitVehicleService.getStationVehicleList(cockpitNumber);
        return HttpResult.success(result);
    }

    /**
     * 获取座席状态
     */
    @GetMapping("/getAllCockpitRealStatus")
    public HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus() {
        List<CockpitRealStatusDTO> result = cockpitVehicleService.getAllCockpitRealStatus();
        return HttpResult.success(result);
    }

    /**
     * 获取坐席单车页信息
     */
    @GetMapping("/getSingleVehicle")
    public HttpResult<CockpitSingleVehicleDTO> getSingleVehicle(@Valid CockpitSingleVehicleVO cockpitSingleVehicleVO) {
        CockpitSingleVehicleDTO singleVehicle = cockpitVehicleService.getSingleVehicle(cockpitSingleVehicleVO);
        return HttpResult.success(singleVehicle);
    }
}