/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller.manual;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.device.jsfapi.domain.enums.EnableEnum;
import com.jdx.rover.metadata.domain.dto.warehouse.WarehouseRobotDetailInfoDTO;
import com.jdx.rover.metadata.domain.vo.warehouse.WarehouseRobotDetailInfoGetPageVO;
import com.jdx.rover.metadata.jsf.service.warehouse.WarehouseBusinessJsfService;
import com.jdx.rover.monitor.dto.MonitorRemoteCommandDTO;
import com.jdx.rover.monitor.dto.robot.RobotGroupMapInfoDTO;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.enums.RemoteCommandSourceEnum;
import com.jdx.rover.monitor.enums.device.DeviceCommandTaskEnum;
import com.jdx.rover.monitor.enums.device.DeviceWorkModeEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.drawer.DrawerService;
import com.jdx.rover.monitor.service.init.InitCacheService;
import com.jdx.rover.monitor.service.obu.VehicleDrivingLaneService;
import com.jdx.rover.monitor.service.robot.RobotCommandService;
import com.jdx.rover.monitor.service.robot.RobotMapService;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import com.jdx.rover.monitor.vo.robot.RobotCommandBaseVO;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;
import com.jdx.rover.server.api.domain.dto.obu.VehicleDriverDataDTO;
import com.jdx.rover.server.api.domain.vo.RemoteCommandVO;
import com.jdx.rover.server.api.jsf.service.command.RemoteCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 通用枚举请求
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/monitor/web/manual")
@Slf4j
public class ManualController {
    private final DrawerService drawerService;

    private final InitCacheService initCacheService;

    private final VehicleDrivingLaneService drivingLaneService;

    private final MonitorRemoteCommandService monitorRemoteCommandService;

    private final VehicleTakeOverRepository vehicleTakeOverRepository;

    private final RemoteCommandService remoteCommandJsfService;

    private final DuccConfigProperties duccConfigProperties;

    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    private final WarehouseBusinessJsfService warehouseBusinessJsfService;

    private final RobotMapService robotMapService;

    private final RobotCommandService commandService;

    /**
     * 手工推送抽屉
     */
    @GetMapping(value = "/pushAllDrawer")
    public HttpResult<String> pushAllDrawer() {
        drawerService.pushAllDrawer();
        log.info("手工执行推送抽屉完成!");
        return HttpResult.success("手工执行推送抽屉完成!");
    }

    /**
     * 手工初始化座席团队
     */
    @GetMapping(value = "/getConfig")
    public HttpResult<String> getConfig() {
        return HttpResult.success(duccConfigProperties.getMonitorDrivingLaneRegion());
    }

    /**
     * 手工初始化座席团队
     */
    @GetMapping(value = "/initAllCockpitTeam")
    public HttpResult<String> initAllCockpitTeam() {
        initCacheService.initAllCockpitTeam();
        log.info("手工初始化座席团队完成!");
        return HttpResult.success("手工初始化座席团队完成!");
    }

    /**
     * 手工初始化用户mqtt
     */
    @GetMapping(value = "/refreshUserMqtt/{userName}")
    public HttpResult<String> refreshUserMqtt(@PathVariable("userName") String userName) {
        initCacheService.refreshUserMqtt(userName);
        log.info("手工初始化用户mqtt完成!");
        return HttpResult.success("手工初始化用户mqtt完成!");
    }

    /**
     * 测试行驶车道线
     */
    @PostMapping(value = "/driving/lane")
    public HttpResult<String> testVehicleDrivingLane(@RequestBody VehicleDriverDataDTO vehicleReportData) {
        log.info("车辆车道线检测{}", JsonUtils.writeValueAsString(vehicleReportData));
        drivingLaneService.handleDrivingLaneMsg(vehicleReportData);
        return HttpResult.success("车道线测试完成!");
    }

    /**
     * 运维车辆恢复指令
     */
    @GetMapping(value = "/command/recovery/{vehicleName}")
    public HttpResult<String> controlRecoveryCommand(@PathVariable("vehicleName") String vehicleName) {
        MonitorRemoteCommandDTO remoteCommandDto = new MonitorRemoteCommandDTO();
        remoteCommandDto.setEventType(WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue());
        String operationUser = UserUtils.getAndCheckLoginUser();
        VehicleTakeOverEntity vehicleTakeOverEntity = vehicleTakeOverRepository.get(vehicleName);
        log.info("用户{}执行人工解除车辆{}接管态{}", operationUser, vehicleName, JsonUtils.writeValueAsString(vehicleTakeOverEntity));
        vehicleTakeOverRepository.remove(vehicleName);
        RemoteCommandVO remoteCommandVo = new RemoteCommandVO();
        remoteCommandVo.setVehicleName(vehicleName);
        remoteCommandVo.setReceiveTimeStamp(new Date());
        remoteCommandVo.setTransitTimeStamp(new Date());
        HttpResult result = remoteCommandJsfService.publishRecoveryCommand(remoteCommandVo);
        String commandSource = Optional.ofNullable(vehicleTakeOverEntity).map(entity -> entity.getCommandSource()).orElse(RemoteCommandSourceEnum.MONITOR.getCommandSource());
        monitorRemoteCommandService.sendRemoteCommandRecordLog(JsonUtils.writeValueAsString(remoteCommandVo),
                WebsocketEventTypeEnum.REMOTE_REQUEST_RECOVERY.getValue(), Optional.ofNullable(operationUser).orElse("ROOT"), commandSource);
        if (!HttpResult.isSuccess(result)) {
            return result;
        }
        return HttpResult.success("恢复成功");
    }

    /**
     * 测试行驶车道线
     */
    @GetMapping(value = "/robot/refresh/realtime/{pageNum}")
    public HttpResult<String> refreshRobotRealtimeMode(@PathVariable("pageNum") Integer pageNum) {
        WarehouseRobotDetailInfoGetPageVO warehouseRobotDetailInfoGetPageVo = new WarehouseRobotDetailInfoGetPageVO();
        warehouseRobotDetailInfoGetPageVo.setPageNum(pageNum);
        warehouseRobotDetailInfoGetPageVo.setPageSize(100);
        HttpResult<List<WarehouseRobotDetailInfoDTO>> httpResult = warehouseBusinessJsfService.getWarehouseRobotDetailInfoPageList(warehouseRobotDetailInfoGetPageVo);
        if (HttpResult.isSuccess(httpResult)) {
            List<WarehouseRobotDetailInfoDTO> list = httpResult.getData();
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Integer, List<WarehouseRobotDetailInfoDTO>> dataMap = list.stream().collect(Collectors.groupingBy(WarehouseRobotDetailInfoDTO::getEnable));
                List<WarehouseRobotDetailInfoDTO> enableList = dataMap.get(EnableEnum.YES.getValue());
                if (CollectionUtils.isNotEmpty(enableList)) {
                    List<RobotRealtimeInfo> deviceList = enableList.stream().map(data -> {
                        RobotRealtimeInfo robotRealtimeInfo = new RobotRealtimeInfo();
                        robotRealtimeInfo.setDeviceName(data.getDeviceName());
                        robotRealtimeInfo.setWorkMode(data.getWorkMode());
                        robotRealtimeInfo.setEnable(Boolean.TRUE);
                        return robotRealtimeInfo;
                    }).collect(Collectors.toList());
                    robotRealtimeInfoManager.batchUpdateWorkMode(deviceList);
                }
                List<WarehouseRobotDetailInfoDTO> disableList = dataMap.get(EnableEnum.NO.getValue());
                if (CollectionUtils.isNotEmpty(disableList)) {
                    List<RobotRealtimeInfo> deviceList = disableList.stream().map(data -> {
                        RobotRealtimeInfo robotRealtimeInfo = new RobotRealtimeInfo();
                        robotRealtimeInfo.setDeviceName(data.getDeviceName());
                        robotRealtimeInfo.setWorkMode(DeviceWorkModeEnum.DISABLE.getWorkModeType());
                        robotRealtimeInfo.setEnable(Boolean.FALSE);
                        return robotRealtimeInfo;
                    }).collect(Collectors.toList());
                    robotRealtimeInfoManager.batchUpdateWorkMode(deviceList);
                }
                return HttpResult.success("同步数据量"+ list.size());
            }
        }
        return HttpResult.error(httpResult.getMessage());

    }

    /**
     * 测试行驶车道线
     */
    @PostMapping(value = "/robot/map/getMapByGroup")
    public HttpResult<RobotGroupMapInfoDTO> getMapByGroup(@RequestBody RobotMapInfoRequestVO mapInfoRequestVo) {
        return robotMapService.getMapInfoByGroup(mapInfoRequestVo);
    }

    @PostMapping(value = "/robot/command/emergency_stop")
    public HttpResult<String> emergencyStop(@RequestBody RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.emergencyStop(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }

    @PostMapping(value = "/robot/command/recovery")
    public HttpResult<String> recovery(@RequestBody RobotCommandBaseVO robotCommandBaseVo) {
        return commandService.recovery(robotCommandBaseVo, DeviceCommandTaskEnum.CMD_REMOTE_COMMAND);
    }

}