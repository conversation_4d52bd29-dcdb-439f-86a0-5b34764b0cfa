/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.monitor.web.jsf.provider.mapcollection;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.mapcollection.FuzzySearchVehicleDTO;
import com.jdx.rover.monitor.dto.mapcollection.GetRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapVehiclePageDTO;
import com.jdx.rover.monitor.dto.mapcollection.MarkSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MonitorTaskSearchDTO;
import com.jdx.rover.monitor.dto.mapcollection.MultiVideoUrlDTO;
import com.jdx.rover.monitor.dto.mapcollection.TaskRouteDTO;
import com.jdx.rover.monitor.dto.mapcollection.VehicleSolidifiedTaskDTO;
import com.jdx.rover.monitor.vo.mapcollection.FinishCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.AssociateTaskVO;
import com.jdx.rover.monitor.vo.mapcollection.FuzzySearchVehicleVO;
import com.jdx.rover.monitor.vo.mapcollection.GetRouteVO;
import com.jdx.rover.monitor.vo.mapcollection.MapVehiclePageVO;
import com.jdx.rover.monitor.vo.mapcollection.MonitorTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.MultiVideoUrlVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskOperateVO;
import com.jdx.rover.monitor.vo.mapcollection.SolidifiedTaskSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchCollectionVO;
import com.jdx.rover.monitor.vo.mapcollection.SwitchModeVO;
import com.jdx.rover.monitor.vo.mapcollection.TaskBaseVO;
import com.jdx.rover.monitor.web.jsf.api.mapcollection.MapCollectionCockpitJsfService;
import com.jdx.rover.monitor.service.cockpit.MapCollectionCockpitService;
import com.jdx.rover.monitor.service.mapcollection.MapTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采图座席相关接口
 */
@Service
@RequiredArgsConstructor
public class MapCollectionCockpitJsfServiceImpl extends AbstractProvider<MapCollectionCockpitJsfService> implements MapCollectionCockpitJsfService {

    /**
     * MapCollectionCockpitService
     */
    private final MapCollectionCockpitService mapCollectionCockpitService;

    /**
     * MapTaskService
     */
    private final MapTaskService mapTaskService;

    @Override
    @ServiceInfo(name = "[获取采图车辆列表]", webUrl = "/monitor/web/cockpit/map/getVehiclePage")
    public HttpResult<PageDTO<MapVehiclePageDTO>> getVehiclePage(MapVehiclePageVO mapVehiclePageVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getVehiclePage(mapVehiclePageVO));
    }

    @Override
    @ServiceInfo(name = "[采图多车页模糊搜索车牌号列表接口]", webUrl = "/monitor/web/cockpit/map/fuzzySearchVehicle")
    public HttpResult<List<FuzzySearchVehicleDTO>> fuzzySearchVehicle(FuzzySearchVehicleVO fuzzySearchVehicleVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.fuzzySearchVehicle(fuzzySearchVehicleVO));
    }

    @Override
    @ServiceInfo(name = "[车辆认领/取消认领任务]", webUrl = "/monitor/web/cockpit/map/associateTask")
    public HttpResult<Void> associateTask(AssociateTaskVO associateTaskVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.associateTask(associateTaskVO));
    }

    @Override
    @ServiceInfo(name = "[获取采图路线]", webUrl = "/monitor/web/cockpit/map/getRoute")
    public HttpResult<GetRouteDTO> getRoute(GetRouteVO getRouteVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getRoute(getRouteVO));
    }

    @Override
    @ServiceInfo(name = "[开启/关闭地图采集模式]", webUrl = "/monitor/web/cockpit/map/switchMode")
    public HttpResult<Void> switchMode(SwitchModeVO switchModeVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.switchMode(switchModeVO));
    }

    @Override
    @ServiceInfo(name = "[开始/暂停采集]", webUrl = "/monitor/web/cockpit/map/switchCollection")
    public HttpResult<Void> switchCollection(SwitchCollectionVO switchCollectionVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.switchCollection(switchCollectionVO));
    }

    @Override
    @ServiceInfo(name = "[完成采集]", webUrl = "/monitor/web/cockpit/map/finishCollection")
    public HttpResult<Void> finishCollection(FinishCollectionVO finishCollectionVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.finishCollection(finishCollectionVO));
    }

    @Override
    @ServiceInfo(name = "[获取采集任务列表]", webUrl = "/monitor/web/cockpit/map/getExplorationTaskList")
    public HttpResult<PageDTO<MonitorTaskSearchDTO>> getExplorationTaskList(MonitorTaskSearchVO monitorTaskSearchVO) {
        return JsfResponse.response(() -> mapTaskService.getExplorationTaskList(monitorTaskSearchVO));
    }

    @Override
    @ServiceInfo(name = "[获取采集路线详情]", webUrl = "/monitor/web/cockpit/map/getExplorationTaskDetail")
    public HttpResult<TaskRouteDTO> getExplorationTaskDetail(TaskBaseVO taskBaseVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getExplorationTaskDetail(taskBaseVO));
    }

    @Override
    @ServiceInfo(name = "[获取附近的标记]", webUrl = "/monitor/web/cockpit/map/getNearbyMarks")
    public HttpResult<MarkSearchDTO> getNearbyMarks(PositionVO positionVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getNearbyMarks(positionVO));
    }

    @Override
    @ServiceInfo(name = "[获取多车页视频链接]", webUrl = "/monitor/web/cockpit/map/getVideoUrlList")
    public HttpResult<List<MultiVideoUrlDTO>> getVideoUrlList(MultiVideoUrlVO multiVideoUrlVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getVideoUrlList(multiVideoUrlVO));
    }

    @Override
    @ServiceInfo(name = "[查看车辆固化数据]", webUrl = "/monitor/web/cockpit/map/getSolidifiedTaskList")
    public HttpResult<List<VehicleSolidifiedTaskDTO>> getSolidifiedTaskList(SolidifiedTaskSearchVO solidifiedTaskSearchVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getSolidifiedTaskList(solidifiedTaskSearchVO));
    }

    @Override
    @ServiceInfo(name = "[取消固化数据]", webUrl = "/monitor/web/cockpit/map/cancelSolidifiedTask")
    public HttpResult<Void> cancelSolidifiedTask(SolidifiedTaskOperateVO solidifiedTaskOperateVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.cancelSolidifiedTask(solidifiedTaskOperateVO));
    }

    @Override
    @ServiceInfo(name = "[获取任务已采集线路]", webUrl = "/monitor/web/cockpit/map/getFinishedRoute")
    public HttpResult<GetRouteDTO> getFinishedRoute(GetRouteVO getRouteVO) {
        return JsfResponse.response(() -> mapCollectionCockpitService.getFinishedRoute(getRouteVO));
    }

    @Override
    @ServiceInfo(name = "获取全量站点", webUrl = "/monitor/web/cockpit/map/getUserStation")
    public HttpResult<List<MonitorBasicDataInfoUnderUseDTO>> getUserStation() {
        return JsfResponse.response(mapCollectionCockpitService::getAllStationInfo);
    }
}