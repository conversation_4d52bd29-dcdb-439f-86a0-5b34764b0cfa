/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.user;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.user.MonitorUserDriveConfigJsfDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.GetUserDriveConfigJsfVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.SaveUserDriveConfigJsfVO;
import com.jdx.rover.monitor.api.web.jsf.service.user.MonitorUserDriveConfigJsfService;
import com.jdx.rover.monitor.service.UserDriveConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 用户配置服务接口
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Service
@RequiredArgsConstructor
public class MonitorUserDriveConfigJsfServiceImpl extends AbstractProvider<MonitorUserDriveConfigJsfService> implements MonitorUserDriveConfigJsfService {
    /**
     * 用户配置service类
     */
    public final UserDriveConfigService userDriveConfigService;

    @Override
    public HttpResult<MonitorUserDriveConfigJsfDTO> saveUserDriveConfig(SaveUserDriveConfigJsfVO saveUserDriveConfigJsfVO) {
        userDriveConfigService.saveOrUpdateUserDriveConfig(saveUserDriveConfigJsfVO);
        return HttpResult.success();
    }

    @Override
    public HttpResult<MonitorUserDriveConfigJsfDTO> getUserDriveConfig(GetUserDriveConfigJsfVO getUserDriveConfigJsfVO) {
        MonitorUserDriveConfigJsfDTO dto = userDriveConfigService.getUserDriveConfig(getUserDriveConfigJsfVO);
        return HttpResult.success(dto);
    }
}