/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.jira;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.datacenter.domain.dto.warehouse.vehicle.VehicleRealtimeJsfDTO;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.jira.JiraAlarmListDTO;
import com.jdx.rover.monitor.service.jira.MonitorJiraService;
import com.jdx.rover.monitor.vo.BugAddVO;
import com.jdx.rover.monitor.vo.SemiJiraAddVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.VehicleRealtimeVO;
import com.jdx.rover.monitor.web.jsf.api.jira.MonitorJiraWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * This is a controller class for jira problem reporting.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorJiraWebJsfServiceImpl extends AbstractProvider<MonitorJiraWebJsfService> implements MonitorJiraWebJsfService {

  private final MonitorJiraService monitorJiraService;

  @ServiceInfo(name = "提报缺陷", webUrl = "/monitor/web/jira/report")
  public HttpResult<String> reportJira(@Valid BugAddVO bugAddVO) {
    ParameterCheckUtility.checkNotNull(bugAddVO, "bugAddVO");
    return monitorJiraService.reportJira(bugAddVO);
  }

  @ServiceInfo(name = "辅助提报缺陷", webUrl = "/monitor/web/jira/semi-report")
  public HttpResult<String> semiReportJira(@Valid SemiJiraAddVO jiraAddVo) {
    ParameterCheckUtility.checkNotNull(jiraAddVo, "jiraAddVo");
    return monitorJiraService.semiReportJira(jiraAddVo);
  }

  @ServiceInfo(name = "告警列表", webUrl = "/monitor/web/jira/getIssueAlarmList")
  public HttpResult<JiraAlarmListDTO> getAvailableReportAlarmList(VehicleNameBasicVO vehicleNameBasicVo) {
    return HttpResult.success(monitorJiraService.getAvailableReportAlarmList(vehicleNameBasicVo.getVehicleName()));
  }

  @ServiceInfo(name = "版本列表", webUrl = "/monitor/web/jira/get_rover_list")
  public HttpResult<List<String>> getRoverList() {
    return monitorJiraService.getRoverList();
  }

  @ServiceInfo(name = "获取车辆历史实时信息", webUrl = "/monitor/web/jira/get_vehicle_realtime_info")
  public HttpResult<VehicleRealtimeJsfDTO> getVehicleRealtimeInfo(VehicleRealtimeVO vehicleRealtimeVO) {
    return HttpResult.success(monitorJiraService.getVehicleRealtimeInfo(vehicleRealtimeVO));
  }
}
