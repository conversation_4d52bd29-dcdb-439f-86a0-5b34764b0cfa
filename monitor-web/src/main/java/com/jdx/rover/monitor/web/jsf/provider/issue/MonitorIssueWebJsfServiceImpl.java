/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.issue;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.issue.IssueAlarmDTO;
import com.jdx.rover.monitor.dto.issue.IssueDetailDTO;
import com.jdx.rover.monitor.dto.issue.IssueRecordDTO;
import com.jdx.rover.monitor.search.IssueRecordSearch;
import com.jdx.rover.monitor.service.web.MonitorIssueService;
import com.jdx.rover.monitor.vo.IssueDetailRequestVO;
import com.jdx.rover.monitor.vo.IssueRecordListRequestVO;
import com.jdx.rover.monitor.vo.IssueVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.issue.MonitorIssueWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor issue.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorIssueWebJsfServiceImpl extends AbstractProvider<MonitorIssueWebJsfService> implements MonitorIssueWebJsfService{

  private final MonitorIssueService monitorIssueService;

  @ServiceInfo(name = "获取工单信息", webUrl = "/monitor/web/issue/detail")
  public HttpResult<IssueDetailDTO> getIssueDetail(IssueDetailRequestVO issueDetailRequestVo) {
    return monitorIssueService.getIssueDetail(issueDetailRequestVo.getVehicleName(), issueDetailRequestVo.getIssueNo());
  }

  @Override
  @ServiceInfo(name = "获取可用工单", webUrl = "/monitor/web/issue/availible")
  public HttpResult<IssueDetailDTO> getAvailibleIssue(VehicleNameBasicVO vehicleNameBasicVo) {
    return monitorIssueService.getAvailibleIssue(vehicleNameBasicVo.getVehicleName());
  }

  @ServiceInfo(name = "新增工单", webUrl = "/monitor/web/issue/add")
  public HttpResult<String> add(IssueVO monitorIssueAddVo) {
    return monitorIssueService.operateAddIssue(monitorIssueAddVo, UserUtils.getLoginUser());
  }

  @ServiceInfo(name = "更新工单", webUrl = "/monitor/web/issue/update")
  public HttpResult<String> update(@Valid IssueVO issueVo) {
    return monitorIssueService.update(issueVo);
  }

  @ServiceInfo(name = "工单列表", webUrl = "/monitor/web/issue/list")
  public HttpResult<PageDTO<IssueRecordDTO>> search(@Valid IssueRecordSearch issueRecordSearch) {
    return HttpResult.success(monitorIssueService.search(issueRecordSearch));
  }

  @ServiceInfo(name = "工单告警列表", webUrl = "/monitor/web/issue/alarm")
  public HttpResult<List<IssueAlarmDTO>> listAlarmByIssueNo(IssueDetailRequestVO issueDetailRequestVo) {
    return HttpResult.success(monitorIssueService.listAlarmByIssueNo(issueDetailRequestVo.getIssueNo(), true));
  }

  @ServiceInfo(name = "工单告警列表", webUrl = "/monitor/web/issue/issue-list")
  public HttpResult<List<IssueDetailDTO>> listIssueAndAlarm(IssueRecordListRequestVO issueRecordListRequestVO) {
    return monitorIssueService.listIssueRecord(issueRecordListRequestVO);
  }
}
