package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.vo.MonitorScheduleStopUpdateVO;
import com.jdx.rover.monitor.jsf.service.MonitorVehicleScheduleStopJsfService;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleStopManager;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is api interface for schedule command.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorVehicleScheduleStopServiceImpl extends AbstractProvider<MonitorVehicleScheduleStopJsfService> implements MonitorVehicleScheduleStopJsfService{

  /**
   * 用于管理和更新调度停靠点实时信息的服务对象。
   */
  private final VehicleScheduleStopManager vehicleScheduleStopManager;

  /**
   * 单车服务对象，用于推送单车页调度变化ws消息。
   */
  private final SingleVehicleService singleVehicleService;

  /**
   * 更新调度停靠点实时信息。
   * @param monitorScheduleStopUpdateVo 更新的监控调度停止信息对象，不能为null。
   * @return 更新结果，若更新成功则返回HttpResult.isSuccess()，否则返回错误信息。
   */
  @Override
  public HttpResult<Void> updateScheduleStop(MonitorScheduleStopUpdateVO monitorScheduleStopUpdateVo) {
    ParameterCheckUtility.checkNotNull(monitorScheduleStopUpdateVo, "monitorScheduleStopUpdateVo");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorScheduleStopUpdateVo.getVehicleName(), "monitorScheduleStopUpdateVo#vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorScheduleStopUpdateVo.getScheduleNo(), "monitorScheduleStopUpdateVo#scheduleNo");
    ParameterCheckUtility.checkNotNull(monitorScheduleStopUpdateVo.getGoalId(), "monitorScheduleStopUpdateVo#goalId");
    HttpResult<Void> httpResult = vehicleScheduleStopManager.updateScheduleStop(monitorScheduleStopUpdateVo);
    if (HttpResult.isSuccess(httpResult)) {
      // 推送单车页调度变化ws消息
      singleVehicleService.pushSingleVehicleSchedule(monitorScheduleStopUpdateVo.getVehicleName());
    }
    return httpResult;
  }
}
