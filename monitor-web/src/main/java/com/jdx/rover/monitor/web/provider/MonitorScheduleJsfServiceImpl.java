package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.dto.VehicleMileageDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleMileageVO;
import com.jdx.rover.monitor.jsf.service.MonitorScheduleJsfService;
import com.jdx.rover.monitor.service.schedule.MonitorScheduleService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class MonitorScheduleJsfServiceImpl extends AbstractProvider<MonitorScheduleJsfService> implements MonitorScheduleJsfService{

    private final MonitorScheduleService monitorScheduleService;

    @Override
    public HttpResult<List<VehicleMileageDTO>> getVehicleMileage(@NotNull(message = "非法请求") VehicleMileageVO vehicleMileageVO) {
        return HttpResult.success(monitorScheduleService.getVehicleMileage(vehicleMileageVO));
    }
}
