/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.cockpit;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.cockpit.CockpitTeamManagerStatisticDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitTransferIssueDTO;
import com.jdx.rover.monitor.dto.cockpit.MultiCockpitPageDTO;
import com.jdx.rover.monitor.vo.CockpitTeamVehicleVO;

import java.util.List;

/**
 * 座席团队接口
 *
 * <AUTHOR>
 */
public interface CockpitTeamWebJsfService {

    public HttpResult<MultiCockpitPageDTO> getCockpitIssueStatistic(CockpitTeamVehicleVO cockpitTeamVo);

    public HttpResult<List<CockpitTransferIssueDTO>> getAllowIssueCockpitList(CockpitTeamVehicleVO cockpitTeamVo);

    public HttpResult<CockpitTeamManagerStatisticDTO> getTeamManagerStatistic(CockpitTeamVehicleVO cockpitTeamVo);

}