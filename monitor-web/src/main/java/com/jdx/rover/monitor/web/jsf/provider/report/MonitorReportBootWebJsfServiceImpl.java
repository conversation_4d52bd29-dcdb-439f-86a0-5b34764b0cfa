package com.jdx.rover.monitor.web.jsf.provider.report;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.service.report.ReportBootService;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.report.ReportBootWebJsfService;
import com.jdx.rover.server.api.domain.dto.report.boot.VehicleBootDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 监控获取启动信息
 * @author: gulin
 * @date: 2024/02/20
 **/
@Service
@RequiredArgsConstructor
public class MonitorReportBootWebJsfServiceImpl extends AbstractProvider<ReportBootWebJsfService> implements ReportBootWebJsfService{

    private final ReportBootService reportBootService;

    @Override
    @ServiceInfo(name = "获取启动信息", webUrl = "/monitor/web/report/boot")
    public HttpResult<VehicleBootDTO> getReportBoot(VehicleNameBasicVO vehicleNameBasicVo) {
        return HttpResult.success(reportBootService.get(vehicleNameBasicVo.getVehicleName()));
    }
}