/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.api.schedule;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleStopDetailDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleTravelDTO;
import com.jdx.rover.monitor.vo.MonitorScheduleStopRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;

/**
 * <p>
 * 调度接口
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorScheduleWebJsfService {

  public HttpResult<Boolean> addSchedule(ScheduleTask scheduleTask);

  public HttpResult<MonitorScheduleDTO> getScheduleDetail(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult<MonitorScheduleTravelDTO> getVehicleScheduleRealtimeInfo(VehicleNameBasicVO vehicleNameBasicVo);

  public HttpResult<MonitorScheduleStopDetailDTO> getScheduleStopRealtimeInfo(MonitorScheduleStopRequestVO monitorScheduleStopRequestVo);

}
