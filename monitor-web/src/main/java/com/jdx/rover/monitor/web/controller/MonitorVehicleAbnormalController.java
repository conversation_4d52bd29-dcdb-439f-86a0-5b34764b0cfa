/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.dto.VehicleExceptionInfoDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleAbnormalVO;
import com.jdx.rover.monitor.service.GuardianVehicleAbnormalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * This is a api interface for monitor data info.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequestMapping(value = "/monitor/web/abnormal")
@RestController
public class MonitorVehicleAbnormalController {

  @Autowired
  private GuardianVehicleAbnormalService abnormalService;

  /**
   * <p>
   * Search vehicle realtime info by search entity.
   * </p>
   *
   * @param vehicleAbnormalVo The search entity for monitor info.
   */
  @PostMapping("/list")
  public HttpResult getVehicleAbnormalInfo(@RequestBody VehicleAbnormalVO vehicleAbnormalVo) {
    List<VehicleExceptionInfoDTO> dataList =
            abnormalService.listVehicleAbnoraml(vehicleAbnormalVo.getVehicleName(),
                    vehicleAbnormalVo.getStartTime(), vehicleAbnormalVo.getEndTime());
    return HttpResult.success(dataList);
  }

}