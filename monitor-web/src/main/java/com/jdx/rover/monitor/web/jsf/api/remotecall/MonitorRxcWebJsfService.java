package com.jdx.rover.monitor.web.jsf.api.remotecall;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.word.WordMonitorListDto;
import com.jdx.rover.monitor.vo.MonitorWordAddVO;
import com.jdx.rover.monitor.vo.MonitorWordDeleteVO;

import java.util.List;

/**
 * <p>
 * 远程喊话接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/02/20
 */
public interface MonitorRxcWebJsfService {

    public HttpResult<List<WordMonitorListDto>> getWordList();

    public HttpResult<Object> addWord(MonitorWordAddVO monitorWordAddVO);

    public HttpResult<Object> deleteWord(MonitorWordDeleteVO monitorWordDeleteVO);
}
