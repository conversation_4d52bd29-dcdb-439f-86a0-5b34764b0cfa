/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.base;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.MonitorSpeedLimitDTO;
import com.jdx.rover.monitor.dto.MonitorUserCityBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorUserVehicleStatisticInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.vo.CockpitVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorDataTypeVO;
import com.jdx.rover.monitor.vo.MonitorUserMetaDataInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;

import java.util.List;
import javax.validation.Valid;

/**
 * 通用基础信息请求
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
public interface MonitorBasicInfoWebJsfService {

  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getUserVehicleBasicInfo(MonitorUserVehicleBasicInfoRequestVO basicInfoRequestVo);

  /**
   * <p>
   * 获取驾舱车辆左侧树
   * </p>
   */
  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getCockpitVehicleTree(CockpitVehicleBasicInfoRequestVO basicInfoRequestVo);

  /**
   * <p>
   * This method helps to return user's permissions.
   * </p>
   */
  public HttpResult<List<MonitorUserVehicleStatisticInfoDTO>> getUserVehicleTypeSize(MonitorDataTypeVO monitorDataTypeVo);

  /**
   * <p>
   * This method helps to return vehicle basic info.
   * </p>
   *
   */
  public HttpResult<MonitorVehiceBasicInfoDTO> getVehicleBasicInfoByIdentity(VehicleNameBasicVO vehicleNameBasicVo);

  /**
   * <p>
   * This method helps to return user city station vehicle basic info.
   * </p>
   *
   */
  public HttpResult<List<MonitorBasicDataInfoUnderUseDTO>> getUserMetaDataByIdentity(MonitorDataTypeVO monitorDataTypeVo);

  /**
   * <p>
   * This method helps to return user metadata list info.
   * </p>
   *
   */
  public HttpResult<List> getUserMetaDataList(MonitorUserMetaDataInfoRequestVO metaDataInfoRequestVo);

    /**
     * 获取车辆临时限速配置
     */
    public HttpResult<MonitorSpeedLimitDTO> getTempSpeedLimitInfo(@Valid VehicleNameBasicVO vehicleNameBasicVo);
}