/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.s3;

import com.amazonaws.HttpMethod;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.s3.S3PreSignDTO;
import com.jdx.rover.monitor.service.s3.S3PreSignUrlService;
import com.jdx.rover.monitor.vo.s3.S3PreSignVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * oss对象存储操作
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/monitor/web/s3")
@RequiredArgsConstructor
@Slf4j
public class S3PresignUrlController {
    private final S3PreSignUrlService s3PreSignUrlService;

    /**
     * 预签名get url,用于下载
     *
     * @param s3PreSignVO
     * @return
     */
    @PostMapping("/webTerminal/preSignGetUrl")
    public HttpResult preSignGetUrl(@RequestBody S3PreSignVO s3PreSignVO) {
        String url = s3PreSignUrlService.preSignUrl(s3PreSignVO, HttpMethod.GET);
        return HttpResult.success(url);
    }

    /**
     * 预签名put url,用于上传
     *
     * @param s3PreSignVO
     * @return
     */
    @PostMapping("/webTerminal/preSignPutUrl")
    public HttpResult preSignPutUrl(@RequestBody S3PreSignVO s3PreSignVO) {
        String url = s3PreSignUrlService.preSignUrl(s3PreSignVO, HttpMethod.PUT);
        return HttpResult.success(url);
    }

    /**
     * 预签名get和put url,用于下载上传
     *
     * @param s3PreSignVO
     * @return
     */
    @PostMapping("/webTerminal/preSignGetAndPutUrl")
    public HttpResult preSignGetAndPutUrl(@RequestBody S3PreSignVO s3PreSignVO) {
        S3PreSignDTO dto = s3PreSignUrlService.preSignGetAndPutUrl(s3PreSignVO);
        return HttpResult.success(dto);
    }
}
