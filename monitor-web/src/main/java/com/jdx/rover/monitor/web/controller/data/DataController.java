package com.jdx.rover.monitor.web.controller.data;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.monitor.dto.data.GetCockpitDataPageDTO;
import com.jdx.rover.monitor.dto.data.GetSupportDataListDTO;
import com.jdx.rover.monitor.dto.data.GetCockpitTeamDataListDTO;
import com.jdx.rover.monitor.service.data.DataService;
import com.jdx.rover.monitor.vo.data.GetCockpitDataPageVO;
import com.jdx.rover.monitor.vo.data.GetSupportDataListVO;
import com.jdx.rover.monitor.vo.data.GetCockpitTeamDataListVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 驾舱数据Controller
 */
@RestController
@RequestMapping(value = "/monitor/web/data")
public class DataController {

    @Resource
    private DataService dataService;

    /**
     * 获取坐席数据列表
     *
     * @param getCockpitDataPageVO getCockpitDataPageVO
     * @return PageDTO<GetCockpitDataPageDTO>
     */
    @PostMapping("/get_cockpit_data_list")
    public PageDTO<GetCockpitDataPageDTO> getCockpitDataPage(@Valid @RequestBody GetCockpitDataPageVO getCockpitDataPageVO) {
        ParameterCheckUtility.checkNotNull(getCockpitDataPageVO.getPageNum(), "pageNum");
        ParameterCheckUtility.checkNotNull(getCockpitDataPageVO.getPageSize(), "pageSize");
        return dataService.getCockpitDataPage(getCockpitDataPageVO);
    }

    /**
     * 获取远驾团队数据列表
     *
     * @param getCockpitTeamDataListVO getCockpitTeamDataListVO
     * @return PageDTO<GetCockpitTeamDataListDTO>
     */
    @PostMapping(value = "/get_cockpit_team_data_list")
    public PageDTO<GetCockpitTeamDataListDTO> getCockpitTeamDataList(@RequestBody GetCockpitTeamDataListVO getCockpitTeamDataListVO) {
        ParameterCheckUtility.checkNotNull(getCockpitTeamDataListVO.getPageNum(), "pageNum");
        ParameterCheckUtility.checkNotNull(getCockpitTeamDataListVO.getPageSize(), "pageSize");
        return dataService.getCockpitTeamDataList(getCockpitTeamDataListVO);
    }

    /**
     * 获取技术支持数据列表
     * @param getSupportDataListVO getSupportDataListVO
     * @return PageDTO<GetSupportDataListDTO>
     */
    @PostMapping("/get_support_data_list")
    public PageDTO<GetSupportDataListDTO> getSupportDataList(@Valid @RequestBody GetSupportDataListVO getSupportDataListVO) {
        return dataService.getSupportDataList(getSupportDataListVO);
    }
}