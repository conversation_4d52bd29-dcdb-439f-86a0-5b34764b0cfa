/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.base;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorBasicDataInfoUnderUseDTO;
import com.jdx.rover.monitor.dto.MonitorSpeedLimitDTO;
import com.jdx.rover.monitor.dto.MonitorUserCityBasicInfoDTO;
import com.jdx.rover.monitor.dto.MonitorUserVehicleStatisticInfoDTO;
import com.jdx.rover.monitor.dto.MonitorVehiceBasicInfoDTO;
import com.jdx.rover.monitor.service.vehicle.VehicleSpeedLimitService;
import com.jdx.rover.monitor.service.web.MonitorBasicInfoService;
import com.jdx.rover.monitor.vo.CockpitVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorDataTypeVO;
import com.jdx.rover.monitor.vo.MonitorUserMetaDataInfoRequestVO;
import com.jdx.rover.monitor.vo.MonitorUserVehicleBasicInfoRequestVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.web.jsf.api.base.MonitorBasicInfoWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用枚举请求
 *
 * <AUTHOR>
 * @date 2025/02/20
 */
@Service
@RequiredArgsConstructor
public class MonitorBasicInfoWebJsfServiceImpl extends AbstractProvider<MonitorBasicInfoWebJsfService> implements MonitorBasicInfoWebJsfService {

  private final MonitorBasicInfoService monitorBasicInfoService;
  private final VehicleSpeedLimitService vehicleSpeedLimitService;

  @Override
  @ServiceInfo(name = "获取用户车辆列表", webUrl = "/monitor/web/basic/user_vehicle")
  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getUserVehicleBasicInfo(MonitorUserVehicleBasicInfoRequestVO basicInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getUserVehicleBasicInfo(basicInfoRequestVo));
  }

  @Override
  @ServiceInfo(name = "获取座席车辆树", webUrl = "/monitor/web/basic/getCockpitVehicleTree")
  public HttpResult<List<MonitorUserCityBasicInfoDTO>> getCockpitVehicleTree(CockpitVehicleBasicInfoRequestVO basicInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getCockpitVehicleStationTree(basicInfoRequestVo));
  }

  @Override
  @ServiceInfo(name = "获取用户车辆类型数量", webUrl = "/monitor/web/basic/user_vehicle_type")
  public HttpResult<List<MonitorUserVehicleStatisticInfoDTO>> getUserVehicleTypeSize(MonitorDataTypeVO monitorDataTypeVo) {
    return HttpResult.success(monitorBasicInfoService.getUserVehicleTypeSize(monitorDataTypeVo.getDataType()));
  }

  @Override
  @ServiceInfo(name = "获取车辆基础数据", webUrl = "/monitor/web/basic/vehicle")
  public HttpResult<MonitorVehiceBasicInfoDTO> getVehicleBasicInfoByIdentity(VehicleNameBasicVO vehicleNameBasicVo) {
    return HttpResult.success(monitorBasicInfoService.getVehicleBasicInfo(vehicleNameBasicVo.getVehicleName()));
  }

  @Override
  @ServiceInfo(name = "获取用户权限下基础数据", webUrl = "/monitor/web/basic/user_metadata")
  public HttpResult<List<MonitorBasicDataInfoUnderUseDTO>> getUserMetaDataByIdentity(MonitorDataTypeVO monitorDataTypeVo) {
    return HttpResult.success(monitorBasicInfoService.getUserMetaData(monitorDataTypeVo.getDataType(), UserUtils.getAndCheckLoginUser()));
  }

  @Override
  @ServiceInfo(name = "获取用户权限下数据列表", webUrl = "/monitor/web/basic/user_metadata_list")
  public HttpResult<List> getUserMetaDataList(MonitorUserMetaDataInfoRequestVO metaDataInfoRequestVo) {
    return HttpResult.success(monitorBasicInfoService.getUserMetaDataList(metaDataInfoRequestVo.getDataType(), metaDataInfoRequestVo));
  }

    @Override
    @ServiceInfo(name = "获取临时限速信息", webUrl = "/monitor/web/basic/getTempSpeedLimitInfo")
    public HttpResult<MonitorSpeedLimitDTO> getTempSpeedLimitInfo(VehicleNameBasicVO vehicleNameBasicVo) {
        return JsfResponse.response(() -> vehicleSpeedLimitService.getTempSpeedLimitInfo(vehicleNameBasicVo));
    }
}