/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.robot;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.dto.robot.RobotDeviceBasicDTO;
import com.jdx.rover.monitor.dto.robot.RobotGroupMapInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.vo.robot.RobotCommandBaseVO;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;

/**
 * <p>
 * 监控机器人地图服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
public interface MonitorRobotMapJsfService {

    HttpResult<RobotGroupMapInfoDTO> getMapInfoByGroup(RobotMapInfoRequestVO mapInfoRequestVo);

    HttpResult<RobotMapRouteInfoDTO> getMapDeviceRoute(RobotDeviceBasicSearchVO robotDeviceBasicSearchVo);
}
