package com.jdx.rover.monitor.web.jsf.provider.cockpit;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.cockpit.CockpitChangeService;
import com.jdx.rover.monitor.vo.UserNameBasicVO;
import com.jdx.rover.monitor.vo.cockpit.EnterCockpitVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchModeVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchStatusVO;
import com.jdx.rover.monitor.web.jsf.api.cockpit.CockpitChangeWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @description: 座席模式切换
 * @author: wangguotai
 * @date: 2024-06-11 10:08
 **/
@Service
@RequiredArgsConstructor
public class CockpitChangeWebJsfServiceImpl extends AbstractProvider<CockpitChangeWebJsfService> implements CockpitChangeWebJsfService{

    private final CockpitChangeService cockpitChangeService;

    @ServiceInfo(name = "入座座席", webUrl = "/monitor/web/cockpit/enterCockpit")
    public HttpResult<String> enterCockpit(@Valid EnterCockpitVO enterCockpitVO) {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.enterCockpit(enterCockpitVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "退出座席", webUrl = "/monitor/web/cockpit/quitCockpit")
    public HttpResult<String> quitCockpit() {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.quitCockpit();
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "切换模式", webUrl = "/monitor/web/cockpit/switchMode")
    public HttpResult<String> switchMode(@Valid SwitchModeVO switchModeVO) {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.switchMode(switchModeVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "切换状态", webUrl = "/monitor/web/cockpit/switchStatus")
    public HttpResult<String> switchStatus(@Valid SwitchStatusVO switchStatusVO) {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.switchStatus(switchStatusVO);
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "进入座席模式（按钮点击）", webUrl = "/monitor/web/cockpit/enterMode")
    public HttpResult<String> enterMode() {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.enterMode();
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "强制退出座席(OPS接口)", webUrl = "/monitor/web/cockpit/forceQuitCockpit")
    public HttpResult<String> forceQuitCockpit(UserNameBasicVO userNameBasicVo) {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.forceQuitCockpit(userNameBasicVo.getUserName());
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }

    @ServiceInfo(name = "手动同步刷新用户座席(OPS接口)", webUrl = "/monitor/web/cockpit/refresh")
    public HttpResult<String> refresh(UserNameBasicVO userNameBasicVo) {
        MonitorErrorEnum monitorErrorEnum = cockpitChangeService.refresh(userNameBasicVo.getUserName());
        return new HttpResult(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage(), null);
    }
}