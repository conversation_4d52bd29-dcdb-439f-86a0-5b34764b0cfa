package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.dto.VehicleTakeOverDTO;
import com.jdx.rover.monitor.jsf.service.MonitorVehicleCommandJsfService;
import com.jdx.rover.monitor.service.web.MonitorRemoteCommandService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 车辆指令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorVehicleCommandServiceImpl extends AbstractProvider<MonitorVehicleCommandJsfService> implements MonitorVehicleCommandJsfService {

  /**
   * 远程命令服务实例，用于获取接管车辆列表。
   */
  private final MonitorRemoteCommandService remoteCommandService;

  /**
   * 获取指定用户的接管车辆列表。
   * @param userName 用户名
   * @return 接管车辆列表
   */
  @Override
  public HttpResult<List<VehicleTakeOverDTO>> getTakeOverVehicle(String userName) {
    return remoteCommandService.getTakeOverVehicle(userName);
  }
}
