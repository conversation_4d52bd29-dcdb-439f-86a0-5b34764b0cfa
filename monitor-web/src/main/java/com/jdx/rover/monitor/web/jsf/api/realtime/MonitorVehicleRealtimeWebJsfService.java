/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.api.realtime;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorNavibarVehicleRealtimeInfoDTO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;

/**
 * <p>
 * 车辆基础信息jsf接口
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface MonitorVehicleRealtimeWebJsfService {


  /**
   * 获取多辆车的位置信息
   */
  public HttpResult<MonitorNavibarVehicleRealtimeInfoDTO> getMultiVehicleMapInfo(VehicleNameBasicVO vehicleNameVo);


  /**
   * 获取指定车辆的预定里程数。
   */
  public HttpResult<Double> getVehicleScheduleMileage(VehicleNameBasicVO vehicleNameVo);

}