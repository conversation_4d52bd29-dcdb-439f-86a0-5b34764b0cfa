/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.order;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.dto.MonitorOrderDTO;
import com.jdx.rover.monitor.service.web.MonitorOrderService;
import com.jdx.rover.monitor.vo.MonitorOrderRequestVO;
import com.jdx.rover.monitor.vo.MonitorVerifyCodeRequestVO;
import com.jdx.rover.monitor.vo.MonitorViewOrderRequestVO;
import com.jdx.rover.monitor.web.jsf.api.order.MonitorOrderWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a controller class for supervisor order.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorOrderWebJsfServiceImpl extends AbstractProvider<MonitorOrderWebJsfService> implements MonitorOrderWebJsfService {

  private final MonitorOrderService monitorOrderService;

  @ServiceInfo(name = "查询订单", webUrl = "/monitor/web/order/list")
  public HttpResult<MonitorOrderDTO> search(MonitorOrderRequestVO monitorOrderRequestVo) {
    return monitorOrderService.search(monitorOrderRequestVo);
  }

  @ServiceInfo(name = "订单验证码", webUrl = "/monitor/web/order/verify_code")
  public HttpResult<Void> sendVerifyCode(MonitorVerifyCodeRequestVO verifyCodeVo) {
    return monitorOrderService.sendVerifyCode(verifyCodeVo);
  }

  @ServiceInfo(name = "安全日志", webUrl = "/monitor/web/order/detail")
  public HttpResult<Void> viewOrderDetail(MonitorViewOrderRequestVO viewOrderVo) {
    String userName = UserUtils.getAndCheckLoginUser();
    return monitorOrderService.viewOrderInfo(userName, viewOrderVo);
  }

}
