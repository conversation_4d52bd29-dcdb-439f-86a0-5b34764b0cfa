package com.jdx.rover.monitor.web.jsf.api.cockpit;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.vo.UserNameBasicVO;
import com.jdx.rover.monitor.vo.cockpit.EnterCockpitVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchModeVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchStatusVO;
import jakarta.validation.Valid;

/**
 * @description: 座席模式切换Controller
 * @author: wangguotai
 * @date: 2024-06-11 10:08
 **/
public interface CockpitChangeWebJsfService {

    public HttpResult<String> enterCockpit(@Valid EnterCockpitVO enterCockpitVO);

    public HttpResult<String> quitCockpit();

    public HttpResult<String> switchMode(@Valid SwitchModeVO switchModeVO);

    public HttpResult<String> switchStatus(@Valid SwitchStatusVO switchStatusVO);

    public HttpResult<String> enterMode();

    public HttpResult<String> forceQuitCockpit(UserNameBasicVO userNameBasicVo);

    public HttpResult<String> refresh(UserNameBasicVO userNameBasicVo);
}