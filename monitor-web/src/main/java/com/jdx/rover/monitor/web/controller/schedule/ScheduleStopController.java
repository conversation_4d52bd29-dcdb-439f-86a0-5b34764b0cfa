/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.schedule;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.vo.MonitorScheduleStopUpdateVO;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleStopManager;
import com.jdx.rover.monitor.service.vehicle.SingleVehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * This is a controller class for schedule stop.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value = "/monitor/schedule/schedule_stop")
public class ScheduleStopController {

  @Autowired
  private VehicleScheduleStopManager vehicleScheduleStopManager;

  @Autowired
  private SingleVehicleService singleVehicleService;

  /**
   * <p>
   * Update schedule stop info.
   * </p>
   * 
   */
  @PostMapping("/update")
  HttpResult updateScheduleStop(@Valid @RequestBody MonitorScheduleStopUpdateVO monitorScheduleStopUpdateVo) {
    ParameterCheckUtility.checkNotNull(monitorScheduleStopUpdateVo, "monitorScheduleStopUpdateVo");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorScheduleStopUpdateVo.getVehicleName(), "monitorScheduleStopUpdateVo#vehicleName");
    ParameterCheckUtility.checkNotNullNorEmpty(monitorScheduleStopUpdateVo.getScheduleNo(), "monitorScheduleStopUpdateVo#scheduleNo");
    ParameterCheckUtility.checkNotNull(monitorScheduleStopUpdateVo.getGoalId(), "monitorScheduleStopUpdateVo#goalId");
    HttpResult httpResult = vehicleScheduleStopManager.updateScheduleStop(monitorScheduleStopUpdateVo);
    if (HttpResult.isSuccess(httpResult)) {
      // 推送单车页调度变化ws消息
      singleVehicleService.pushSingleVehicleSchedule(monitorScheduleStopUpdateVo.getVehicleName());
    }
    return httpResult;
  }

}
