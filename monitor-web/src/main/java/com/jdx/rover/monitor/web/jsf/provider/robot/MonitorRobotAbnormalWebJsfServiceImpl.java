/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.jsf.provider.robot;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.metadata.api.domain.enums.common.YesOrNoEnum;
import com.jdx.rover.monitor.dto.robot.RobotAbnormalPageDTO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmCodeEnum;
import com.jdx.rover.monitor.enums.device.DeviceAlarmProcessModeEnum;
import com.jdx.rover.monitor.enums.device.DeviceWorkModeEnum;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.repository.mapper.RobotAbnormalInfoMapper;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.vo.robot.RobotAbnormalPageSearchVO;
import com.jdx.rover.monitor.web.jsf.api.robot.MonitorRobotAbnormalJsfService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 监控机器人异常服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Service
@RequiredArgsConstructor
public class MonitorRobotAbnormalWebJsfServiceImpl extends AbstractProvider<MonitorRobotAbnormalJsfService> implements MonitorRobotAbnormalJsfService{

    /**
     * 异常服务
     */
    private final RobotAbnormalInfoMapper robotAbnormalInfoMapper;

    @Override
    @ServiceInfo(name = "分页查询列表", webUrl = "/monitor/web/robot/abnormal/page")
    public HttpResult<PageDTO<RobotAbnormalPageDTO>> pageSearch(RobotAbnormalPageSearchVO robotAbnormalPageSearchVo) {
        LambdaQueryWrapper<RobotAbnormalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(robotAbnormalPageSearchVo.getDeviceName()), RobotAbnormalInfo::getDeviceName, robotAbnormalPageSearchVo.getDeviceName());
        queryWrapper.eq(StringUtils.isNotEmpty(robotAbnormalPageSearchVo.getRemarkName()), RobotAbnormalInfo::getRemarkName, robotAbnormalPageSearchVo.getRemarkName());
        queryWrapper.eq(StringUtils.isNotEmpty(robotAbnormalPageSearchVo.getWorkMode()), RobotAbnormalInfo::getWorkMode, robotAbnormalPageSearchVo.getWorkMode());
        queryWrapper.like(StringUtils.isNotEmpty(robotAbnormalPageSearchVo.getStationName()), RobotAbnormalInfo::getStationName, robotAbnormalPageSearchVo.getStationName());
        if (StringUtils.equals(robotAbnormalPageSearchVo.getProcessMode(), DeviceAlarmProcessModeEnum.LOCAL_PROCESS.getValue())) {
            queryWrapper.eq(RobotAbnormalInfo::getProcessMode, robotAbnormalPageSearchVo.getProcessMode());
        } else if (StringUtils.equals(robotAbnormalPageSearchVo.getProcessMode(), DeviceAlarmProcessModeEnum.REMOTE_PROCESS.getValue())){
            queryWrapper.isNull(RobotAbnormalInfo::getProcessMode);
        }
        queryWrapper.like(StringUtils.isNotEmpty(robotAbnormalPageSearchVo.getFollowUser()), RobotAbnormalInfo::getFollowUser, robotAbnormalPageSearchVo.getFollowUser());
        if (robotAbnormalPageSearchVo.getStartTime() != null && robotAbnormalPageSearchVo.getEndTime() != null) {
            queryWrapper.ge(RobotAbnormalInfo:: getStartTime, robotAbnormalPageSearchVo.getStartTime());
            queryWrapper.le(RobotAbnormalInfo:: getStartTime, robotAbnormalPageSearchVo.getEndTime());
        }
        queryWrapper.orderByDesc(RobotAbnormalInfo::getStartTime);
        IPage<RobotAbnormalInfo> iPage = PageUtils.toMpPage(robotAbnormalPageSearchVo,RobotAbnormalInfo.class);
        IPage<RobotAbnormalInfo> robotAbnormalInfoPage = robotAbnormalInfoMapper.selectPage(iPage, queryWrapper);
        PageDTO<RobotAbnormalPageDTO> pageInfo = new PageDTO<>();
        pageInfo.setTotal(robotAbnormalInfoPage.getTotal());
        pageInfo.setPageNum((int)robotAbnormalInfoPage.getCurrent());
        pageInfo.setPageSize((int)robotAbnormalInfoPage.getSize());
        if(CollectionUtils.isEmpty(robotAbnormalInfoPage.getRecords())) {
            return HttpResult.success(pageInfo);
        }
        pageInfo.setList(buildRobotAbnormalInfo(robotAbnormalInfoPage.getRecords()));
        return HttpResult.success(pageInfo);
    }

    /**
     * 将RobotAbnormalInfo列表转换为RobotAbnormalPageDTO列表。
     * @param records RobotAbnormalInfo列表
     * @return 转换后的RobotAbnormalPageDTO列表
     */
    private List<RobotAbnormalPageDTO> buildRobotAbnormalInfo(List<RobotAbnormalInfo> records) {
        return records.stream().map(record -> {
            RobotAbnormalPageDTO pageDto = new RobotAbnormalPageDTO();
            pageDto.setId(record.getId());
            pageDto.setDeviceName(record.getDeviceName());
            pageDto.setRemarkName(record.getRemarkName());
            if (StringUtils.isNotBlank(record.getWorkMode())) {
                Optional.ofNullable(DeviceWorkModeEnum.of(record.getWorkMode())).
                        ifPresent(data -> pageDto.setWorkModeName(data.getWorkModeName()));
            }
            if (StringUtils.isNotBlank(record.getErrorLevel())) {
                Optional.ofNullable(AlarmLevelEnum.valueOf(record.getErrorLevel())).
                        ifPresent(data -> pageDto.setErrorLevelName(data.getName()));
            }
            pageDto.setProcessModeName(StringUtils.equals(record.getProcessMode(),
                    DeviceAlarmProcessModeEnum.LOCAL_PROCESS.getValue())? YesOrNoEnum.YES.getName() : YesOrNoEnum.NO.getName());
            pageDto.setStationName(record.getStationName());
            pageDto.setFollowUser(record.getFollowUser());
            pageDto.setStartTime(record.getStartTime());
            pageDto.setEndTime(record.getEndTime());
            Optional.ofNullable(DeviceAlarmCodeEnum.of(record.getErrorCode())).
                    ifPresent(code -> {
                        pageDto.setErrorTypeName(code.getAlarmMsg());
                        pageDto.setSolutionName(code.getSolutionName());
                    });
            pageDto.setErrorMsg(record.getErrorMsg());
            pageDto.setErrorNumber(record.getErrorNumber());
            pageDto.setStationName(record.getStationName());
            pageDto.setTaskNo(record.getTaskNo());
            pageDto.setPoint(record.getPoint());
            return pageDto;
        }).collect(Collectors.toList());
    }
}
