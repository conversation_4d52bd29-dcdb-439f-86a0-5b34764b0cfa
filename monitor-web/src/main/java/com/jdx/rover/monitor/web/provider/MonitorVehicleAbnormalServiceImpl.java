package com.jdx.rover.monitor.web.provider;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.api.domain.dto.VehicleExceptionInfoDTO;
import com.jdx.rover.monitor.api.domain.vo.VehicleAbnormalVO;
import com.jdx.rover.monitor.jsf.service.MonitorVehicleAbnormalJsfService;
import com.jdx.rover.monitor.service.GuardianVehicleAbnormalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * This is api interface for abnormal info.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is mutable and not thread safe. But it will be used
 * as entity so it'll not cause any thread safe problem.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorVehicleAbnormalServiceImpl extends AbstractProvider<MonitorVehicleAbnormalJsfService> implements MonitorVehicleAbnormalJsfService {

  /**
   * 提供车辆异常信息的服务接口。
   */
  private final GuardianVehicleAbnormalService abnormalService;

  /**
   * 获取指定时间段内的车辆异常信息。
   * @param vehicleAbnormalVo 包含车辆名称、开始时间和结束时间的VO对象。
   * @return 返回一个HttpResult对象，包含了List<VehicleExceptionInfoDTO>类型的数据列表。
   */
  @Override
  public HttpResult<List<VehicleExceptionInfoDTO>> getVehicleRealtimeInfo(VehicleAbnormalVO vehicleAbnormalVo) {
    List<VehicleExceptionInfoDTO> dataList =
        abnormalService.listVehicleAbnoraml(vehicleAbnormalVo.getVehicleName(),
            vehicleAbnormalVo.getStartTime(), vehicleAbnormalVo.getEndTime());
    return HttpResult.success(dataList);
  }
}
