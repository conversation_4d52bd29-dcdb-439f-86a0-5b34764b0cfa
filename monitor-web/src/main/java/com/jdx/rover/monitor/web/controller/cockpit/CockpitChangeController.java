package com.jdx.rover.monitor.web.controller.cockpit;

import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.cockpit.CockpitChangeService;
import com.jdx.rover.monitor.vo.cockpit.EnterCockpitVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchModeVO;
import com.jdx.rover.monitor.vo.cockpit.SwitchStatusVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 座席模式切换Controller
 * @author: wang<PERSON><PERSON>i
 * @create: 2024-06-11 10:08
 **/
@RequiredArgsConstructor
@RequestMapping(value = "/monitor/web/cockpit")
@RestController
public class CockpitChangeController {

    private final CockpitChangeService cockpitChangeService;

    /**
     * 1、入座座席
     *
     * @param enterCockpitVO enterCockpitVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/enterCockpit")
    public MonitorErrorEnum enterCockpit(@Valid @RequestBody EnterCockpitVO enterCockpitVO) {
        return cockpitChangeService.enterCockpit(enterCockpitVO);
    }

    /**
     * 2、退出座席
     *
     * @return MonitorErrorEnum
     */
    @GetMapping(value = "/quitCockpit")
    public MonitorErrorEnum quitCockpit() {
        return cockpitChangeService.quitCockpit();
    }

    /**
     * 3、切换模式
     *
     * @param switchModeVO switchModeVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/switchMode")
    public MonitorErrorEnum switchMode(@Valid @RequestBody SwitchModeVO switchModeVO) {
        return cockpitChangeService.switchMode(switchModeVO);
    }

    /**
     * 4、切换状态
     *
     * @param switchStatusVO switchStatusVO
     * @return MonitorErrorEnum
     */
    @PostMapping(value = "/switchStatus")
    public MonitorErrorEnum switchStatus(@Valid @RequestBody SwitchStatusVO switchStatusVO) {
        return cockpitChangeService.switchStatus(switchStatusVO);
    }

    /**
     * 5、进入座席模式（按钮点击）
     *
     * @return MonitorErrorEnum
     */
    @GetMapping(value = "/enterMode")
    public MonitorErrorEnum enterMode() {
        return cockpitChangeService.enterMode();
    }

    /**
     * 强制退出座席(OPS接口)
     *
     * @param userName userName
     * @return MonitorErrorEnum
     */
    @GetMapping(value = "/forceQuitCockpit")
    public MonitorErrorEnum forceQuitCockpit(@RequestParam(value = "userName") String userName) {
        return cockpitChangeService.forceQuitCockpit(userName);
    }

    /**
     * 手动同步刷新用户座席(OPS接口)
     *
     * @param userName userName
     * @return MonitorErrorEnum
     */
    @GetMapping(value = "/refresh")
    public MonitorErrorEnum refresh(@RequestParam(value = "userName") String userName) {
        return cockpitChangeService.refresh(userName);
    }
}