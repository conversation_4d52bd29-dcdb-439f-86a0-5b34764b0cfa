/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller;

import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorScheduleDTO;
import com.jdx.rover.monitor.dto.MonitorScheduleTravelDTO;
import com.jdx.rover.monitor.entity.MonitorScheduleEntity;
import com.jdx.rover.monitor.enums.LiteFlowTaskEnum;
import com.jdx.rover.monitor.manager.schedule.VehicleScheduleManager;
import com.jdx.rover.monitor.repository.redis.VehicleScheduleRepository;
import com.jdx.rover.schedule.api.domain.kafka.ScheduleTask;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>
 * 测试调度用，可删除.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(value="/monitor/web/schedule")
public class MonitorScheduleController {

  @Autowired
  private VehicleScheduleManager vehicleScheduleManager;

  @Resource
  private FlowExecutor flowExecutor;

  @Autowired
  private VehicleScheduleRepository vehicleScheduleRepository;

  @PostMapping("/add")
  public HttpResult addSchedule(@RequestBody(required = true) ScheduleTask scheduleTask) {
    LiteflowResponse response = flowExecutor.execute2Resp(LiteFlowTaskEnum.SCHEDULECHAIN.getType(), scheduleTask, ScheduleTask.class);
    if (!response.isSuccess()) {
      return HttpResult.error();
    }
    MonitorScheduleEntity scheduleEntity = response.getSlot().getResponseData();
    if (scheduleEntity != null) {
      vehicleScheduleRepository.set(scheduleEntity);
    }
    return HttpResult.success(response.isSuccess());
  }

  @GetMapping("/detail/{vehicleName}")
  public HttpResult getScheduleDetail(@PathVariable(value = "vehicleName") String vehicleName) {
    Map<String, MonitorScheduleDTO> scheduleDtoMap = vehicleScheduleManager.getScheduleDetailMap(Lists.newArrayList(vehicleName));
    return HttpResult.success(scheduleDtoMap.get(vehicleName));
  }

  @GetMapping("/realtime_schedule/{vehicleName}")
  public HttpResult getVehicleScheduleRealtimeInfo(@PathVariable(value = "vehicleName") String vehicleName) {
    MonitorScheduleTravelDTO scheduleDto = vehicleScheduleManager.getScheduleTravelInfo(vehicleName);
    return HttpResult.success(scheduleDto);
  }

}
