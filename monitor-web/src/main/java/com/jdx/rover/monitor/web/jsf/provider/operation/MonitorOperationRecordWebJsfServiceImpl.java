/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.jsf.provider.operation;

import com.google.common.base.Preconditions;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorOperationRecordDTO;
import com.jdx.rover.monitor.search.MonitorDataListSearch;
import com.jdx.rover.monitor.service.web.MonitorOperationRecordService;
import com.jdx.rover.monitor.web.jsf.api.operation.MonitorOperationRecordWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a controller class for monitor operation record.
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class MonitorOperationRecordWebJsfServiceImpl extends AbstractProvider<MonitorOperationRecordWebJsfService> implements MonitorOperationRecordWebJsfService {

  private final MonitorOperationRecordService monitorOperationRecordService;

  @ServiceInfo(name = "获取操作数据列表", webUrl = "/monitor/web/operation/list")
  public HttpResult<PageDTO<MonitorOperationRecordDTO>> search(MonitorDataListSearch monitorDataListSearch) {
    Preconditions.checkNotNull(monitorDataListSearch, "查询条件不能为空");
    Preconditions.checkNotNull(monitorDataListSearch.getStartTime(), "查询条件开始时间不能为空");
    Preconditions.checkNotNull(monitorDataListSearch.getEndTime(), "查询条件结束时间不能为空");
    PageDTO<MonitorOperationRecordDTO> operationRecordDtoList = monitorOperationRecordService.search(monitorDataListSearch);
    return HttpResult.success(operationRecordDtoList);
  }

}