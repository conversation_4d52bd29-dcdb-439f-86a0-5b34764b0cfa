package com.jdx.rover.monitor.web.jsf.provider.user;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.user.GetCockpitStatusDTO;
import com.jdx.rover.monitor.service.user.UserChangeService;
import com.jdx.rover.monitor.web.jsf.api.user.MonitorUserChangeWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @description: 用户页面切换Controller
 * @author: wangguotai
 * @create: 2024-06-12 17:14
 **/
@Service
@RequiredArgsConstructor
public class MonitorUserChangeWebJsfServiceImpl extends AbstractProvider<MonitorUserChangeWebJsfService> implements MonitorUserChangeWebJsfService{

    private final UserChangeService userChangeService;

    /**
     * 1、获取用户座席状态
     *
     */
    @ServiceInfo(name = "获取用户座席状态", webUrl = "/monitor/web/user/getCockpitStatus")
    public HttpResult<GetCockpitStatusDTO> getCockpitStatus() {
        return HttpResult.success(userChangeService.getCockpitStatus());
    }
}