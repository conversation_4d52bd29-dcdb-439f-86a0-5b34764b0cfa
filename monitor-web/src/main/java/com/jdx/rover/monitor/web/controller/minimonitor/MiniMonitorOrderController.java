/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.web.controller.minimonitor;

import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mini.MiniMonitorOrderInfoDTO;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.web.MiniMonitorOrderService;
import com.jdx.rover.monitor.service.web.MonitorOrderService;
import com.jdx.rover.monitor.vo.MiniMonitorOrderListRequestVO;
import com.jdx.rover.monitor.vo.MonitorVerifyCodeRequestVO;
import com.jdx.rover.monitor.vo.MonitorViewOrderRequestVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <p>
 * This is a controller class for mini monitor command info.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/minimonitor/web/order")
public class MiniMonitorOrderController {

  @Autowired
  private MonitorOrderService monitorOrderService;
  @Autowired
  private MiniMonitorOrderService miniMonitorOrderService;

  @PostMapping("/verify_code")
  public HttpResult sendVerifyCode(@Valid @RequestBody(required = true) MonitorVerifyCodeRequestVO verifyCodeVo) {
    return monitorOrderService.sendVerifyCode(verifyCodeVo);
  }

  @PostMapping("/detail")
  public HttpResult viewOrderDetail(@RequestBody(required = true) MonitorViewOrderRequestVO viewOrderVo) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    return monitorOrderService.viewOrderInfo(userName, viewOrderVo);
  }

  @PostMapping("/orderlist")
  public HttpResult getOrderListByPhone(@RequestBody(required = true) MiniMonitorOrderListRequestVO orderListVo) {
    String userName = LoginUtils.getUsername();
    if (StringUtils.isBlank(userName)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_USER_ABSENT.getMessage());
    }
    if (!StringUtils.isNumericSpace(orderListVo.getOrderPhone())) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_ORDER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_ORDER_ABSENT.getMessage());
    }
    List<MiniMonitorOrderInfoDTO> result = miniMonitorOrderService.getHistoryOrderInfoDtoList(orderListVo);
    if (CollectionUtils.isEmpty(result)) {
      return HttpResult.error(MonitorErrorEnum.ERROR_MINI_MONITOR_ORDER_ABSENT.getCode(), MonitorErrorEnum.ERROR_MINI_MONITOR_ORDER_ABSENT.getMessage());
    }
    return HttpResult.success(result);
  }

}

