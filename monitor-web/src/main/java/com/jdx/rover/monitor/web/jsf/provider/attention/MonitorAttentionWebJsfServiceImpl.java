package com.jdx.rover.monitor.web.jsf.provider.attention;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.monitor.dto.MonitorAttentionPhoneDTO;
import com.jdx.rover.monitor.dto.MonitorUserInfoDTO;
import com.jdx.rover.monitor.service.web.MonitorAttentionService;
import com.jdx.rover.monitor.vo.attention.MonitorAttentionVehicleVO;
import com.jdx.rover.monitor.vo.AttentionEventVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionCancelVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionCancelVO;
import com.jdx.rover.monitor.web.jsf.api.attention.MonitorAttentionWebJsfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监控关注接口
 * @author: gulin
 * @date 2025/02/20
 **/
@Service
@RequiredArgsConstructor
public class MonitorAttentionWebJsfServiceImpl extends AbstractProvider<MonitorAttentionWebJsfService> implements MonitorAttentionWebJsfService{

    /**
     * MonitorAttentionService
     */
    private final MonitorAttentionService monitorAttentionService;

    @Override
    @ServiceInfo(name = "关注增加车辆列表", webUrl = "/monitor/web/attention/add_vehicle")
    public HttpResult<Void> addVehicle(MonitorVehicleAttentionAddVO monitorVehicleAttentionAddVO) {
        return monitorAttentionService.addVehicle(monitorVehicleAttentionAddVO);
    }

    @Override
    @ServiceInfo(name = "取消车辆关注列表", webUrl = "/monitor/web/attention/cancel_vehicle")
    public HttpResult<Void> cancelVehicle(MonitorVehicleAttentionCancelVO monitorVehicleAttentionCancelVO) {
        return monitorAttentionService.cancelVehicle(monitorVehicleAttentionCancelVO);
    }

    @Override
    @ServiceInfo(name = "取消用户订阅所有车辆", webUrl = "/monitor/web/attention/cancel_all")
    public HttpResult<Void> cancelAll() {
        return monitorAttentionService.cancelAll();
    }

    @Override
    @ServiceInfo(name = "获取车辆关注用户列表", webUrl = "/monitor/web/attention/getAttentionUser")
    public HttpResult<List<MonitorUserInfoDTO>> getVehicleAttentionUser(VehicleNameBasicVO vehicleNameBasicVo) {
        return monitorAttentionService.getVehicleAttentionUser(vehicleNameBasicVo.getVehicleName());
    }

    @Override
    @ServiceInfo(name = "新增事件关注用户列表", webUrl = "/monitor/web/attention/addAttentionEvent")
    public HttpResult<List<String>> addAttentionEvent(MonitorEventAttentionAddVO attentionAddVO) {
        return monitorAttentionService.addAttentionEvent(attentionAddVO);
    }

    @Override
    @ServiceInfo(name = "删除事件关注用户列表", webUrl = "/monitor/web/attention/deleteAttentionEvent")
    public HttpResult<List<String>> deleteAttentionEvent(MonitorEventAttentionCancelVO attentionCancelVO) {
        return monitorAttentionService.deleteAttentionEvent(attentionCancelVO);
    }

    @Override
    @ServiceInfo(name = "获取事件关注用户列表", webUrl = "/monitor/web/attention/getAttentionEvent")
    public HttpResult<List<String>> getAttentionEvent(AttentionEventVO attentionEventVo) {
        return monitorAttentionService.getAttentionEvent(attentionEventVo.getAttentionEvent());
    }

    @Override
    @ServiceInfo(name = "根据车号查询对应站点负责人手机号", webUrl = "/monitor/web/attention/getStationPersonPhone")
    public HttpResult<MonitorAttentionPhoneDTO> getStationPersonPhone(MonitorAttentionVehicleVO monitorAttentionVehicleVO) {
        return monitorAttentionService.getStationPersonPhone(monitorAttentionVehicleVO);
    }
}