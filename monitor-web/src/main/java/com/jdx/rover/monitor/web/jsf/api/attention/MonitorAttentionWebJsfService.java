package com.jdx.rover.monitor.web.jsf.api.attention;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.MonitorUserInfoDTO;
import com.jdx.rover.monitor.vo.AttentionEventVO;
import com.jdx.rover.monitor.vo.VehicleNameBasicVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorEventAttentionCancelVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionAddVO;
import com.jdx.rover.monitor.vo.attention.MonitorVehicleAttentionCancelVO;
import com.jdx.rover.monitor.dto.MonitorAttentionPhoneDTO;
import com.jdx.rover.monitor.vo.attention.MonitorAttentionVehicleVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * <p>
 * 关注相关 Controller
 * </p>
 *
 * <AUTHOR>
 * @date 2023/09/22
 */
public interface MonitorAttentionWebJsfService {

    /**
     * 添加车辆关注列表
     * @param monitorVehicleAttentionAddVO
     */
    public HttpResult<Void> addVehicle(@Valid MonitorVehicleAttentionAddVO monitorVehicleAttentionAddVO);

    /**
     * 取消车辆关注列表
     * @param monitorVehicleAttentionCancelVO
     * @return
     */
    public HttpResult<Void> cancelVehicle(@Valid MonitorVehicleAttentionCancelVO monitorVehicleAttentionCancelVO);

    /**
     * 取消用户订阅所有车辆
     * @return
     */
    public HttpResult<Void> cancelAll();

    /**
     * 获取车辆关注用户列表
     * @param
     * @return
     */
    public HttpResult<List<MonitorUserInfoDTO>> getVehicleAttentionUser(VehicleNameBasicVO vehicleNameBasicVo);

    /**
     * 新增事件关注用户列表
     * @param
     * @return
     * 根据车号查询对应站点负责人手机号
     */
    public HttpResult<List<String>> addAttentionEvent(MonitorEventAttentionAddVO attentionAddVO);

    /**
     * 删除事件关注用户列表
     * @param
     * @return
     */
    public HttpResult<List<String>> deleteAttentionEvent(MonitorEventAttentionCancelVO attentionCancelVO);

    /**
     * 获取事件关注用户列表
     * @param
     * @return
     */
    public HttpResult<List<String>> getAttentionEvent(AttentionEventVO attentionEventVo);

    /**
     * 根据车号查询对应站点负责人手机号
     */
    HttpResult<MonitorAttentionPhoneDTO> getStationPersonPhone(@NotNull(message = "非法请求") @Valid MonitorAttentionVehicleVO monitorAttentionVehicleVO);
}
