/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorLocationService;
import com.jdx.rover.monitor.vo.MonitorLocationCloudMapVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆人工定位服务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/monitor/web/location")
public class MonitorLocationInitController {
  @Autowired
  private MonitorLocationService locationService;

  /**
   * 上报点云地图以及备选点位
   */
  @PostMapping(value = "/uploadCloudMap")
  public HttpResult uploadVehicleCloudMap(@RequestBody(required = true) MonitorLocationCloudMapVO locationCloudMapVo) {
    return locationService.uploadVehicleMapPose(locationCloudMapVo);
  }

  /**
   * 上报点云地图以及备选点位
   */
  @GetMapping(value = "/getCloudMap/{vehicleName}/{distance}")
  public HttpResult getVehicleCloudMap(@PathVariable("vehicleName") String vehicleName, @PathVariable("distance") Double distance) {
    return locationService.requestCloudMap(vehicleName, distance);
  }

}
