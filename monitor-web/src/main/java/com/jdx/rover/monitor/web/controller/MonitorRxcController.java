package com.jdx.rover.monitor.web.controller;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.service.web.MonitorRxcService;
import com.jdx.rover.monitor.vo.MonitorWordAddVO;
import com.jdx.rover.monitor.vo.MonitorWordDeleteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * This is a controller class for monitor remote xin call function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/monitor/web/rxc")
public class MonitorRxcController {

    @Autowired
    private MonitorRxcService monitorRxcService;

    /**
     * <p>
     * This method helps to return word list.
     * </p>
     *
     * @return mini monitor word DTO list.
     */
    @GetMapping("/word_list")
    public HttpResult getWordList() {
        return monitorRxcService.getWordList();
    }

    /**
     * <p>
     * This method helps to add word detail.
     * </p>
     *
     * @throws IllegalArgumentException if the argument doesn't meet the requirement.
     */
    @PostMapping("/add")
    public HttpResult addWord(@RequestBody MonitorWordAddVO monitorWordAddVO) {
        ParameterCheckUtility.checkNotNull(monitorWordAddVO, "monitorWordAddVO");
        ParameterCheckUtility.checkNotNullNorEmptyAfterTrimming(monitorWordAddVO.getContent(), "monitorWordAddVO#content");
        return monitorRxcService.addWord(monitorWordAddVO);
    }

    /**
     * <p>
     * This method helps to del word detail.
     * </p>
     *
     * @throws IllegalArgumentException if the argument doesn't meet the requirement.
     */
    @PostMapping("/delete")
    public HttpResult deleteWord(@RequestBody MonitorWordDeleteVO monitorWordDeleteVO) {
        ParameterCheckUtility.checkNotNull(monitorWordDeleteVO, "monitorWordDeleteVO");
        ParameterCheckUtility.checkNotNull(monitorWordDeleteVO.getId(), "monitorWordDeleteVO#id");
        return monitorRxcService.deleteWord(monitorWordDeleteVO);
    }
}
