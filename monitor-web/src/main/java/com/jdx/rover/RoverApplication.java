/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <p>
 * This is a static class that provides entry point for whole application.
 * </p>
 *
 * <p>
 * <strong>Thread Safety: </strong> This class is immutable and thread safe.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
@EnableRetry
@EnableAspectJAutoProxy(proxyTargetClass = true)
@ComponentScan(basePackages = {"com.jdx.rover.monitor.domain"
    , "com.jdx.rover.monitor.repository", "com.jdx.rover.monitor.manager"
    , "com.jdx.rover.monitor.service", "com.jdx.rover.monitor.web"})
@EnableFeignClients(basePackages = {"com.jdx.rover.monitor.repository.feign"})
@MapperScan("com.jdx.rover.monitor.repository")
@ImportResource(value = {"classpath:spring-ducc.xml", "classpath:jmq.xml"})
public class RoverApplication {
  /**
   * <p>
   * This is the main entry for our application.
   * </p>
   *
   * @param args the arguments from caller.
   */
  public static void main(String[] args) {
    SpringApplication.run(RoverApplication.class, args);
  }
}
