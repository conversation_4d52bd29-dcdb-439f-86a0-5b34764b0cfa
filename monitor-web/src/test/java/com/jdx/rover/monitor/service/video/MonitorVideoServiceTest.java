package com.jdx.rover.monitor.service.video;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.RoverVideoUrlDTO;
import com.jdx.rover.monitor.dto.accident.VehicleSnapshotDTO;
import com.jdx.rover.monitor.manager.jdme.config.JdmeConfig;
import com.jdx.rover.monitor.service.web.MonitorVideoService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor(onConstructor_ = @Autowired)

@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class MonitorVideoServiceTest {

    private final MonitorVideoService monitorVideoService;

    private final JdmeConfig jdmeConfig;

    @Test
    public void getVehicleSnapshot(){
        monitorVideoService.getVehicleSnapshot("JDZ0002", DateUtil.parse("2024-10-15 13:00:10", "yyyy-MM-dd HH:mm:ss"), "ALL", "liuwenwen52", "123");
    }

    @Test
    public void getVehicleHistorySnapshot(){
        monitorVideoService.getVehicleHistorySnapshot("JDZ0002", DateUtil.parse("2024-10-28 10:00:10", "yyyy-MM-dd HH:mm:ss"), "ALL", "liuwenwen52", "123");
    }

    @Test
    public void test_config() {
        System.out.println();
        System.out.println(jdmeConfig);
    }

    @Test
    public void test_parse() {
        String result = "{\n" +
                "    \"eventType\": null,\n" +
                "    \"errorCode\": 0,\n" +
                "    \"message\": null,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": null,\n" +
                "            \"uuid\": \"4b4aa5a5ecd1423c88cce7a79f43cb70\",\n" +
                "            \"originator\": \"default\",\n" +
                "            \"createTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"createTimeStr\": \"2024-10-23 14:15:05\",\n" +
                "            \"finishTime\": \"2024-10-23T06:15:10.567+00:00\",\n" +
                "            \"finishTimeStr\": \"2024-10-23 14:15:10\",\n" +
                "            \"startTime\": null,\n" +
                "            \"startTimeStr\": null,\n" +
                "            \"endTime\": null,\n" +
                "            \"endTimeStr\": null,\n" +
                "            \"terminalId\": \"JDK8154\",\n" +
                "            \"taskType\": \"SNAPSHOT\",\n" +
                "            \"usageType\": \"DEV\",\n" +
                "            \"taskStatus\": \"FINISH\",\n" +
                "            \"handlerIp\": \"**************\",\n" +
                "            \"ossKey\": null,\n" +
                "            \"internalUrl\": null,\n" +
                "            \"publicUrl\": null,\n" +
                "            \"failType\": \"NONE\",\n" +
                "            \"yn\": null,\n" +
                "            \"direction\": \"FRONT\",\n" +
                "            \"picTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"picTimeStr\": \"2024-10-23 14:15:03\",\n" +
                "            \"picOssUrl\": \"export/Logs/snapshot/2024-10-23/JDK8154/default_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_0_JDK8154_2024-10-23-14-15-05.jpeg\",\n" +
                "            \"publicPicUrl\": \"https://s3.cn-north-1.jdcloud-oss.com/video-stream/export/Logs/snapshot/2024-10-23/JDK8154/default_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_0_JDK8154_2024-10-23-14-15-05.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241023T061510Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241023%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=b3568db10e8183e6ef7952163a55d164d877e87eb573aa8e3e463830ce512d43\",\n" +
                "            \"internalPicUrl\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": null,\n" +
                "            \"uuid\": \"4b4aa5a5ecd1423c88cce7a79f43cb70\",\n" +
                "            \"originator\": \"default\",\n" +
                "            \"createTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"createTimeStr\": \"2024-10-23 14:15:05\",\n" +
                "            \"finishTime\": \"2024-10-23T06:15:09.655+00:00\",\n" +
                "            \"finishTimeStr\": \"2024-10-23 14:15:09\",\n" +
                "            \"startTime\": null,\n" +
                "            \"startTimeStr\": null,\n" +
                "            \"endTime\": null,\n" +
                "            \"endTimeStr\": null,\n" +
                "            \"terminalId\": \"JDK8154\",\n" +
                "            \"taskType\": \"SNAPSHOT\",\n" +
                "            \"usageType\": \"DEV\",\n" +
                "            \"taskStatus\": \"FINISH\",\n" +
                "            \"handlerIp\": \"**************\",\n" +
                "            \"ossKey\": null,\n" +
                "            \"internalUrl\": null,\n" +
                "            \"publicUrl\": null,\n" +
                "            \"failType\": \"NONE\",\n" +
                "            \"yn\": null,\n" +
                "            \"direction\": \"RIGHT\",\n" +
                "            \"picTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"picTimeStr\": \"2024-10-23 14:15:03\",\n" +
                "            \"picOssUrl\": \"ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_1_JDK8154_2024-10-23-14-15-05.jpeg\",\n" +
                "            \"publicPicUrl\": \"https://s3.cn-north-1.jdcloud-oss.com/video-stream/ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_1_JDK8154_2024-10-23-14-15-05.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241023T061509Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241023%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=3a143d085b129f22bbf2c6632f14ad57ec605643053b9a930b93a5add463070f\",\n" +
                "            \"internalPicUrl\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": null,\n" +
                "            \"uuid\": \"4b4aa5a5ecd1423c88cce7a79f43cb70\",\n" +
                "            \"originator\": \"default\",\n" +
                "            \"createTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"createTimeStr\": \"2024-10-23 14:15:05\",\n" +
                "            \"finishTime\": \"2024-10-23T06:15:10.000+00:00\",\n" +
                "            \"finishTimeStr\": \"2024-10-23 14:15:10\",\n" +
                "            \"startTime\": null,\n" +
                "            \"startTimeStr\": null,\n" +
                "            \"endTime\": null,\n" +
                "            \"endTimeStr\": null,\n" +
                "            \"terminalId\": \"JDK8154\",\n" +
                "            \"taskType\": \"SNAPSHOT\",\n" +
                "            \"usageType\": \"DEV\",\n" +
                "            \"taskStatus\": \"FINISH\",\n" +
                "            \"handlerIp\": \"**************\",\n" +
                "            \"ossKey\": null,\n" +
                "            \"internalUrl\": null,\n" +
                "            \"publicUrl\": null,\n" +
                "            \"failType\": \"NONE\",\n" +
                "            \"yn\": null,\n" +
                "            \"direction\": \"BACK\",\n" +
                "            \"picTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"picTimeStr\": \"2024-10-23 14:15:03\",\n" +
                "            \"picOssUrl\": \"ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_2_JDK8154_2024-10-23-14-15-05.jpeg\",\n" +
                "            \"publicPicUrl\": \"https://s3.cn-north-1.jdcloud-oss.com/video-stream/ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_2_JDK8154_2024-10-23-14-15-05.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241023T061510Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241023%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=970dd2e4e9218c6f85212295b60e547f45b33e1228e8ac2ec64c3676a1c5c462\",\n" +
                "            \"internalPicUrl\": null\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": null,\n" +
                "            \"uuid\": \"4b4aa5a5ecd1423c88cce7a79f43cb70\",\n" +
                "            \"originator\": \"default\",\n" +
                "            \"createTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"createTimeStr\": \"2024-10-23 14:15:05\",\n" +
                "            \"finishTime\": \"2024-10-23T06:15:09.668+00:00\",\n" +
                "            \"finishTimeStr\": \"2024-10-23 14:15:09\",\n" +
                "            \"startTime\": null,\n" +
                "            \"startTimeStr\": null,\n" +
                "            \"endTime\": null,\n" +
                "            \"endTimeStr\": null,\n" +
                "            \"terminalId\": \"JDK8154\",\n" +
                "            \"taskType\": \"SNAPSHOT\",\n" +
                "            \"usageType\": \"DEV\",\n" +
                "            \"taskStatus\": \"FINISH\",\n" +
                "            \"handlerIp\": \"**************\",\n" +
                "            \"ossKey\": null,\n" +
                "            \"internalUrl\": null,\n" +
                "            \"publicUrl\": null,\n" +
                "            \"failType\": \"NONE\",\n" +
                "            \"yn\": null,\n" +
                "            \"direction\": \"LEFT\",\n" +
                "            \"picTime\": \"2024-10-23T06:15:05.831+00:00\",\n" +
                "            \"picTimeStr\": \"2024-10-23 14:15:03\",\n" +
                "            \"picOssUrl\": \"ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_3_JDK8154_2024-10-23-14-15-05.jpeg\",\n" +
                "            \"publicPicUrl\": \"https://s3.cn-north-1.jdcloud-oss.com/video-stream/ulldefault_snapshot_4b4aa5a5ecd1423c88cce7a79f43cb70_3_JDK8154_2024-10-23-14-15-05.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20241023T061509Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86399&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20241023%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=ebaa7c283a139a65c10661974e8eb3b1bc73a369f92acdc5a186cb410bfb8c86\",\n" +
                "            \"internalPicUrl\": null\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        RoverVideoUrlDTO<List<VehicleSnapshotDTO>> dto = JsonUtils.readValue(result, new TypeReference<RoverVideoUrlDTO<List<VehicleSnapshotDTO>>>() {
        });
        List<VehicleSnapshotDTO> vehicleSnapshotDTOS = dto.getData();
        System.out.println(JsonUtils.writeValueAsString(vehicleSnapshotDTOS));
    }

    @Test
    public void test_getMuiltVideoList() {
        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add("JDZ0038");
        vehicleNameList.add("JDK8184");
        vehicleNameList.add("JDZ0034");


        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl("http://rover-video-lbs-staging.jd.local/video/lbs/url/play/quantity");
        RoverVideoUrlDTO dto = new RoverVideoUrlDTO();
        try {
            String url = builder.toUriString();
            log.info("Load video url {}", url);
            MonitorVideoServiceTest.MultiVideoUrlVO requestBody = MultiVideoUrlVO.builder().front("true").left("true").right("true").back("true").carIdList(vehicleNameList).build();

            long startTime = System.currentTimeMillis();
            String result = HttpUtil.post(url, JsonUtils.writeValueAsString(requestBody));
            long endTime = System.currentTimeMillis();
            System.out.println("代码耗时: " + (endTime - startTime) + " 毫秒");
            System.out.println("视频返回结果:"+ result);

        } catch (Exception e) {
            log.error("Load multi video url exception", e);
        }
    }

    @Data
    @Builder
    static class MultiVideoUrlVO {
        /**
         * <p>
         * 请求标识
         * </p>
         */
        private String uid = "jdx";

        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String front;


        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String right = "true";


        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String back = "true";

        /**
         * <p>
         * 请求视频
         * </p>
         */
        private String left = "true";

        /**
         * <p>
         * 请求车辆列表
         * </p>
         */
        private List<String> carIdList;
    }
}
