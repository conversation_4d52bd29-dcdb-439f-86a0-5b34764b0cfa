package com.jdx.rover.monitor.manager.video;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.video.MultiVideoBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class VideoManagerTest {

    private final VideoManager videoManager;

    @Test
    public void test_getMultiUrl() {
        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add("JDZ0034");
        List<MultiVideoBO> multiUrl = videoManager.getMultiUrl(vehicleNameList, "true", "true", "true", "true");
        log.info("结果:{}", JsonUtils.writeValueAsString(multiUrl));
    }
}
