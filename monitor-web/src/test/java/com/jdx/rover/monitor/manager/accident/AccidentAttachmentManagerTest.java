package com.jdx.rover.monitor.manager.accident;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.RoverApplication;
import com.jdx.rover.monitor.enums.mobile.AccidentAttachmentSourceEnum;
import com.jdx.rover.monitor.po.AccidentAttachment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@RequiredArgsConstructor(onConstructor_ = @Autowired)

@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class AccidentAttachmentManagerTest {

    private final AccidentAttachmentManager accidentAttachmentManager;

    @Test
    public void selectOrderedListByAccidentNoAndSource(){
        List<AccidentAttachment> accidentAttachments = accidentAttachmentManager.selectOrderedListByAccidentNoAndSource("AD2024072400005", AccidentAttachmentSourceEnum.SAFETY_GROUP.getValue());
        System.out.println(accidentAttachments);
    }
}
