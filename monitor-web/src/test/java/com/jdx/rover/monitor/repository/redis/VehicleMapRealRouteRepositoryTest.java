package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.VehiclePointDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class VehicleMapRealRouteRepositoryTest {

    private final VehicleMapRealRouteRepository vehicleMapRealRouteRepository;

    @Test
    public void test_push() {
        VehiclePointDO vehiclePointDO = new VehiclePointDO();
        vehiclePointDO.setLatitude(2.0);
        vehiclePointDO.setLongitude(4.0);
        vehicleMapRealRouteRepository.push("JD001", vehiclePointDO);
    }

    @Test
    public void test_get() {
        List<VehiclePointDO> result = vehicleMapRealRouteRepository.get("JD001");
        log.info("vehiclePointDO list : {}", JsonUtils.writeValueAsString(result));
    }

    @Test
    public void test_remove() {
        boolean result = vehicleMapRealRouteRepository.remove("JD001");
        log.info("remove result:{}", result);
    }
}
