package com.jdx.rover.monitor.service.web;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.AccidentAnalysisDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.MonthlyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.RecentAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.accidnet.WeeklyAccidentTrendsDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.AccidentBoardDetailVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.MonthlyAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.RecentAccidentTrendsVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.accident.WeeklyAccidentTrendsVO;
import com.jdx.rover.monitor.bo.accident.AccidentWeekCycleBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;
import java.util.List;

//@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class MonitorAccidentBoardServiceTest {

    private final MonitorAccidentBoardService monitorAccidentBoardService;

    @Test
    public void test_getAccidentAnalysis() {
        AccidentAnalysisDTO accidentAnalysis = monitorAccidentBoardService.getAccidentAnalysis();
        log.info("结果:{}", JsonUtils.writeValueAsString(accidentAnalysis));
    }

    @Test
    public void test_recentAccidentTrends() {
        RecentAccidentTrendsVO recentAccidentTrendsVO = new RecentAccidentTrendsVO();
        recentAccidentTrendsVO.setStartDate(DateUtil.parse("2024-11-16"));
        recentAccidentTrendsVO.setEndDate(DateUtil.parse("2024-11-18"));
        List<RecentAccidentTrendsDTO> recentAccidentTrendsDTOS = monitorAccidentBoardService.recentAccidentTrends(recentAccidentTrendsVO);
        System.out.println(JsonUtils.writeValueAsString(recentAccidentTrendsDTOS));
    }

    @Test
    public void test_weeklyAccidentTrends() {
        WeeklyAccidentTrendsVO weeklyAccidentTrendsVO = new WeeklyAccidentTrendsVO();
        weeklyAccidentTrendsVO.setStartDate(DateUtil.parse("2024-11-01"));
        weeklyAccidentTrendsVO.setEndDate(DateUtil.parse("2024-11-27"));
        List<WeeklyAccidentTrendsDTO> weeklyAccidentTrendsDTOS = monitorAccidentBoardService.weeklyAccidentTrends(weeklyAccidentTrendsVO);
        System.out.println(JsonUtils.writeValueAsString(weeklyAccidentTrendsDTOS));
    }

    @Test
    public void test_monthlyAccidentTrends() {
        MonthlyAccidentTrendsVO monthlyAccidentTrendsVO = new MonthlyAccidentTrendsVO();
        monthlyAccidentTrendsVO.setStartDate("2024-10");
        monthlyAccidentTrendsVO.setEndDate("2025-01");
        List<MonthlyAccidentTrendsDTO> monthlyAccidentTrendsDTOS = monitorAccidentBoardService.monthlyAccidentTrends(monthlyAccidentTrendsVO);
        System.out.println(JsonUtils.writeValueAsString(monthlyAccidentTrendsDTOS));
    }

    @Test
    public void test_getWeeklyCycles() {
        Date startDate = DateUtil.parse("2024-11-04");
        Date endDate = DateUtil.parse("2025-1-04");
        List<AccidentWeekCycleBO> weeklyCycles = monitorAccidentBoardService.getWeeklyCycles(startDate, endDate);
        for (AccidentWeekCycleBO accidentWeekCycleBO : weeklyCycles) {
            log.info("开始日期:{} -- 结束日期:{}", accidentWeekCycleBO.getStartTime(), accidentWeekCycleBO.getEndTime());
        }

        log.info("事故周期拼装结果:{}",JsonUtils.writeValueAsString(weeklyCycles));
    }

    @Test
    public void test_getAccidentDetail() {
        AccidentBoardDetailVO accidentBoardDetailVO = new AccidentBoardDetailVO();
        accidentBoardDetailVO.setType("MONTH_TOTAL_ACCIDENT");
        monitorAccidentBoardService.getAccidentDetail(accidentBoardDetailVO);
    }
}
