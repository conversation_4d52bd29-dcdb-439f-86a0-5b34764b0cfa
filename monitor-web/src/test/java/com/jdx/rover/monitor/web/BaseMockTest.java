/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web;

import com.jdx.rover.common.utils.login.LoginUtils;
import com.jdx.rover.monitor.web.helper.JdbcTestHelper;
import com.jdx.rover.monitor.web.helper.RedisTestHelper;
import com.jdx.rover.monitor.web.helper.TestConstants;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 所有测试类必须继承此类,否则会有重新初始化上下文,导致redis被重新启动不成功
 * 公共的mock基类,防止MockBean初始化多次spring boot上下文
 * <a href="https://blog.csdn.net/zlx312/article/details/101985267">...</a>
 * <a href="https://github.com/spring-projects/spring-boot/issues/7174">...</a>
 * <p>
 * 如果您正在编写单元测试，请不要加载Spring上下文文件并模拟您要测试的组件的所有依赖项。
 * 如果您正在编写集成测试，请不要模拟依赖项，让框架加载上下文并连接依赖项。
 * https://www.thinbug.com/q/48560659
 *
 * <AUTHOR>
 * @date 2024/01/25
 */

@SpringBootTest
public class BaseMockTest {
    /**
     * 数据源
     */
    @Resource
    private DataSource dataSource;

    /**
     * 用户名静态方法
     */
    public static MockedStatic getUsernameStatic;

    @BeforeAll
    public static void beforeAllBase() {
        getUsernameStatic = Mockito.mockStatic(LoginUtils.class);
        Mockito.when(LoginUtils.getUsername()).thenReturn(TestConstants.USER_NAME);
    }

    @BeforeEach
    public void beforeEachBase() {
        MockitoAnnotations.openMocks(this);
        RedisTestHelper.setUpTestData();
        JdbcTestHelper.setUpTestData(new JdbcTemplate(dataSource));
    }

    @AfterAll
    public static void afterAllBase() {
        getUsernameStatic.close();
    }
}
