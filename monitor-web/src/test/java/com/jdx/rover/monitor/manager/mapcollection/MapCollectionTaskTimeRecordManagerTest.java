package com.jdx.rover.monitor.manager.mapcollection;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.monitor.po.mapcollection.MapCollectionTaskTimeRecord;
import com.jdx.rover.monitor.po.mapcollection.json.RealRoutePoint;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class MapCollectionTaskTimeRecordManagerTest {

    private final MapCollectionTaskTimeRecordManager mapCollectionTaskTimeRecordManager;

    @Test
    public void test_createRecord() {
        mapCollectionTaskTimeRecordManager.createRecord(1, "JD001", "JSC001", "liuwenwen52", new Date());
    }

    @Test
    public void test_updateRecord() {
        List<RealRoutePoint> routePointList = new ArrayList<>();
        for(int i = 0; i < 10; i++) {
            RealRoutePoint realRoutePoint = new RealRoutePoint();
            realRoutePoint.setLatitude(1.0);
            realRoutePoint.setLongitude(1.0);
            routePointList.add(realRoutePoint);
        }
        mapCollectionTaskTimeRecordManager.updateRecord(2.0, routePointList, "liuwenwen52", 1, "JD001", false);
    }

    @Test
    public void test_select() {
        MapCollectionTaskTimeRecord one = mapCollectionTaskTimeRecordManager.lambdaQuery().eq(MapCollectionTaskTimeRecord::getTaskId, 2).one();
        System.out.println(one);
    }
}
