package com.jdx.rover.monitor.manager.vehicle;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleStorageSpaceInfoDO;
import com.jdx.rover.monitor.dto.VehicleStorageResultDTO;
import com.jdx.rover.monitor.web.BaseMockTest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class VehicleStorageManagerTest extends BaseMockTest {

    private final VehicleStorageManager vehicleStorageManager;

    @Test
    public void test_getVehicleStorage() {
        VehicleStorageSpaceInfoDO vehicleStorage = vehicleStorageManager.getVehicleStorage("JD001");
        log.info("获取单车储存空间:{}", vehicleStorage);
        VehicleStorageSpaceInfoDO vehicleStorage1 = vehicleStorageManager.getVehicleStorage("JD004");
        log.info("获取单车储存空间:{}", vehicleStorage1);
    }

    @Test
    public void test_getVehicleStorageListMap() {
        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add("JD001");
        vehicleNameList.add("JD002");
        Map<String, VehicleStorageSpaceInfoDO> vehicleStorageListMap = vehicleStorageManager.getVehicleStorageListMap(vehicleNameList);
        log.info("批量获取车辆储存空间:{}", JsonUtils.writeValueAsString(vehicleStorageListMap));
    }

    @Test
    public void test_serial() {
        VehicleStorageResultDTO vehicleStorageInfoDTO = new VehicleStorageResultDTO();
        vehicleStorageInfoDTO.setTotal(10);
        VehicleStorageResultDTO.VehicleStorage vehicleStorage = new VehicleStorageResultDTO.VehicleStorage();
        vehicleStorage.setVehicleName("JD001");
        vehicleStorage.setOnline(true);
        VehicleStorageResultDTO.VehicleDiskInfo vehicleDiskInfo = new VehicleStorageResultDTO.VehicleDiskInfo();
        vehicleDiskInfo.setFree(1.0);
        vehicleDiskInfo.setTotal(2.0);
        vehicleStorage.setVehicleDiskInfoVo(vehicleDiskInfo);
        List<VehicleStorageResultDTO.VehicleStorage> rows = new ArrayList<>();
        rows.add(vehicleStorage);
        vehicleStorageInfoDTO.setRows(rows);
        String vehicleStorageInfo = JsonUtils.writeValueAsString(vehicleStorageInfoDTO);
        VehicleStorageResultDTO vehicleStorageResultDTO = JsonUtils.readValue(vehicleStorageInfo, new TypeReference<VehicleStorageResultDTO>(){});
        log.info("车辆存储空间:{}", vehicleStorageResultDTO);
    }
}
