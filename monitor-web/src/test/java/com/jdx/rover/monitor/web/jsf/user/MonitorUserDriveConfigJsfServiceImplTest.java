/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web.jsf.user;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.enums.HttpCodeEnum;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.user.MonitorUserDriveConfigJsfDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.GetUserDriveConfigJsfVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.user.SaveUserDriveConfigJsfVO;
import com.jdx.rover.monitor.service.UserDriveConfigService;
import com.jdx.rover.monitor.web.BaseMockTest;
import com.jdx.rover.monitor.web.helper.TestConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 注
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverApplication.class)
@Slf4j
class MonitorUserDriveConfigJsfServiceImplTest extends BaseMockTest {
    /**
     * 用户配置服务接口
     */
    private final MonitorUserDriveConfigJsfServiceImpl monitorUserDriveConfigJsfService;

    @Test
    void saveUserDriveConfig() {
        Integer speedLimit = 5;

        GetUserDriveConfigJsfVO getUserDriveConfigJsfVO = new GetUserDriveConfigJsfVO();
        getUserDriveConfigJsfVO.setUserName(TestConstants.USER_NAME);
        HttpResult<MonitorUserDriveConfigJsfDTO> resultDbBefore = monitorUserDriveConfigJsfService.getUserDriveConfig(getUserDriveConfigJsfVO);
        Assertions.assertThat(resultDbBefore.getData().getUserName()).isEqualTo(TestConstants.USER_NAME);
        Assertions.assertThat(resultDbBefore.getData().getSpeedLimit()).isEqualTo(UserDriveConfigService.SPEED_LIMIT_DEFAULT);


        SaveUserDriveConfigJsfVO saveUserDriveConfigJsfVO = new SaveUserDriveConfigJsfVO();
        saveUserDriveConfigJsfVO.setUserName(TestConstants.USER_NAME);
        saveUserDriveConfigJsfVO.setSpeedLimit(speedLimit);
        HttpResult<MonitorUserDriveConfigJsfDTO> result = monitorUserDriveConfigJsfService.saveUserDriveConfig(saveUserDriveConfigJsfVO);
        Assertions.assertThat(result.getCode()).isEqualTo(HttpCodeEnum.OK.getValue());

        HttpResult<MonitorUserDriveConfigJsfDTO> resultDb = monitorUserDriveConfigJsfService.getUserDriveConfig(getUserDriveConfigJsfVO);
        Assertions.assertThat(resultDb.getData().getUserName()).isEqualTo(TestConstants.USER_NAME);
        Assertions.assertThat(resultDb.getData().getSpeedLimit()).isEqualTo(speedLimit);


    }

    @Test
    void getUserDriveConfig() {
        // 校验不存在的用户
        {
            GetUserDriveConfigJsfVO getUserDriveConfigJsfVO = new GetUserDriveConfigJsfVO();
            getUserDriveConfigJsfVO.setUserName(TestConstants.USER_NAME_NOT_EXIST);
            HttpResult<MonitorUserDriveConfigJsfDTO> result = monitorUserDriveConfigJsfService.getUserDriveConfig(getUserDriveConfigJsfVO);
            Assertions.assertThat(result.getCode()).isEqualTo(HttpCodeEnum.OK.getValue());
            Assertions.assertThat(result.getData().getUserName()).isEqualTo(TestConstants.USER_NAME_NOT_EXIST);
            Assertions.assertThat(result.getData().getSpeedLimit()).isEqualTo(15);
        }
    }
}