package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.monitor.entity.vehicle.VehicleStatusDO;
import com.jdx.rover.monitor.enums.OnlineStatusEnum;
import com.jdx.rover.monitor.enums.mapcollection.CollectionModeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class VehicleStatusRepositoryTest {

    private final VehicleStatusRepository vehicleStatusRepository;


    @Test
    public void test_putMapValue() {
        vehicleStatusRepository.putMapValue("JD001", VehicleStatusDO::getCollectionMode, CollectionModeEnum.COLLECTION.getCollectionMode());
        vehicleStatusRepository.putMapValue("JD001", VehicleStatusDO::getGuardianOnline, OnlineStatusEnum.ONLINE.name());
    }

}
