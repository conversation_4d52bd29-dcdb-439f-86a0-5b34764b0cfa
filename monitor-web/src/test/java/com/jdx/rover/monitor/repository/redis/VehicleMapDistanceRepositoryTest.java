package com.jdx.rover.monitor.repository.redis;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleDistanceDO;
import com.jdx.rover.monitor.web.BaseMockTest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class VehicleMapDistanceRepositoryTest extends BaseMockTest {

    private final VehicleMapDistanceRepository vehicleMapDistanceRepository;

    @Test
    public void test_set() {
        VehicleDistanceDO vehicleDistanceDO = new VehicleDistanceDO();
        vehicleDistanceDO.setLatitude(1.0);
        vehicleDistanceDO.setLongitude(1.0);
        vehicleDistanceDO.setDistance(2.0);
        vehicleMapDistanceRepository.set("JD002", vehicleDistanceDO);
    }

    @Test
    public void test_get() {
        VehicleDistanceDO vehicleDistanceDO = vehicleMapDistanceRepository.get("JD001");
        log.info("vehicleDistanceDO:{}", JsonUtils.writeValueAsString(vehicleDistanceDO));
    }

    @Test
    public void test_listMap() {
        List<String> vehicleNameList = new ArrayList<>();
        vehicleNameList.add("JD001");
        vehicleNameList.add("JD002");
        Map<String, VehicleDistanceDO> vehicleDistanceDOMap = vehicleMapDistanceRepository.listMap(vehicleNameList);
        log.info("list map result : {}", JsonUtils.writeValueAsString(vehicleDistanceDOMap));
    }
}
