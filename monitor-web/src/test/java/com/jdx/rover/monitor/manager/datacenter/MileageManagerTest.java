package com.jdx.rover.monitor.manager.datacenter;

import cn.hutool.core.date.DateUtil;
import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.HistoryMileageQueryJsfDTO;
import com.jdx.rover.datacenter.domain.dto.warehouse.mileage.VehicleMileageQueryJsfDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;
import java.util.List;

@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class MileageManagerTest {

    private final  MileageManager mileageManager;

    @Test
    public void test_vehicleMileageQuery() {
        List<VehicleMileageQueryJsfDTO> vehicleMileageQueryJsfDTOS = mileageManager.vehicleMileageQuery(DateUtil.parse("2024-12-02 00:00:00"), new Date());
        log.info("结果:{}", JsonUtils.writeValueAsString(vehicleMileageQueryJsfDTOS));
    }

    @Test
    public void test_historyMileageQuery() {
        List<HistoryMileageQueryJsfDTO> historyMileageQueryJsfDTOS = mileageManager.historyMileageQuery(DateUtil.parse("2024-11-06 00:00:00"), new Date());
        log.info("结果:{}", JsonUtils.writeValueAsString(historyMileageQueryJsfDTOS));
    }
}
