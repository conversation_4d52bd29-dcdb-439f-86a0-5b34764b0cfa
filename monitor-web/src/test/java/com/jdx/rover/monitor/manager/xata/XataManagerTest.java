package com.jdx.rover.monitor.manager.xata;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dataobject.mapcollection.VehicleStorageSpaceInfoDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class XataManagerTest {

    private final XataManager xataManager;

    @Test
    public void test_queryVehicleStorage() {
        String vehicleName = "JDZ0038";
        VehicleStorageSpaceInfoDO vehicleStorageSpaceInfoDO = xataManager.queryVehicleStorage(vehicleName);
        log.info("结果:{}", JsonUtils.writeValueAsString(vehicleStorageSpaceInfoDO));
    }

}
