/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.web.helper;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.jdbc.JdbcTestUtils;

/**
 * jdbc测试帮助类
 *
 * <AUTHOR>
 * @date 2024/01/25
 */
public final class JdbcTestHelper {
    /**
     * 初始化测试数据
     */
    public static void setUpTestData(JdbcTemplate jdbcTemplate) {
        deleteFromTables(jdbcTemplate);
        initData(jdbcTemplate);
    }

    /**
     * 清除表数据.
     */
    private static void deleteFromTables(JdbcTemplate jdbcTemplate) {
        // 执行删除语句
        JdbcTestUtils.deleteFromTableWhere(jdbcTemplate, "user_drive_config", "user_name=?", TestConstants.USER_NAME);
    }

    /**
     * 插入初始化数据.
     */
    private static void initData(JdbcTemplate jdbcTemplate) {
//        jdbcTemplate.execute("INSERT INTO `monitor_user_operation_log` VALUES (1,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:07.227','test','2022-03-10 03:03:07','2022-03-10 03:03:07',NULL),(2,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:16.161','test','2022-03-10 03:03:16','2022-03-10 03:03:16',NULL),(3,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:16.838','test','2022-03-10 03:03:16','2022-03-10 03:03:16',NULL),(4,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:17.306','test','2022-03-10 03:03:17','2022-03-10 03:03:17',NULL),(5,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:17.704','test','2022-03-10 03:03:17','2022-03-10 03:03:17',NULL),(6,'JD0006',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:03:18.037','test','2022-03-10 03:03:18','2022-03-10 03:03:18',NULL),(7,'JD0007',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:10:23.673','test','2022-03-10 03:10:23','2022-03-10 03:10:23',NULL),(8,'JD0007',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:10:25.981','test','2022-03-10 03:10:25','2022-03-10 03:10:25',NULL),(9,'JD0007',1,'北京',32,'三洋职场','REMOTE_REQUEST_EMERGENCY_STOP','Takeover: enable emergency stop','2022-03-10 03:10:26.634','test','2022-03-10 03:10:26','2022-03-10 03:10:26',NULL);");
    }
}