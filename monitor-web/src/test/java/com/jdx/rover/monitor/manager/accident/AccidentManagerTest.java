package com.jdx.rover.monitor.manager.accident;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailBO;
import com.jdx.rover.monitor.bo.accident.AccidentBoardDetailQueryBO;
import com.jdx.rover.monitor.enums.mobile.PreliminaryAccidentLevelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@ActiveProfiles("test")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {RoverApplication.class})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class AccidentManagerTest {

    private final AccidentManager accidentManager;

    @Test
    public void test_getAccidentBoardDetailList() {
        AccidentBoardDetailQueryBO accidentBoardDetailQueryBO = new AccidentBoardDetailQueryBO();
        accidentBoardDetailQueryBO.setAccidentLevel(PreliminaryAccidentLevelEnum.HIGH_RISK.getValue());
        List<AccidentBoardDetailBO> accidentBoardDetailList = accidentManager.getBaseMapper().getAccidentBoardDetailList(accidentBoardDetailQueryBO);
        System.out.println(JsonUtils.writeValueAsString(accidentBoardDetailList));
    }
}
