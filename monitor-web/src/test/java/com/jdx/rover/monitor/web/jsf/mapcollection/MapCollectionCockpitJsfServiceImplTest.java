package com.jdx.rover.monitor.web.jsf.mapcollection;

import com.jdx.rover.RoverApplication;
import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.monitor.dto.mapcollection.FuzzySearchVehicleDTO;
import com.jdx.rover.monitor.dto.mapcollection.MapVehiclePageDTO;
import com.jdx.rover.monitor.vo.mapcollection.AssociateTaskVO;
import com.jdx.rover.monitor.vo.mapcollection.FuzzySearchVehicleVO;
import com.jdx.rover.monitor.vo.mapcollection.MapVehiclePageVO;
import com.jdx.rover.monitor.web.BaseMockTest;
import com.jdx.rover.monitor.web.jsf.provider.mapcollection.MapCollectionCockpitJsfServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@RequiredArgsConstructor(onConstructor_ = @Autowired)
@SpringBootTest(classes = RoverApplication.class)
@Slf4j
public class MapCollectionCockpitJsfServiceImplTest extends BaseMockTest {

    private final MapCollectionCockpitJsfServiceImpl mapCollectionCockpitJsfService;

    @Test
    public void test_getVehiclePage() {
        MapVehiclePageVO mapVehiclePageVO = new MapVehiclePageVO();
        mapVehiclePageVO.setPageNum(2);
        mapVehiclePageVO.setPageSize(2);
        HttpResult<PageDTO<MapVehiclePageDTO>> vehiclePage = mapCollectionCockpitJsfService.getVehiclePage(mapVehiclePageVO);
        log.info("vehiclePage:{}", JsonUtils.writeValueAsString(vehiclePage));
    }

    @Test
    public void test_fuzzySearchVehicle() {
        FuzzySearchVehicleVO fuzzySearchVehicleVO = new FuzzySearchVehicleVO();
        fuzzySearchVehicleVO.setSearchName("天津");
        HttpResult<List<FuzzySearchVehicleDTO>> searchVehicleList = mapCollectionCockpitJsfService.fuzzySearchVehicle(fuzzySearchVehicleVO);
        log.info("searchVehicleList:{}", JsonUtils.writeValueAsString(searchVehicleList));
    }

    @Test
    public void test_associateTask() {
        AssociateTaskVO associateTaskVO = new AssociateTaskVO();
        associateTaskVO.setTaskId(1);
        associateTaskVO.setVehicleName("JD001");
        associateTaskVO.setAction(0);
        associateTaskVO.setCockpitNumber("JSC001");
        mapCollectionCockpitJsfService.associateTask(associateTaskVO);
    }
}
