spring:
  application:
    name: rover-monitor-web
  profiles:
    active: "test"
  cloud:
    nacos:
      discovery:
        group: ${spring.profiles.active}_group
  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 1
            max-active: 15
            max-wait: 60000 #缺省使用公平锁
            min-idle: 1 #最小连接池数量
            max-evictable-idle-time-millis: 600000 #连接最大生存时间, ms
            min-evictable-idle-time-millis: 300000 #连接最小生存时间, ms，默认300s
            test-on-borrow: false
            test-on-return: false #默认值,不配置
            test-while-idle: true
            validation-query: SELECT 1 FROM DUAL
            validation-query-timeout: 10000
  data:
    redis:
      database: 1
      lettuce:
        pool:
          max-active: 500
          max-wait: -1ms
          min-idle: 0
          max-idle: 50
  kafka:
    producer:
      retries: 5
    consumer:
      enable-auto-commit: true
      auto-commit-interval: 1000
      group-id: ${spring.application.name}
  cache:
    type: caffeine
    caffeine:
      spec: "maximumSize=500,expireAfterWrite=600s"
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss #jackson日期格式化,用于@RestController返回
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:sqlmap/*.xml
  type-handlers-package: com.jdx.rover.monitor.domain.handler
  configuration:
    auto-mapping-behavior: FULL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
assist-jira-record:
  projectKey: JDXAD
  issueTypeId: 1
  priorityId: 3
  defectTypeId: 11505
  defectTypeValue: 用户体验问题/优化提升
  severityId: 11511
  severityValue: Major（一般缺陷）
  discoveryPhaseId: 12005
  discoveryPhaseValue: 用户反馈
  discoverSituationId: 11529
  discoverSituationValue: 否
  componentId: 27791
  scenario: 社会道路
  useCase: 运营
  versionId: 22084
  versionValue: unkown
logging:
  config: classpath:log4j2.xml
#logging:
#  level:
#    org.springframework: DEBUG
#  #    org.springframework.cloud.gateway: DEBUG
#  #    org.springframework.security: DEBUG
#  file:
#    name: /export/logs/${spring.application.name}/${spring.application.name}.log  #日志文件名称
#    max-size: 100MB #最大文件大小,超过就转历史
#    max-history: 365 #保存最大份数
#  pattern:
#    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%t]){faint} %clr(%-40.40logger{39}){cyan}[%line] %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}"
#  #"${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
s3:
  access-key: 85C76AB2AB89077E8500CB6021A81EC7
  secret-key: EEDD3436DF9B9ED075A39DEE468F66A2
  endpoint: https://s3-internal.cn-north-1.jdcloud-oss.com
  out-endpoint: https://s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  signer-override: AWSS3V4SignerType
jira-service:
  userName: org.jdx.monitor1
  passWord: Zhenxinaini(521)
project:
  local:
    cache:
      localCacheEvictTopic: local:cache:evict
liteflow:
  ruleSource: config/flow.xml
rover-map:
  rover-map-bucket-name: rover-map-test
  attachment-bucket-name: rover-jira
#京ME消息推送
jdme:
  api:
    app-access-token: /open-api/auth/v1/app_access_token
    team-access-token: /open-api/auth/v1/team_access_token
    create-group: /open-api/suite/v1/timline/createGroup
    add-group-member: /open-api/suite/v1/timline/addGroupMember
    send-robot-msg: /open-api/suite/v1/timline/sendRobotMsg
    send-jue-msg: /open-api/suite/v1/timline/sendJUEMsg
  robot:
    id: 00_ee824b500481492c
    pin: app.w8jfe2tb
    env: PROD
    host: http://openme.jd.local
    appKey: zhinengche
    appSecret: 124A2644A08669F8
    openTeamId: f65e9fb91c8cca695105735ca51ce2e9
    cardTemplateId: templateMsgCard
jsf:
  enableProvider: false
  enableConsumer: false
  provider:
    validation: true