/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 追踪事件枚举.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum MonitorTrackingEventEnum {
  // 远程接管
  REMOTE_REQUEST_EMERGENCY_STOP("REMOTE_REQUEST_EMERGENCY_STOP", "远程急停", "PC_MONITOR1127951046"),
  REMOTE_REQUEST_RECOVERY("REMOTE_REQUEST_RECOVERY", "远程恢复", "PC_MONITOR1127955162"),
  REMOTE_REQUEST_REMOTE_RESTART("REMOTE_REQUEST_REMOTE_RESTART", "软件重启", "PC_MONITOR1114500009"),
  REMOTE_REQUEST_AS_ARRIVED("REMOTE_REQUEST_AS_ARRIVED", "视同到达","PC_MONITOR1072094484"),
  REMOTE_REQUEST_PASS_LIGHT("REMOTE_REQUEST_PASS_LIGHT", "通过红绿灯路口", "PC_MONITOR710152869"),
  REMOTE_REQUEST_CANCEL_LIGHT("REMOTE_REQUEST_CANCEL_LIGHT", "取消通过红绿灯路口", "PC_MONITOR710152869"),
  REMOTE_REQUEST_RELIEVE_BUTTON_STOP("REMOTE_REQUEST_RELIEVE_BUTTON_STOP", "解除按钮拍停", "PC_MONITOR1104096533"),
  REMOTE_CONTROL_POWER_REBOOT("REMOTE_CONTROL_POWER_REBOOT", "断电重启", "PC_MONITOR602301445"),
  REMOTE_CONTROL_POWER_OFF("REMOTE_CONTROL_POWER_OFF",  "远程下电", "PC_MONITOR602301445"),
  // 规划
  PNC_PLANNING("PNC_PLANNING", "pnc重新规划路线", "PC_MONITOR454691430"),
  // 连接
  VEHICLE_CONNECT("VEHICLE_CONNECT", "车辆连接状态变化", "PC_MONITOR687693936"),
  // 告警
  VEHICLE_CRASH("VEHICLE_CRASH", "bumper碰撞", "PC_MONITOR00983534"),
  VEHICLE_GUARDIAN_CRASH("VEHICLE_GUARDIAN_CRASH", "guardian碰撞", "PC_MONITOR00983534"),

  LOW_PRESSURE("LOW_PRESSURE", "胎压异常", "PC_MONITOR1003659443"),
  SENSOR_ERROR("SENSOR_ERROR", "传感器异常", "PC_MONITOR142217599"),
  //LOW_BATTERY("LOW_BATTERY","电量低", "PC_MONITOR30012244"),

  VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "感知看灯失败", "PC_MONITOR206637950"),
  VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "看灯规划失败", "PC_MONITOR331036710"),
  VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "按钮停车", "PC_MONITOR792543247"),
  VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "看灯点被占", "PC_MONITOR1113326922"),
  VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "路口遇阻", "PC_MONITOR1104276904"),
  VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "异常停车", "PC_MONITOR748430464"),
  VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "安全接管停车", "PC_MONITOR1561849765"),
  VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "停靠点被占", "PC_MONITOR854619530"),
  VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "停靠规划失败", "PC_MONITOR901042150"),
  VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "遇阻停车", "PC_MONITOR1137964126"),
  PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "无保护左转", "PC_MONITOR1097233613"),
  REMOTE_NO_SIGNAL_INTERSECTION("REMOTE_NO_SIGNAL_INTERSECTION", "无保护左转", "PC_MONITOR710152869"),
  STOP_CROSSING_BAR("STOP_CROSSING_BAR", "过杆遇阻","PC_MONITOR1123107699"),
  GATE_STUCK("GATE_STUCK", "过门遇阻", "PC_MONITOR1134580117"),
  ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", "关注预警", "PC_MONITOR2006157166"),

  MANUAL_REPORT_ALARM("MANUAL_REPORT_ALARM", "人工告警", "PC_MONITOR624211975"),

  VEHICLE_VIDEO_MODE_CHANGE("VEHICLE_VIDEO_MODE_CHANGE", "视频清晰度", "PC_MONITOR1551781776"),

  VEHICLE_STEER_ZERO_FAULT("VEHICLE_STEER_ZERO_FAULT", "车辆零偏", "PC_MONITOR1600664341"),
//  VEHICLE_SCENE_SIGNAL_ABNORMAL("VEHICLE_SCENE_SIGNAL_ABNORMAL", "定位异常", "PC_MONITOR718886313"),
  VEHICLE_SCENE_SIGNAL_NORMAL("VEHICLE_SCENE_SIGNAL_NORMAL", "定位异常恢复", "PC_MONITOR1508283476"),
  VEHICLE_DRIVING_LANE("VEHICLE_DRIVING_LANE", "机动车道行驶", "PC_MONITOR1984306629"),

  //平行驾驶
  REMOTE_DRIVE_ENTER_TAKE_OVER("REMOTE_DRIVE_ENTER_TAKE_OVER","进入平行驾驶接管", "PC_MONITOR1380247415"),
  REMOTE_DRIVE_EXIT_TAKE_OVER("REMOTE_DRIVE_EXIT_TAKE_OVER","退出平行驾驶接管","PC_MONITOR533782663"),

  //JIRA回放
  VEHICLE_JIRA_REPORT("VEHICLE_JIRA_REPORT", "JIRA提报", "PC_MONITOR41813251"),
  BOOT_FAIL("BOOT_FAIL", "开机失败", "PC_MONITOR750656462"),
  BOOT_TIMEOUT("BOOT_TIMEOUT", "开机过久", "PC_MONITOR751073560"),
  BOOT_ABNORMAL("BOOT_ABNORMAL", "异常重启", "PC_MONITOR748934648"),
  BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", "异常重启失败", "PC_MONITOR319918604"),

  PARK_HARD("PARK_HARD", "停靠困难", "PC_MONITOR650829650"),
  PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", "胎压骤减", "PC_MONITOR1004129512"),
  CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", "CPU温度过高", "PC_MONITOR419153878"),
  LOCALIZATION_ERROR("LOCALIZATION_ERROR", "定位异常", "PC_MONITOR718886313"),
  ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", "Routing规划失败", "PC_MONITOR639014152"),

  ROBOT_DEVICE_DRIVE_LOG("ROBOT_DEVICE_DRIVE_LOG", "机器人行走日志", "PC_MONITOR632884290"),
  ROBOT_DEVICE_TASK_LOG("ROBOT_DEVICE_TASK_LOG", "机器人任务执行", "PC_MONITOR181275735"),

  ;

  private String value;

  private String name;

  private String code;

  public static MonitorTrackingEventEnum of(final String value) {
    for (MonitorTrackingEventEnum itemEnum : MonitorTrackingEventEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }

}
