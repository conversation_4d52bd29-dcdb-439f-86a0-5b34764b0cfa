/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 车辆告警分类.
 *
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum VehicleAlarmCategoryEnum {
  // 失联
  GUARDIAN_LOST("GUARDIAN_LOST", "失联", "GUARDIAN_LOST", 101),

  // 碰撞
  VEHICLE_CRASH("VEHICLE_CRASH", "碰撞", "VEHICLE_CRASH", 201),

  // 人工告警
  MANUAL_REPORT("MANUAL_REPORT", "人工告警", "MANUAL", 301),

  // 传感器异常
  LOCALIZATION_ERROR("LOCALIZATION_ERROR", "定位异常", "SENSOR_ERROR", 401),
  SENSOR_ERROR("SENSOR_ERROR", "传感器异常", "SENSOR_ERROR", 402),

  // 运营告警

  // 开机异常
  BOOT_ABNORMAL_FAIL("BOOT_ABNORMAL_FAIL", "异常重启失败", "BOOT", 601),
  BOOT_ABNORMAL("BOOT_ABNORMAL", "异常重启", "BOOT", 602),
  BOOT_FAIL("BOOT_FAIL", "开机失败", "BOOT", 603),
  BOOT_TIMEOUT("BOOT_TIMEOUT", "开机过久", "BOOT", 604),

  // 路口异常
  PASS_NO_SIGNAL_INTERSECTION("PASS_NO_SIGNAL_INTERSECTION", "无保护左转", "INTERSECTION_ABNORMAL", 701),
  VEHICLE_STOP_TRAFFICLIGHT_TAKEUP("VEHICLE_STOP_TRAFFICLIGHT_TAKEUP", "看灯点被占", "INTERSECTION_ABNORMAL", 702),
  VEHICLE_STOP_TRAFFICLIGHT_FAIL("VEHICLE_STOP_TRAFFICLIGHT_FAIL", "感知看灯失败", "INTERSECTION_ABNORMAL", 703),
  VEHICLE_STOP_TRAFFICLIGHT_ADJUST("VEHICLE_STOP_TRAFFICLIGHT_ADJUST", "路口看灯调整失败", "INTERSECTION_ABNORMAL", 704),
  VEHICLE_STOP_INTERSECTION_STUCK("VEHICLE_STOP_INTERSECTION_STUCK", "路口遇阻", "INTERSECTION_ABNORMAL", 705),

  // 停车
  VEHICLE_STOP_BUTTON("VEHICLE_STOP_BUTTON", "按钮停车", "STOP", 801),
  GATE_STUCK("GATE_STUCK", "过门遇阻", "STOP", 802),
  INTERSECTION_OVER_LINE("INTERSECTION_OVER_LINE", "路口压线", "STOP", 803),
  VEHICLE_STOP_STOP_TAKEUP("VEHICLE_STOP_STOP_TAKEUP", "停靠点被占", "STOP", 804),
  VEHICLE_STOP_STOP_ADJUST("VEHICLE_STOP_STOP_ADJUST", "停靠点调整失败", "STOP", 805),
  VEHICLE_STOP_FATAL("VEHICLE_STOP_FATAL", "异常停车", "STOP", 806),
  CPU_HIGH_TEMPERATURE("CPU_HIGH_TEMPERATURE", "CPU温度过高", "STOP", 807),
  ROUTING_PLAN_FAIL("ROUTING_PLAN_FAIL", "Routing规划失败", "STOP", 808),
  VEHICLE_STOP_GUARDIAN("VEHICLE_STOP_GUARDIAN", "安全接管停车", "STOP", 809),
  PARK_HARD("PARK_HARD", "停靠困难", "STOP", 810),
  VEHICLE_STOP_TIMEOUT("VEHICLE_STOP_TIMEOUT", "遇阻停车", "STOP", 811),

  // 区域预警
  ATTENTION_REGION_WARN("ATTENTION_REGION_WARN", "关注预警", "REGION", 901),
  PARKING_REGION_WARN("PARKING_REGION_WARN", "停车预警", "REGION", 902),

  // 电量
  LOW_BATTERY("LOW_BATTERY", "电量低","LOW_BATTERY", 1001),

  // 胎压
  TIRE_PUNCTURE("TIRE_PUNCTURE", "爆胎/扎胎", "LOW_PRESSURE", 1101),
  PRESSURE_SHARP_DECREASE("PRESSURE_SHARP_DECREASE", "胎压骤减", "LOW_PRESSURE", 1102),
  LOW_PRESSURE("LOW_PRESSURE", "胎压异常", "LOW_PRESSURE", 1103),
  ;

  /**
   * 类型
   */
  private String value;

  /**
   * 名称
   */
  private String name;

  /**
   * 分类
   */
  private String category;

  /**
   * 优先级
   */
  private final Integer priority;

  public static VehicleAlarmCategoryEnum of(final String value) {
    for (VehicleAlarmCategoryEnum itemEnum : VehicleAlarmCategoryEnum.values()) {
      if (itemEnum.value.equals(value)) {
        return itemEnum;
      }
    }
    return null;
  }
}
